{% extends 'dashboard.html' %}
{% load static %}


{% block csses %}
  <link rel="stylesheet" href="{% static "styles/highcharts/highcharts.css" %}"/>

  <link rel="stylesheet" href="{% static "styles/highcharts/flags32-both.css" %}"/>
{% endblock csses %}


{% block content %}
  {% include 'geolocation/00-country-chart--inside-content.html' %}
{% endblock content %}


{% block scripts %}
  {% comment %} vvv 1. used instead of js/highcharts/highcharts.js because we have mp on this page{% endcomment %}
  {% comment %}     2. also in general/homepage.html {% endcomment %}
  <script src="{% static "js/highcharts/highmaps.js" %}"></script>  {% comment %}keep on top{% endcomment %}
  <script src="{% static "js/highcharts/no-data-to-display.js" %}"></script>
  <script src="{% static "js/highcharts/data.js" %}"></script>
  <script src="{% static "js/highcharts/series-label.js" %}"></script>  {% comment %}to show labels (e.g. Warning or Critical) on each line of line graph{% endcomment %}
  <script src="{% static "js/highcharts/drilldown.js" %}"></script>
  <script src="{% static "js/highcharts/exporting.js" %}"></script>  {% comment %}keep above export-data.js{% endcomment %}
  <script src="{% static "js/highcharts/offline-exporting.js" %}"></script>
  <script src="{% static "js/highcharts/export-data.js" %}"></script>

  {# for lollipop #}
  <script src="{% static "js/highcharts/highcharts-more.js" %}"></script>
  <script src="{% static "js/highcharts/dumbbell.js" %}"></script>
  <script src="{% static "js/highcharts/lollipop.js" %}"></script>

  <script type="module" src="{% static "js/graphs.js" %}"></script>
{% endblock scripts %}
