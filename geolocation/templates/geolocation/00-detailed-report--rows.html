{% comment %}
  __USING_STRIPTAGS__
  we use ...|striptags in this file
  to strip/remove html tags from variables
  in action_menu, in *-url, etc.:
  <mark>***********</mark> -> ***********
{% endcomment %}


{% load tags-filters %}


{% for d_r in db_rows %}
  <tr
    id="htmx_fade_in"

    {% if not refresh_to_show and forloop.last and not db_rows|length < limit_to_show %}
      hx-include="#dropdown_form"
      hx-get="{% url "geolocation-detailed-report-url" geo_mode %}"
      hx-vals='{"page": "{{page_number|add:"1"}}"}'
      hx-trigger="intersect once"
      hx-swap="afterend"
      hx-indicator="#{{id_for_htmx_indicator}}"
    {% endif %}
  >
    {% if geo_mode == "domain" %}
      <td>{{ d_r.0  }}</td>

      {# Domain #}
      <td>
        <div class="d-flex justify-content-between align-items-center">
          <div>{{ d_r.1 }}</div>
          {% if d_r.1 %}
            {% with d_r.1|striptags as striptagged %}
              <div class="ms-2 action_menu">
                <a href="javascript:void(0);" data-bs-toggle="dropdown" class="btn btn-icon btn-sm btn-link text-muted text-decoration-none">
                  <i class="ti ti-dots-vertical"></i>
                </a>
                <ul class="dropdown-menu">
                  <li><a target="_blank" class="dropdown-item" href="{% url "general-lookup-url" %}?look-up={{striptagged}}">Lookup</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "malicious-detailed-report-url" "domain" %}?Domain={{striptagged}}">Malicious - Domain</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "geolocation-detailed-report-url" "domain" %}?Domain={{striptagged}}">GeoLocation - Domain</a></li>
                </ul>
              </div>
            {% endwith %}
          {% endif %}
        </div>
      </td>

      <td>{{ d_r.2  }}</td>
      <td>{{ d_r.3  }}</td>
      <td>{{ d_r.4  }}</td>
      <td>{{ d_r.5  }}</td>
      <td>{{ d_r.6  }}</td>
      <td>{{ d_r.7  }}</td>
      <td>{{ d_r.8  }}</td>
      <td>{{ d_r.9  }}</td>
      <td>{{ d_r.10 }}</td>
      
      {# IP #}
      <td>
        <div class="d-flex justify-content-between align-items-center">
          <div>{{ d_r.11 }}</div>
          {% if d_r.11 %}
            {% with d_r.11|striptags as striptagged %}
              <div class="ms-2 action_menu">
                <a href="javascript:void(0);" data-bs-toggle="dropdown" class="btn btn-icon btn-sm btn-link text-muted text-decoration-none">
                  <i class="ti ti-dots-vertical"></i>
                </a>
                <ul class="dropdown-menu">
                  <li><a target="_blank" class="dropdown-item" href="{% url "general-lookup-url" %}?look-up={{striptagged}}">Lookup</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "malicious-detailed-report-url" "ip" %}?IP={{striptagged}}">Malicious - IP</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "geolocation-detailed-report-url" "ip" %}?IP={{striptagged}}">GeoLocation - IP</a></li>
                </ul>
              </div>
            {% endwith %}
          {% endif %}
        </div>
      </td>

    {% elif geo_mode == "ip" %}
      <td>{{ d_r.0  }}</td>

      {# IP #}
      <td>
        <div class="d-flex justify-content-between align-items-center">
          <div>{{ d_r.1 }}</div>
          {% if d_r.1 %}
            {% with d_r.1|striptags as striptagged %}
              <div class="ms-2 action_menu">
                <a href="javascript:void(0);" data-bs-toggle="dropdown" class="btn btn-icon btn-sm btn-link text-muted text-decoration-none">
                  <i class="ti ti-dots-vertical"></i>
                </a>
                <ul class="dropdown-menu">
                  <li><a target="_blank" class="dropdown-item" href="{% url "general-lookup-url" %}?look-up={{striptagged}}">Lookup</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "malicious-detailed-report-url" "ip" %}?IP={{striptagged}}">Malicious - IP</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "geolocation-detailed-report-url" "ip" %}?IP={{striptagged}}">GeoLocation - IP</a></li>
                </ul>
              </div>
            {% endwith %}
          {% endif %}
        </div>
      </td>

      <td>{{ d_r.2  }}</td>
      <td>{{ d_r.3  }}</td>
      <td>{{ d_r.4  }}</td>
      <td>{{ d_r.5  }}</td>
      <td>{{ d_r.6  }}</td>
      <td>{{ d_r.7  }}</td>
      <td>{{ d_r.8  }}</td>
      <td>{{ d_r.9  }}</td>
      <td>{{ d_r.10 }}</td>
      <td>{{ d_r.11 }}</td>
      <td>{{ d_r.12 }}</td>
      <td>{{ d_r.13 }}</td>

      {# Domain #}
      <td>
        <div class="d-flex justify-content-between align-items-center">
          <div>{{ d_r.14 }}</div>
          {% if d_r.14 %}
            {% with d_r.14|striptags as striptagged %}
              <div class="ms-2 action_menu">
                <a href="javascript:void(0);" data-bs-toggle="dropdown" class="btn btn-icon btn-sm btn-link text-muted text-decoration-none">
                  <i class="ti ti-dots-vertical"></i>
                </a>
                <ul class="dropdown-menu">
                  <li><a target="_blank" class="dropdown-item" href="{% url "general-lookup-url" %}?look-up={{striptagged}}">Lookup</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "malicious-detailed-report-url" "domain" %}?Domain={{striptagged}}">Malicious - Domain</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "geolocation-detailed-report-url" "domain" %}?Domain={{striptagged}}">GeoLocation - Domain</a></li>
                </ul>
              </div>
            {% endwith %}
          {% endif %}
        </div>
      </td>

      <td>{{ d_r.15 }}</td>
      <td>{{ d_r.16 }}</td>
      <td>{{ d_r.17 }}</td>
      <td>{{ d_r.18 }}</td>
    {% endif %}
  </tr>

  {% if not refresh_to_show and forloop.last and db_rows|length < limit_to_show %}
    {% include '00-no-results-found.html' with page_number=page_number|add:"1" %}
  {% endif %}
{% empty %}
  {% comment %}first time page loads{% endcomment %}

  {% comment %}
    to make sure this block fires only on first hx request.
    without this, when rows is empty (e.g. because the date by dates section has not been parsed yet)
    it keeps generating this block and, in turn, sending hx requests infinitely
  {% endcomment %}
  {% if not refresh_to_show and page_number == 0 %}

    {% comment %}
      this is a tr with empty cells and its purpose is to send the initial hx query
      so py-0 and d-none are added to make it not visible
    {% endcomment %}
    <tr
      id="htmx_fade_in"
      class="py-0"

      hx-include="#dropdown_form"
      hx-get="{% url "geolocation-detailed-report-url" geo_mode %}"
      hx-vals='{"page": "{{page_number|add:"1"}}"}'
      hx-trigger="intersect once"
      hx-swap="afterend"
      hx-indicator="#{{id_for_htmx_indicator}}"

      data-to_be_deleted_tr="true"
    >
      <td class="d-none" colspan="{{db_headers|length}}"></td>
    </tr>

  {% else %}
    {% include '00-no-results-found.html' %}
  {% endif %}

{% endfor %}


{# update latest-id cookie #}
{% include '00-set-latest-id-cookie.html' %}
