from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse  # HttpResponse
from django.shortcuts import render

from functools import lru_cache
from os import path
from re import sub

from MySQLdb import connect
from rahavard import (
    calculate_offset,
    clear_messages,
    comes_from_htmx,
    create_id_for_htmx_indicator,
    get_percent,
)

from base.utils_classes import (
    GeoLocationConfig,
    MYSQLConfig,
)

from base.utils_constants import (
    LOGICAL_OPERATORS,
    LRU_CACHE_MAXSIZE,
    SEARCH_SIGNS,
)

from base.utils import (
    create_name_of_database,
    create_warning_message,
    get_field_headers_and_values,
    get_name_of_function,
    get_sum_of_values,
    get_to_shows,
    highlight_searched_items_in_db_rows,
    paginate,
)

from base.handlers import (
    bad_request,
)

from .all import (
    # all_charts__domain,
    # all_charts__ip,
    all_tables__domain,
    all_tables__ip,
)


APP_TITLE = GeoLocationConfig.TITLE.value
APP_SLUG  = GeoLocationConfig.SLUG.value


@login_required
def detailed_report(request, geo_mode):
    chosensensorname, \
    from_dropdown,    \
    limit_to_show,    \
    logical_operator, \
    match_case,       \
    page_number = get_to_shows(
        request,
        'chosen-sensor-name',
        'from-dropdown',
        'limit',
        'logical-operator',
        'match-case',
        'page',

        pick_lowest=True,
    )

    geo_mode = geo_mode.lower()

    if geo_mode == 'domain':
        main_title              = f'{APP_TITLE} - Detailed Report - Domain'
        db_headers              = GeoLocationConfig.DB_HEADERS__DOMAIN.value
        db_headers_with_indexes = GeoLocationConfig.DB_HEADERS_WITH_INDEXES__DOMAIN.value
    elif geo_mode == 'ip':
        main_title              = f'{APP_TITLE} - Detailed Report - IP'
        db_headers              = GeoLocationConfig.DB_HEADERS__IP.value
        db_headers_with_indexes = GeoLocationConfig.DB_HEADERS_WITH_INDEXES__IP.value
    else:
        return bad_request(request, error_msg=f'Invalid Mode: {geo_mode}')

    src_dir = GeoLocationConfig.get_logs_parsed_dir()

    if not path.exists(src_dir):
        return bad_request(request, error_msg=f'No Databases for {APP_TITLE}')

    database_name = create_name_of_database(APP_SLUG)

    field_headers_and_values = get_field_headers_and_values(request, db_headers)

    if comes_from_htmx(request):
        db_rows = api__get_db_rows_for_detailed_report(
            request,
            database_name,
            logical_operator,
            field_headers_and_values,
            limit_to_show,
            match_case,
            page_number,

            geo_mode,
        )

        if from_dropdown:
            html_file = '00-detailed-report--inside-content.html'
        else:
            html_file = '00-detailed-report--rows.html'

        db_rows = highlight_searched_items_in_db_rows(
            db_rows,
            field_headers_and_values,
            match_case,
            db_headers_with_indexes,
            multi_day_report_allowed=False,
        )

        return render(
            request,
            f'{APP_SLUG}/{html_file}',
            context={
                'page_has_scrollable_tables': True,

                'db_headers': db_headers,
                'db_rows': db_rows,
                'from_dropdown': from_dropdown,
                'geo_mode': geo_mode,
                'id_for_htmx_indicator': create_id_for_htmx_indicator('detailed_report'),
                'limit_to_show': limit_to_show,
                'logical_operator': logical_operator,
                'match_case': match_case,
                'page_number': page_number,

                'chosensensorname': chosensensorname,
        })

    return render(
        request,
        f'{APP_SLUG}/detailed-report.html',
        context={
            'main_title': main_title,
            'is_detailed_report': True,
            'multi_day_report_allowed': False,
            'page_has_scrollable_tables': True,

            'db_headers': db_headers,
            'field_headers_and_values': field_headers_and_values,
            'geo_mode': geo_mode,
            'limit_to_show': limit_to_show,
            'logical_operator': logical_operator,
            'match_case': match_case,
            'page_number': 0,  ## set to 0 to prevent page input from showing
            'recents_to_show': None,  ## to prevent recents dropdown from showing

            'chosensensorname': chosensensorname,
        },
    )

@login_required
def tabular_overview(request, geo_mode):
    chosensensorname, \
    from_dropdown,    \
    limit_to_show,    \
    overview,         \
    page_number,      \
    table_slug = get_to_shows(
        request,
        'chosen-sensor-name',
        'from-dropdown',
        'limit',
        'overview',
        'page',
        'table-slug',
    )

    geo_mode = geo_mode.lower()

    if geo_mode == 'domain':
        main_title = f'{APP_TITLE} - Tabular Overview - Domain'
        all_tables = all_tables__domain
    elif geo_mode == 'ip':
        main_title = f'{APP_TITLE} - Tabular Overview - IP'
        all_tables = all_tables__ip
    else:
        return bad_request(request, error_msg=f'Invalid Mode: {geo_mode}')

    src_dir = GeoLocationConfig.get_logs_parsed_dir()

    if not path.exists(src_dir):
        return bad_request(request, error_msg=f'No Databases for {APP_TITLE}')

    if comes_from_htmx(request):
        overview_total_no = 0
        overview_sum      = 0
        overview_maximum  = 0
        overview_minimum  = 0
        overview_average  = 0

        if from_dropdown:
            rows = []
            page_number = 0  ## set to 0 to prevent page input from showing
            id_for_htmx_indicator = ''
            html_file = '00-tabular-overview--inside-content.html'
        else:
            table_slug = table_slug.lower()

            item_tops = api__get_rows_from_toptable(
                request,
                slug=table_slug,  ## chart_slug or table_slug

                geo_mode=geo_mode,
            )
            ## main = {'*************': 2172242, '************': 225469, ...}

            item_tops_total = get_sum_of_values(item_tops)

            rows = []

            if overview:
                try:
                    dic_values        = item_tops.values()
                    overview_total_no = len(item_tops)
                    overview_sum      = item_tops_total
                    overview_maximum  = max(dic_values)
                    overview_minimum  = min(dic_values)
                    overview_average  = int(overview_sum / overview_total_no)
                except:
                    pass
                html_file = '00-overview--card-footer.html'
            else:
                item_tops = paginate(item_tops, limit_to_show, page_number)

                id_ = calculate_offset(page_number, limit_to_show)
                for item, count in item_tops.items():
                    id_ += 1
                    percent = get_percent(smaller_number=count, total_number=item_tops_total)
                    rows.append([id_, item, count, percent])

                html_file = '00-tabular-overview--rows.html'

            id_for_htmx_indicator = create_id_for_htmx_indicator(table_slug)

        return render(
            request,
            f'{APP_SLUG}/{html_file}',
            context={
                'page_has_scrollable_tables': True,
                'multi_day_report_allowed': False,

                'from_dropdown': from_dropdown,
                'geo_mode': geo_mode,
                'limit_to_show': limit_to_show,
                'page_number': page_number,
                'recents_to_show': None,  ## to prevent recents dropdown from showing

                'all_tables': all_tables,
                'table_slug': table_slug,
                'id_for_htmx_indicator': id_for_htmx_indicator,
                'rows': rows,

                'overview_total_no': overview_total_no,
                'overview_sum': overview_sum,
                'overview_maximum': overview_maximum,
                'overview_minimum': overview_minimum,
                'overview_average': overview_average,
                'overview_title': f'{main_title}: {get_column_name_and_table_name_from_slug(table_slug, geo_mode)[0]}',
                # 'overview_object': f'Sensor: {chosensensorname}',

                'chosensensorname': chosensensorname,
        })

    return render(
        request,
        f'{APP_SLUG}/tabular-overview.html',
        context={
            'page_has_scrollable_tables': True,
            'multi_day_report_allowed': False,
            'main_title': main_title,

            'geo_mode': geo_mode,
            'limit_to_show': limit_to_show,
            'recents_to_show': None,  ## to prevent recents dropdown from showing
            'page_number': 0,  ## set to 0 to prevent page input from showing

            'all_tables': all_tables,

            'chosensensorname': chosensensorname,
        },
    )

@login_required
def country_chart(request, geo_mode):
    chart_slug,       \
    chosensensorname, \
    from_dropdown = get_to_shows(
        request,
        'chart-slug',
        'chosen-sensor-name',
        'from-dropdown',
    )

    geo_mode = geo_mode.lower()

    if geo_mode == 'domain':
        main_title = f'{APP_TITLE} - Country Chart - Domain'
    elif geo_mode == 'ip':
        main_title = f'{APP_TITLE} - Country Chart - IP'
    else:
        return bad_request(request, error_msg=f'Invalid Mode: {geo_mode}')

    src_dir = GeoLocationConfig.get_logs_parsed_dir()

    if not path.exists(src_dir):
        return bad_request(request, error_msg=f'No Databases for {APP_TITLE}')

    if chart_slug:
        detailed_main = api__get_rows_from_toptable(
            request,
            slug=chart_slug,  ## chart_slug or table_slug

            geo_mode=geo_mode,
        )
        ## detailed_main = {'United States': 8046, 'Singapore': 2801, 'China': 859, ...}

        mapdata = []

        for country, count in detailed_main.items():
            alphas = GeoLocationConfig.COUNTRY_CODES_DICT.value.get(country, [])  ## ['IR', 'IRN']
            if not alphas:
                continue

            mapdata.append({
                'name': country,
                'value': count,
                'code': alphas[0],  ## IR
                'code3': alphas[1],  ## IRN
            })

        graph_title, _ = get_column_name_and_table_name_from_slug(chart_slug, geo_mode)
        if   geo_mode == 'domain': graph_title = f'{APP_TITLE} - {graph_title} - Domain'
        elif geo_mode == 'ip':     graph_title = f'{APP_TITLE} - {graph_title} - IP'
        return JsonResponse(
            data={
                'dates': list(detailed_main.keys()),
                'counts': list(detailed_main.values()),
                'mapdata': mapdata,

                ## will be used to set max number on map's colorAxis
                'coloraxis_max': max(detailed_main.values()),  ## 9025

                ## to be used in graph's subtitle
                'graph_title': graph_title,
                'module_name': None,  ## None because geolocation is module-independent
                'chosensensorname': None,  ## None because geolocation is sensor-independent
                'chosensensorip': None,  ## None because geolocation is sensor-independent
            })

    if from_dropdown:
        html_file = '00-country-chart--inside-content.html'
    else:
        html_file = 'country-chart.html'

    return render(
        request,
        f'{APP_SLUG}/{html_file}',
        context={
            'multi_day_report_allowed': False,
            'main_title': main_title,

            'from_dropdown': from_dropdown,
            'geo_mode': geo_mode,
            'recents_to_show': None,  ## to prevent recents dropdown from showing
            'top_to_show': 0,  ## do NOT comment. country chart needs this

            'chosensensorname': chosensensorname,
        },
    )

## --------------------------------

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def get_column_name_and_table_name_from_slug(slug: str, geo_mode: str) -> tuple[str | None, str | None]:
    '''
    Gets a URL-friendly slug and returns a tuple containing the human-readable
    column name and the corresponding database table name.

    Args:
        slug (str): URL-friendly identifier (e.g., 'country', 'city)
        geo_mode (str): Geo Mode (e.g., 'domain', 'ip')

    Returns:
        tuple: (column_name, table_name) e.g., ('Country', 'countrytoptable__domain')
               Returns (None, None) if slug is not recognized or geo_mode is invalid
    Examples:
        >>> get_column_name_and_table_name_from_slug('country', 'domain')
        ('Country', 'countrytoptable__domain')

        >>> get_column_name_and_table_name_from_slug('city', 'ip')
        ('City', 'citytoptable__ip')

        >>> get_column_name_and_table_name_from_slug('unknown-slug', 'domain')
        (None, None)
    '''
    ## __HAS_TEST__

    if geo_mode == 'domain':
        return {
            ## slug          column_name      table_name
            'country':      ('Country',      'countrytoptable__domain'),
            'city':         ('City',         'citytoptable__domain'),
            'timezone':     ('Timezone',     'timezonetoptable__domain'),
            'isp':          ('ISP',          'isptoptable__domain'),
            'organization': ('Organization', 'organizationtoptable__domain'),
            'ip':           ('IP',           'iptoptable__domain'),
        }.get(slug, (None, None))

    if geo_mode == 'ip':
        return {
            ## slug          column_name      table_name
            'type':         ('Type',         'typetoptable__ip',),
            'continent':    ('Continent',    'continenttoptable__ip',),
            'country':      ('Country',      'countrytoptable__ip',),
            'city':         ('City',         'citytoptable__ip',),
            'organization': ('Organization', 'organizationtoptable__ip',),
            'isp':          ('ISP',          'isptoptable__ip',),
            'domain':       ('Domain',       'domaintoptable__ip',),
            'timezone':     ('Timezone',     'timezonetoptable__ip',),
        }.get(slug, (None, None))

    return (None, None)

## NOTE commented cache decorator unlike other */views.py files:
##   1. in this app, we don't pass ymd to this funtion
##   2. using cache caused unexpected behavior
##      in tabular overview and chart pages
# @lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def _api__get_rows_from_toptable__cached(slug, geo_mode):
    column_name, \
    table_name = get_column_name_and_table_name_from_slug(slug, geo_mode)

    if not column_name or not table_name:
        return {}

    try:
        database_name = create_name_of_database(APP_SLUG)
        with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:
                cur.execute(f'SELECT `{column_name}`, Count FROM {table_name};')
                day_info = dict(cur)  # .fetchall()

        return day_info
    except Exception as exc_obj:
        return {'is_exc': True, 'exc_obj': exc_obj}

def api__get_rows_from_toptable(request, slug, geo_mode):
    '''slug is chart_slug or table_slug'''

    day_info = _api__get_rows_from_toptable__cached(slug, geo_mode)

    if day_info.get('is_exc', False):
        warning_message = create_warning_message(request, APP_TITLE, '', get_name_of_function(), day_info.get('exc_obj'))
        messages.add_message(request, messages.WARNING, warning_message)
        return {}

    return day_info

def api__get_db_rows_for_detailed_report(
    request,
    database_name,
    field_headers_and_values,
    limit_to_show,
    logical_operator,
    match_case,
    page_number,

    geo_mode,
):
    ## __BY_AI__ optimized by copilot

    db_offset         = calculate_offset(page_number, limit_to_show)
    id_condition      = ''
    order_by_statemnt = ''

    try:
        with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:

                ## search in specific column(s)
                if field_headers_and_values:
                    where_condition = ''
                    search_values = []

                    if match_case:
                        case_sensitive = 'BINARY'  ## is case sensitive
                    else:
                        case_sensitive = ''

                    for field_header, field_values in field_headers_and_values.items():
                        field_condition = ''
                        for value in field_values:
                            startswith_asterisk = value.startswith(SEARCH_SIGNS.asterisk)
                            endswith_asterisk   = value.endswith(SEARCH_SIGNS.asterisk)
                            startswith_caret    = value.startswith(SEARCH_SIGNS.caret)
                            endswith_dollar     = value.endswith(SEARCH_SIGNS.dollar)

                            ## add conditions ======================
                            if any([
                                not startswith_asterisk and not endswith_asterisk,  ## guard
                                startswith_caret and endswith_dollar,  ## ^guard$
                                field_header == 'ID',
                            ]):
                                equal_like = '='
                            else:
                                equal_like = 'LIKE'

                            field_condition += f'`{field_header}` {equal_like} {case_sensitive} %s {LOGICAL_OPERATORS.for_field} '

                            ## add placeholders ======================

                            ## remove leading/trailing *^$
                            value = value.strip(f'{SEARCH_SIGNS.asterisk}{SEARCH_SIGNS.caret}{SEARCH_SIGNS.dollar}')

                            if equal_like == '=':
                                search_values.append(value)
                            else:
                                ## use LIKE together with %s (https://stackoverflow.com/a/9244646/)
                                if startswith_asterisk or endswith_asterisk:
                                    if startswith_asterisk and endswith_asterisk:
                                        value_ = f'%{value}%'
                                    elif startswith_asterisk and not endswith_asterisk:
                                        value_ = f'%{value}'
                                    elif not startswith_asterisk and endswith_asterisk:
                                        value_ = f'{value}%'
                                else:
                                    value_ = f'%{value}%'

                                search_values.append(value_)

                        if field_condition:
                            field_condition = sub(f' {LOGICAL_OPERATORS.for_field} $', '', field_condition).strip()
                            ## '--> "Source Port" LIKE ? AND "Description" LIKE ?

                            if where_condition:
                                where_condition += f' {logical_operator} ({field_condition})'
                            else:
                                where_condition = f'({field_condition})'


                    if where_condition:
                        where_condition = sub(f' {logical_operator} $', '', where_condition).strip()
                        ## '--> ("Description" LIKE ? OR "Description" LIKE ?) AND ("Source IP" LIKE ? OR "Source IP" LIKE ?)


                    cur.execute(f'''
                        {GeoLocationConfig.get_select_statement(geo_mode=geo_mode)}
                        WHERE ({where_condition})
                        {id_condition}
                        {order_by_statemnt}

                        LIMIT %s
                        OFFSET %s
                    ;''', tuple(search_values + [limit_to_show, db_offset]))

                else:
                    cur.execute(f'''
                        {GeoLocationConfig.get_select_statement(geo_mode=geo_mode)}
                        {id_condition}
                        {order_by_statemnt}

                        LIMIT %s
                        OFFSET %s
                    ;''', tuple([limit_to_show, db_offset]))

                db_rows = cur.fetchall()

    except Exception as exc:
        clear_messages(request)
        warning_message = create_warning_message(request, APP_TITLE, '', get_name_of_function(), exc)
        messages.warning(request, warning_message)
        db_rows = []

    return db_rows
