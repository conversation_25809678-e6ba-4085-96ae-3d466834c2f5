from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.shortcuts import render

from collections import defaultdict
from os import path
from re import sub

from MySQLdb import connect
from natsort import natsorted
from rahavard import (
    calculate_offset,
    clear_messages,
    comes_from_htmx,
    create_id_for_htmx_indicator,
    sort_dict,
)

from base.utils_classes import (
    DHCPConfig,
    DNSConfig,
    FilterLogConfig,
    MaliciousConfig,
    MYSQLConfig,
    SnortConfig,
    SquidConfig,
)

from base.utils_constants import (
    LOGICAL_OPERATORS,
    SEARCH_SIGNS,
)

from base.utils import (
    create_date_range,
    create_name_of_database,
    create_warning_message,
    get_field_headers_and_values,
    get_name_of_function,
    get_parsed_dirs,
    get_rts_dts_dets,
    get_to_shows,
    highlight_searched_items_in_db_rows,
    list_of_tuples_to_list,
)

from base.utils_extra import get_ip_of_chosen_sensor_name

from base.handlers import (
    bad_request,
)

from dhcp.views      import api__get_rows_from_toptable as api__get_rows_from_toptable___dhcp
from dns.views       import api__get_rows_from_toptable as api__get_rows_from_toptable___dns
from filterlog.views import api__get_rows_from_toptable as api__get_rows_from_toptable___filterlog
from snort.views     import api__get_rows_from_toptable as api__get_rows_from_toptable___snort
from squid.views     import api__get_rows_from_toptable as api__get_rows_from_toptable___squid


APP_TITLE = MaliciousConfig.TITLE.value
APP_SLUG  = MaliciousConfig.SLUG.value


@login_required
def detailed_report(request, mal_mode):
    chosensensorname, \
    from_dropdown,    \
    limit_to_show,    \
    logical_operator, \
    match_case,       \
    page_number = get_to_shows(
        request,
        'chosen-sensor-name',
        'from-dropdown',
        'limit',
        'logical-operator',
        'match-case',
        'page',

        pick_lowest=True,
    )

    mal_mode = mal_mode.lower()

    if mal_mode == 'domain':
        main_title              = f'{APP_TITLE} - Detailed Report - Domain'
        db_headers              = MaliciousConfig.DB_HEADERS__DOMAIN.value
        db_headers_with_indexes = MaliciousConfig.DB_HEADERS_WITH_INDEXES__DOMAIN.value
    elif mal_mode == 'ip':
        main_title              = f'{APP_TITLE} - Detailed Report - IP'
        db_headers              = MaliciousConfig.DB_HEADERS__IP.value
        db_headers_with_indexes = MaliciousConfig.DB_HEADERS_WITH_INDEXES__IP.value
    else:
        return bad_request(request, error_msg=f'Invalid Mode: {mal_mode}')

    src_dir = MaliciousConfig.get_logs_parsed_dir()

    if not path.exists(src_dir):
        return bad_request(request, error_msg=f'No Databases for {APP_TITLE}')

    database_name = create_name_of_database(APP_SLUG)

    field_headers_and_values = get_field_headers_and_values(request, db_headers)

    if comes_from_htmx(request):
        db_rows = api__get_db_rows_for_detailed_report(
            request,
            database_name,
            field_headers_and_values,
            limit_to_show,
            logical_operator,
            match_case,
            page_number,

            mal_mode,
        )

        if from_dropdown:
            html_file = '00-detailed-report--inside-content.html'
        else:
            html_file = '00-detailed-report--rows.html'

        db_rows = highlight_searched_items_in_db_rows(
            db_rows,
            field_headers_and_values,
            match_case,
            db_headers_with_indexes,
            multi_day_report_allowed=False,
        )

        return render(
            request,
            f'{APP_SLUG}/{html_file}',
            context={
                'page_has_scrollable_tables': True,

                'db_headers': db_headers,
                'db_rows': db_rows,
                'from_dropdown': from_dropdown,
                'mal_mode': mal_mode,
                'id_for_htmx_indicator': create_id_for_htmx_indicator('detailed_report'),
                'limit_to_show': limit_to_show,
                'logical_operator': logical_operator,
                'match_case': match_case,
                'page_number': page_number,

                'chosensensorname': chosensensorname,
        })

    return render(
        request,
        f'{APP_SLUG}/detailed-report.html',
        context={
            'main_title': main_title,
            'is_detailed_report': True,
            'multi_day_report_allowed': False,
            'page_has_scrollable_tables': True,

            'db_headers': db_headers,
            'field_headers_and_values': field_headers_and_values,
            'mal_mode': mal_mode,
            'limit_to_show': limit_to_show,
            'logical_operator': logical_operator,
            'match_case': match_case,
            'page_number': 0,  ## set to 0 to prevent page input from showing
            'recents_to_show': None,  ## to prevent recents dropdown from showing

            'chosensensorname': chosensensorname,
        },
    )

@login_required
def charts(request):
    app_slug,         \
    chosensensorname, \
    date_end_to_show, \
    date_to_show,     \
    from_dropdown,    \
    recent_to_show = get_to_shows(
        request,
        'app-slug',
        'chosen-sensor-name',
        'date-end',
        'date',
        'from-dropdown',
        'recent',
    )

    main_title = f'{APP_TITLE} - Charts'

    ## src_dir = ...  ## moved to JUMP_1

    recent_to_show, \
    date_to_show, \
    date_end_to_show = get_rts_dts_dets(recent_to_show, date_to_show, date_end_to_show)


    if app_slug:
        malicious_database_name = create_name_of_database(MaliciousConfig.SLUG.value)

        date_range = create_date_range(date_to_show, date_end_to_show)

        if app_slug == DHCPConfig.SLUG.value:
            app_title              = DHCPConfig.TITLE.value
            top_function           = api__get_rows_from_toptable___dhcp
            chart_slugs_for_domain = []
            chart_slugs_for_ip     = ['source-ip']

            object_name = None

            module_name_      = None  ## None because it is module-independent
            chosensensorname_ = None  ## None because it is sensor-independent
            chosensensorip_   = None  ## None because it is sensor-independent

        elif app_slug == DNSConfig.SLUG.value:
            app_title              = DNSConfig.TITLE.value
            top_function           = api__get_rows_from_toptable___dns
            chart_slugs_for_domain = ['question-name']
            chart_slugs_for_ip     = ['source-ip']

            object_name = None

            module_name_      = None  ## None because it is module-independent
            chosensensorname_ = None  ## None because it is sensor-independent
            chosensensorip_   = None  ## None because it is sensor-independent

        elif app_slug == FilterLogConfig.SLUG.value:
            app_title              = FilterLogConfig.TITLE.value
            top_function           = api__get_rows_from_toptable___filterlog
            chart_slugs_for_domain = []
            chart_slugs_for_ip     = ['source-ip', 'destination-ip']

            object_name = chosensensorname

            module_name_      = None  ## None because it is module-independent
            chosensensorname_ = chosensensorname
            chosensensorip_   = get_ip_of_chosen_sensor_name(name=chosensensorname)

        elif app_slug == SnortConfig.SLUG.value:
            app_title              = SnortConfig.TITLE.value
            top_function           = api__get_rows_from_toptable___snort
            chart_slugs_for_domain = []
            chart_slugs_for_ip     = ['source-ip', 'destination-ip']

            object_name = chosensensorname

            module_name_      = SnortConfig.TITLE.value
            chosensensorname_ = chosensensorname
            chosensensorip_   = get_ip_of_chosen_sensor_name(name=chosensensorname)

        elif app_slug == SquidConfig.SLUG.value:
            app_title              = SquidConfig.TITLE.value
            top_function           = api__get_rows_from_toptable___squid
            chart_slugs_for_domain = ['url']
            chart_slugs_for_ip     = ['source-ip', 'destination-ip']

            object_name = chosensensorname

            module_name_      = None  ## None because it is module-independent
            chosensensorname_ = chosensensorname
            chosensensorip_   = get_ip_of_chosen_sensor_name(name=chosensensorname)

        else:
            return JsonResponse({})

        if chart_slugs_for_domain:
            counts_of_mal_domains__dict = defaultdict(int)

            for csfd in chart_slugs_for_domain:
                for d_r in date_range:
                    day_dict = top_function(
                        request,
                        ymd=d_r,
                        slug=csfd,  ## chart_slug or table_slug

                        object_name=object_name,
                    )

                    if day_dict:
                        try:
                            with connect(**MYSQLConfig.R_USER_CREDS.value, database=malicious_database_name) as conn:
                                with conn.cursor() as cur:
                                    ## __PLACEHOLDERS_FOR_WHERE__
                                    placeholders = ', '.join(['%s'] * len(day_dict.keys()))

                                    cur.execute(f'''
                                        SELECT Domain FROM {MaliciousConfig.get_table_name(mal_mode="domain", is_parent=False)}
                                        WHERE (Domain IN ({placeholders}))
                                    ;''', tuple(day_dict.keys()))
                                    counts_of_mal_domains__dict[d_r] += len(list_of_tuples_to_list(cur.fetchall()))
                        except Exception:
                            counts_of_mal_domains__dict[d_r] += 0
                    else:
                        counts_of_mal_domains__dict[d_r] += 0

            counts_of_mal_domains__dict = sort_dict(counts_of_mal_domains__dict, based_on='key', reverse=False)

            ## dict -> list of dict values
            counts_of_mal_domains = list(counts_of_mal_domains__dict.values())
        else:
            counts_of_mal_domains = [0 for _ in date_range]
        ## counts_of_mal_domains = [198, 21, 5373, ...]


        if chart_slugs_for_ip:
            counts_of_mal_ips__dict = defaultdict(int)

            for csfi in chart_slugs_for_ip:
                for d_r in date_range:
                    day_dict = top_function(
                        request,
                        ymd=d_r,
                        slug=csfi,  ## chart_slug or table_slug

                        object_name=object_name,
                    )

                    if day_dict:
                        try:
                            with connect(**MYSQLConfig.R_USER_CREDS.value, database=malicious_database_name) as conn:
                                with conn.cursor() as cur:
                                    ## __PLACEHOLDERS_FOR_WHERE__
                                    placeholders = ', '.join(['%s'] * len(day_dict.keys()))
                                    cur.execute(f'''
                                        SELECT IP FROM {MaliciousConfig.get_table_name(mal_mode="ip", is_parent=False)}
                                        WHERE (IP IN ({placeholders}))
                                    ;''', tuple(day_dict.keys()))
                                    counts_of_mal_ips__dict[d_r] += len(list_of_tuples_to_list(cur.fetchall()))
                        except Exception:
                            counts_of_mal_ips__dict[d_r] += 0
                    else:
                        counts_of_mal_ips__dict[d_r] += 0

            counts_of_mal_ips__dict = sort_dict(counts_of_mal_ips__dict, based_on='key', reverse=False)

            ## dict -> list of dict values
            counts_of_mal_ips = list(counts_of_mal_ips__dict.values())
        else:
            counts_of_mal_ips = [0 for _ in date_range]


        return JsonResponse({
            'dates':             date_range,
            'malicious_domains': counts_of_mal_domains,
            'malicious_ips':     counts_of_mal_ips,

            ## to be used in graph's subtitle
            'date_to_show': date_to_show,
            'date_end_to_show': date_end_to_show,
            'graph_title': f'{MaliciousConfig.TITLE.value} - {app_title}',
            'module_name': module_name_,
            'chosensensorname': chosensensorname_,
            'chosensensorip': chosensensorip_,
        })

    ## JUMP_1 get parsed_dirs from multiple apps
    parsed_dirs = []
    ##
    for src_dir in [
        DHCPConfig.get_logs_parsed_dir(),
        DNSConfig.get_logs_parsed_dir(),
        f'{FilterLogConfig.get_logs_parsed_dir()}/{chosensensorname}',
        f'{SnortConfig.get_logs_parsed_dir()}/{chosensensorname}',
        f'{SquidConfig.get_logs_parsed_dir()}/{chosensensorname}',
    ]:
        parsed_dirs.extend(
            get_parsed_dirs(src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]
        )
    ##
    parsed_dirs = natsorted(set(parsed_dirs))

    if not parsed_dirs:
        return bad_request(request, error_msg='No Databases')

    if from_dropdown:
        html_file = '00-charts--inside-content.html'
    else:
        html_file = 'charts.html'

    return render(
        request,
        f'{APP_SLUG}/{html_file}',
        context={
            'multi_day_report_allowed': True,
            'main_title': main_title,

            'app_titles_and_slugs': {
                DHCPConfig.TITLE.value:      DHCPConfig.SLUG.value,
                DNSConfig.TITLE.value:       DNSConfig.SLUG.value,
                FilterLogConfig.TITLE.value: FilterLogConfig.SLUG.value,
                SnortConfig.TITLE.value:     SnortConfig.SLUG.value,
                SquidConfig.TITLE.value:     SquidConfig.SLUG.value,
            },

            'date_end_to_show': date_end_to_show,
            'date_to_show': date_to_show,
            'from_dropdown': from_dropdown,
            'recent_to_show': recent_to_show,

            'chosensensorname': chosensensorname,

            'parsed_dirs': parsed_dirs,
        },
    )

## --------------------------------

def api__get_db_rows_for_detailed_report(
    request,
    database_name,
    field_headers_and_values,
    limit_to_show,
    logical_operator,
    match_case,
    page_number,

    mal_mode,
):
    ## __BY_AI__ optimized by copilot

    db_offset         = calculate_offset(page_number, limit_to_show)
    id_condition      = ''
    order_by_statemnt = ''

    try:
        with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:

                ## search in specific column(s)
                if field_headers_and_values:
                    where_condition = ''
                    search_values = []

                    if match_case:
                        case_sensitive = 'BINARY'  ## is case sensitive
                    else:
                        case_sensitive = ''

                    for field_header, field_values in field_headers_and_values.items():
                        field_condition = ''
                        for value in field_values:
                            startswith_asterisk = value.startswith(SEARCH_SIGNS.asterisk)
                            endswith_asterisk   = value.endswith(SEARCH_SIGNS.asterisk)
                            startswith_caret    = value.startswith(SEARCH_SIGNS.caret)
                            endswith_dollar     = value.endswith(SEARCH_SIGNS.dollar)

                            ## add conditions ======================
                            if any([
                                not startswith_asterisk and not endswith_asterisk,  ## guard
                                startswith_caret and endswith_dollar,  ## ^guard$
                                field_header == 'ID',
                            ]):
                                equal_like = '='
                            else:
                                equal_like = 'LIKE'

                            field_condition += f'`{field_header}` {equal_like} {case_sensitive} %s {LOGICAL_OPERATORS.for_field} '

                            ## add placeholders ======================

                            ## remove leading/trailing *^$
                            value = value.strip(f'{SEARCH_SIGNS.asterisk}{SEARCH_SIGNS.caret}{SEARCH_SIGNS.dollar}')

                            if equal_like == '=':
                                search_values.append(value)
                            else:
                                ## use LIKE together with %s (https://stackoverflow.com/a/9244646/)
                                if startswith_asterisk or endswith_asterisk:
                                    if startswith_asterisk and endswith_asterisk:
                                        value_ = f'%{value}%'
                                    elif startswith_asterisk and not endswith_asterisk:
                                        value_ = f'%{value}'
                                    elif not startswith_asterisk and endswith_asterisk:
                                        value_ = f'{value}%'
                                else:
                                    value_ = f'%{value}%'

                                search_values.append(value_)

                        if field_condition:
                            field_condition = sub(f' {LOGICAL_OPERATORS.for_field} $', '', field_condition).strip()
                            ## '--> "Source Port" LIKE ? AND "Description" LIKE ?

                            if where_condition:
                                where_condition += f' {logical_operator} ({field_condition})'
                            else:
                                where_condition = f'({field_condition})'


                    if where_condition:
                        where_condition = sub(f' {logical_operator} $', '', where_condition).strip()
                        ## '--> ("Description" LIKE ? OR "Description" LIKE ?) AND ("Source IP" LIKE ? OR "Source IP" LIKE ?)


                    cur.execute(f'''
                        {MaliciousConfig.get_select_statement(mal_mode=mal_mode, is_parent=False)}
                        WHERE ({where_condition})
                        {id_condition}
                        {order_by_statemnt}

                        LIMIT %s
                        OFFSET %s
                    ;''', tuple(search_values + [limit_to_show, db_offset]))

                else:
                    cur.execute(f'''
                        {MaliciousConfig.get_select_statement(mal_mode=mal_mode, is_parent=False)}
                        {id_condition}
                        {order_by_statemnt}

                        LIMIT %s
                        OFFSET %s
                    ;''', tuple([limit_to_show, db_offset]))

                db_rows = cur.fetchall()

    except Exception as exc:
        clear_messages(request)
        warning_message = create_warning_message(request, APP_TITLE, '', get_name_of_function(), exc)
        messages.warning(request, warning_message)
        db_rows = []

    return db_rows
