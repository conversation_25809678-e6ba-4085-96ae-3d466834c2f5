{% load static %}
{% load humanize %}
{% load tags-filters %}


<div class="row g-3" id="dropdown_hx_destination">
  {% for app_title, app_slug in app_titles_and_slugs.items %}    
    <div class="col-12">
      {% join_strings "Malicious - " app_title as ttl %}
      {% include '00-chart-malicious-chart.html' with ttl=ttl app_slug=app_slug chart_height="med_height" %}
    </div>
  {% endfor %}
</div>


{% if from_dropdown %}
  {% comment %}
    NOTE 1. ...
         2. ?version=* is added to prevent caching when loading graph.js
            so that a new version of graphs.js is loaded
            when htmx from dropdown is fired
            (https://stackoverflow.com/a/7413243/14388247)
         3. {% now "YmdGis" %} gives 20231220103114
            (https://docs.djangoproject.com/en/5.1/ref/templates/builtins/#std-templatefilter-date)
  {% endcomment %}
  <script type="module" src="{% static "js/graphs.js" %}?version={% now "YmdGis" %}"></script>

  {% include '00-set-cookies.html' %}
  {% include '00-set-dropdowns.html' %}
{% endif %}
