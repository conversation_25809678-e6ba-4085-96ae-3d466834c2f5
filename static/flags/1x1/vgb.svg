<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="flag-icon-css-vg" width="512" height="512">
  <defs>
    <linearGradient id="a">
      <stop offset="0" stop-color="red"/>
      <stop offset="1" stop-color="#ff0"/>
    </linearGradient>
    <linearGradient id="c" x1="103.1" x2="92.5" y1="111.3" y2="107.8" gradientTransform="matrix(.65977 0 0 1.4919 -3.7 -.7)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
    <linearGradient id="d" x1="103.1" x2="92.5" y1="111.3" y2="107.8" gradientTransform="matrix(.65977 0 0 1.4919 -3.7 -.5)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
    <linearGradient id="e" x1="103.1" x2="92.5" y1="111.3" y2="107.8" gradientTransform="matrix(.65977 0 0 1.4919 -4.5 -.3)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
    <linearGradient id="f" x1="103.1" x2="92.5" y1="111.3" y2="107.8" gradientTransform="matrix(.65977 0 0 1.4919 -3.7 0)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
    <linearGradient id="g" x1="103.1" x2="92.5" y1="111.3" y2="107.8" gradientTransform="matrix(.65977 0 0 1.4919 -3.7 .2)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
    <linearGradient id="h" x1="103.1" x2="92.5" y1="111.3" y2="107.8" gradientTransform="matrix(.65977 0 0 1.4919 -3.7 .4)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
    <linearGradient id="i" x1="103.1" x2="92.5" y1="111.3" y2="107.8" gradientTransform="matrix(.65977 0 0 1.4919 -4.5 .4)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
    <linearGradient id="j" x1="103.1" x2="92.5" y1="111.3" y2="107.8" gradientTransform="matrix(.65977 0 0 1.4919 -4.5 .2)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
    <linearGradient id="k" x1="103.1" x2="92.5" y1="111.3" y2="107.8" gradientTransform="matrix(.65977 0 0 1.4919 -4.5 0)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
    <linearGradient id="m" x1="103.1" x2="92.5" y1="111.3" y2="107.8" gradientTransform="matrix(.65977 0 0 1.4919 -4.5 -.5)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
    <linearGradient id="n" x1="103.1" x2="92.5" y1="111.3" y2="107.8" gradientTransform="matrix(.65977 0 0 1.4919 -3.8 -.3)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
    <linearGradient id="l" x1="103.1" x2="92.5" y1="111.3" y2="107.8" gradientTransform="matrix(.85004 0 0 1.9222 723.3 109.7)" gradientUnits="userSpaceOnUse" xlink:href="#a"/>
    <clipPath id="b">
      <path fill-opacity=".7" d="M0 0h512v512H0z"/>
    </clipPath>
  </defs>
  <g clip-path="url(#b)">
    <path fill="#006" d="M0 0h1024v512H0z"/>
    <path fill="#006" fill-rule="evenodd" d="M0 0h512v256H0z"/>
    <g stroke-width="1pt">
      <path fill="#fff" d="M0 0v28.6L454.8 256H512v-28.6L57.2 0H0zm512 0v28.6L57.2 256H0v-28.6L454.8 0H512z"/>
      <path fill="#fff" d="M213.3 0v256h85.4V0h-85.4zM0 85.3v85.4h512V85.3H0z"/>
      <path fill="#c00" d="M0 102.4v51.2h512v-51.2H0zM230.4 0v256h51.2V0h-51.2zM0 256l170.7-85.3h38.1L38.2 256H0zM0 0l170.7 85.3h-38.2L0 19.1V0zm303.2 85.3L473.8 0H512L341.3 85.3h-38.1zM512 256l-170.7-85.3h38.2L512 236.9V256z"/>
    </g>
    <path fill="#fff" fill-rule="evenodd" d="M256.4 193.6l213.7-.8-.4 190.3s7.5 29-89.6 73c34.9-3.6 73-40.9 73-40.9s15.4-19.8 23-8.7c7.5 11.1 14.6 16.6 20.1 21 5.6 4.4 10 16.3 1.6 25s-21.4 9.9-25-.8c-5.5 2.8-39.6 44-109.4 46-71-1.2-109.8-46.4-109.8-46.4s-9.5 15-23 3.2c-13-15.5-3.1-25.4-3.1-25.4s11-6.3 14.2-10.7c5.2-6 6.8-13.9 15.5-13.9 10.3.8 14.3 9.1 14.3 9.1s35.6 37.7 74.1 42.5c-86.8-41.7-90-67.4-89.6-73.8l.4-188.7z"/>
    <path fill="#006129" fill-rule="evenodd" stroke="#000" stroke-width="1.7" d="M261.5 198.8l203.8-1.2v183.1c.4 23.8-39.6 48-102.3 79-64.6-33.4-101.9-53.6-102.3-79.4l.9-181.5z"/>
    <path fill="none" stroke="#f7c600" stroke-width="1.7" d="M286 400.6l12-17.7 12.2 17.7"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M300.5 394a2.4 2.4 0 1 1-4.8 0 2.4 2.4 0 0 1 4.8 0z"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M288.3 413.6h18.6s.4-2.3-1.9-3.8c9.8-1.3 7.2-10 15.4-10.4 1.6.2-4.3 3.6-4.3 3.6s-4.9 3.5-2.6 5.3c1.7 1.4 2.5-.9 2.8-2.6.2-1.7 7.9-2.8 6.8-7.7-1.8-4-12.6 2.7-12.6 2.7H303c-.5-1-2.6-4.4-4.8-4.4-2.6.1-4.4 4.4-4.4 4.4h-17.4s-.6 4.4 8.2 5.3a10.5 10.5 0 0 0 5.3 4c-1.2 1-1.5 2-1.5 3.6z"/>
    <path fill="none" stroke="#000" stroke-width=".8" d="M289.6 409.8h15.6m-22.7-9.1s1.3 7.2 7 9"/>
    <path fill="url(#c)" fill-rule="evenodd" stroke="#000" stroke-width=".6" d="M66.3 170.5c.5-2.1 1.3-2.5 2.2-5.2.1-2.7-2.2-2.4-1.5-4.1 1.2-1.9.6-3.6-1.6-5 .4 2.4-3 4.7-3 6.7s1.8 1.6 1.6 4.6c0 1.8-.5 1.3-.6 3h3z" transform="translate(196.1 180.9) scale(1.2884)"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M300.3 383.4a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0z"/>
    <path fill="none" stroke="#f7c600" stroke-width="1.7" d="M285.4 364.2l12-17.6 12 17.6"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M299.8 357.6a2.4 2.4 0 1 1-4.8 0 2.4 2.4 0 0 1 4.8 0z"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M287.6 377.3h18.7s.3-2.4-2-3.9c9.8-1.3 7.2-10 15.4-10.4 1.6.2-4.2 3.7-4.2 3.7s-5 3.4-2.7 5.2c1.8 1.4 2.5-.9 2.8-2.6.2-1.7 8-2.8 6.8-7.7-1.8-4-12.6 2.7-12.6 2.7h-7.6c-.5-.9-2.6-4.3-4.8-4.3-2.6 0-4.4 4.3-4.4 4.3h-17.3s-.6 4.5 8.2 5.3a10.5 10.5 0 0 0 5.2 4c-1.2 1-1.5 2.1-1.5 3.7z"/>
    <path fill="none" stroke="#000" stroke-width=".8" d="M289 373.5h15.5m-22.6-9.2s1.2 7.3 7 9"/>
    <path fill="url(#d)" fill-rule="evenodd" stroke="#000" stroke-width=".6" d="M66.3 170.5c.5-2.1 1.3-2.5 2.2-5.2.1-2.7-2.2-2.4-1.5-4.1 1.2-1.9.6-3.6-1.6-5 .4 2.4-3 4.7-3 6.7s1.8 1.6 1.6 4.6c0 1.8-.5 1.3-.6 3h3z" transform="translate(195.4 144.5) scale(1.2884)"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M299.6 347a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0z"/>
    <path fill="none" stroke="#f7c600" stroke-width="1.7" d="M413.1 331.2l12-17.7 12.2 17.7"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M427.6 324.6a2.4 2.4 0 1 1-4.8 0 2.4 2.4 0 0 1 4.8 0z"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M415.4 344.2H434s.3-2.3-1.9-3.8c9.8-1.4 7.2-10 15.4-10.5 1.6.3-4.3 3.7-4.3 3.7s-4.9 3.5-2.6 5.3c1.7 1.4 2.5-1 2.8-2.6.2-1.7 7.9-2.8 6.8-7.7-1.8-4-12.6 2.7-12.6 2.7H430c-.5-1-2.6-4.4-4.8-4.4-2.6.1-4.4 4.4-4.4 4.4h-17.4s-.6 4.4 8.2 5.3a10.5 10.5 0 0 0 5.3 4c-1.2 1-1.5 2-1.5 3.6z"/>
    <path fill="none" stroke="#000" stroke-width=".8" d="M416.8 340.4h15.5m-22.7-9.1s1.3 7.2 7 9"/>
    <path fill="url(#e)" fill-rule="evenodd" stroke="#000" stroke-width=".6" d="M66.3 170.5c.5-2.1 1.3-2.5 2.2-5.2.1-2.7-2.2-2.4-1.5-4.1 1.2-1.9.6-3.6-1.6-5 .4 2.4-3 4.7-3 6.7s1.8 1.6 1.6 4.6c0 1.8-.5 1.3-.6 3h3z" transform="translate(323.2 111.5) scale(1.2884)"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M427.4 314a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0z"/>
    <path fill="none" stroke="#f7c600" stroke-width="1.7" d="M288.3 295.8l12-17.6 12.2 17.7"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M302.8 289.2a2.4 2.4 0 1 1-4.8 0 2.4 2.4 0 0 1 4.8 0z"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M290.6 308.9h18.6s.4-2.4-1.9-3.9c9.8-1.3 7.2-10 15.4-10.4 1.6.2-4.3 3.7-4.3 3.7s-4.9 3.4-2.6 5.2c1.7 1.4 2.5-.8 2.8-2.6.2-1.7 7.9-2.8 6.8-7.7-1.8-4-12.6 2.7-12.6 2.7h-7.6c-.5-.9-2.6-4.3-4.8-4.3-2.6 0-4.4 4.3-4.4 4.3h-17.4s-.6 4.5 8.2 5.3a10.5 10.5 0 0 0 5.3 4c-1.2 1-1.5 2.1-1.5 3.7z"/>
    <path fill="none" stroke="#000" stroke-width=".8" d="M292 305h15.5m-22.7-9s1.3 7.2 7 9"/>
    <path fill="url(#f)" fill-rule="evenodd" stroke="#000" stroke-width=".6" d="M66.3 170.5c.5-2.1 1.3-2.5 2.2-5.2.1-2.7-2.2-2.4-1.5-4.1 1.2-1.9.6-3.6-1.6-5 .4 2.4-3 4.7-3 6.7s1.8 1.6 1.6 4.6c0 1.8-.5 1.3-.6 3h3z" transform="translate(198.4 76.1) scale(1.2884)"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M302.6 278.6a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0z"/>
    <path fill="none" stroke="#f7c600" stroke-width="1.7" d="M288.6 261l12-17.6 12.1 17.7"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M303 254.5a2.4 2.4 0 1 1-4.7 0 2.4 2.4 0 0 1 4.8 0z"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M290.9 274.1h18.6s.3-2.3-2-3.8c9.8-1.3 7.3-10 15.5-10.4 1.6.2-4.3 3.6-4.3 3.6s-5 3.5-2.7 5.3c1.8 1.4 2.6-.9 2.8-2.6.3-1.7 8-2.8 6.9-7.7-1.9-4-12.6 2.7-12.6 2.7h-7.7c-.4-1-2.6-4.3-4.7-4.4-2.6.1-4.4 4.4-4.4 4.4h-17.4s-.6 4.4 8.2 5.3a10.5 10.5 0 0 0 5.2 4c-1.1 1-1.4 2-1.4 3.6z"/>
    <path fill="none" stroke="#000" stroke-width=".8" d="M292.2 270.4l15.6-.1m-22.7-9.1s1.2 7.3 7 9"/>
    <path fill="url(#g)" fill-rule="evenodd" stroke="#000" stroke-width=".6" d="M66.3 170.5c.5-2.1 1.3-2.5 2.2-5.2.1-2.7-2.2-2.4-1.5-4.1 1.2-1.9.6-3.6-1.6-5 .4 2.4-3 4.7-3 6.7s1.8 1.6 1.6 4.6c0 1.8-.5 1.3-.6 3h3z" transform="translate(198.7 41.4) scale(1.2884)"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M302.9 243.9a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0z"/>
    <path fill="none" stroke="#f7c600" stroke-width="1.7" d="M289.3 225.8l12-17.6 12 17.7"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M303.7 219.2a2.4 2.4 0 1 1-4.8 0 2.4 2.4 0 0 1 4.8 0z"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M291.5 238.9h18.7s.3-2.4-2-3.9c9.8-1.3 7.3-10 15.4-10.4 1.6.3-4.2 3.7-4.2 3.7s-5 3.4-2.7 5.2c1.8 1.4 2.6-.8 2.8-2.5.2-1.7 8-2.8 6.8-7.7-1.8-4-12.6 2.7-12.6 2.7h-7.6c-.5-1-2.6-4.4-4.8-4.4-2.6 0-4.4 4.4-4.4 4.4h-17.3s-.6 4.4 8.2 5.3a10.5 10.5 0 0 0 5.2 4c-1.1.9-1.5 2-1.5 3.6z"/>
    <path fill="none" stroke="#000" stroke-width=".8" d="M292.9 235.1h15.5m-22.6-9.2s1.2 7.3 7 9"/>
    <path fill="url(#h)" fill-rule="evenodd" stroke="#000" stroke-width=".6" d="M66.3 170.5c.5-2.1 1.3-2.5 2.2-5.2.1-2.7-2.2-2.4-1.5-4.1 1.2-1.9.6-3.6-1.6-5 .4 2.4-3 4.7-3 6.7s1.8 1.6 1.6 4.6c0 1.8-.5 1.3-.6 3h3z" transform="translate(199.4 6.1) scale(1.2884)"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M303.5 208.6a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0z"/>
    <path fill="none" stroke="#f7c600" stroke-width="1.7" d="M414.1 226.4l12-17.7 12.1 17.8"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M428.5 219.8a2.4 2.4 0 1 1-4.7 0 2.4 2.4 0 0 1 4.7 0z"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M416.4 239.5H435s.3-2.4-2-3.9c9.8-1.3 7.3-10 15.5-10.4 1.6.2-4.3 3.6-4.3 3.6s-5 3.5-2.7 5.3c1.8 1.4 2.6-.9 2.8-2.6.3-1.7 8-2.8 6.9-7.7-1.9-4-12.6 2.7-12.6 2.7h-7.7c-.5-.9-2.6-4.3-4.8-4.3-2.6 0-4.4 4.3-4.4 4.3h-17.3s-.6 4.5 8.2 5.3a10.5 10.5 0 0 0 5.2 4c-1.1 1-1.4 2.1-1.4 3.7z"/>
    <path fill="none" stroke="#000" stroke-width=".8" d="M417.7 235.7h15.5m-22.6-9.2s1.2 7.3 7 9"/>
    <path fill="url(#i)" fill-rule="evenodd" stroke="#000" stroke-width=".6" d="M66.3 170.5c.5-2.1 1.3-2.5 2.2-5.2.1-2.7-2.2-2.4-1.5-4.1 1.2-1.9.6-3.6-1.6-5 .4 2.4-3 4.7-3 6.7s1.8 1.6 1.6 4.6c0 1.8-.5 1.3-.6 3h3z" transform="translate(324.2 6.7) scale(1.2884)"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M428.3 209.2a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0z"/>
    <path fill="none" stroke="#f7c600" stroke-width="1.7" d="M413 261l12-17.7 12 17.7"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M427.4 254.4a2.4 2.4 0 1 1-4.8 0 2.4 2.4 0 0 1 4.8 0z"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M415.2 274l18.7.1s.3-2.4-2-3.9c9.8-1.3 7.2-10 15.4-10.4 1.6.2-4.3 3.6-4.3 3.6s-4.9 3.5-2.6 5.3c1.8 1.4 2.5-.9 2.8-2.6.2-1.7 8-2.8 6.8-7.7-1.8-4-12.6 2.7-12.6 2.7h-7.6c-.5-.9-2.6-4.3-4.8-4.3-2.6 0-4.4 4.3-4.4 4.3h-17.4s-.5 4.5 8.3 5.3a10.5 10.5 0 0 0 5.2 4c-1.2 1-1.5 2.1-1.5 3.7z"/>
    <path fill="none" stroke="#000" stroke-width=".8" d="M416.6 270.3H432m-22.7-9.2s1.3 7.3 7 9"/>
    <path fill="url(#j)" fill-rule="evenodd" stroke="#000" stroke-width=".6" d="M66.3 170.5c.5-2.1 1.3-2.5 2.2-5.2.1-2.7-2.2-2.4-1.5-4.1 1.2-1.9.6-3.6-1.6-5 .4 2.4-3 4.7-3 6.7s1.8 1.6 1.6 4.6c0 1.8-.5 1.3-.6 3h3z" transform="translate(323 41.3) scale(1.2884)"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M427.2 243.8a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0z"/>
    <path fill="none" stroke="#f7c600" stroke-width="1.7" d="M411.4 296.4l12-17.7 12.1 17.7"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M425.9 289.8a2.4 2.4 0 1 1-4.8 0 2.4 2.4 0 0 1 4.8 0z"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M413.7 309.4h18.6s.3-2.3-2-3.8c9.8-1.3 7.3-10 15.5-10.5 1.6.3-4.3 3.7-4.3 3.7s-5 3.5-2.7 5.3c1.8 1.4 2.6-.9 2.8-2.6.3-1.7 8-2.8 6.9-7.7-1.8-4-12.6 2.7-12.6 2.7h-7.7c-.4-1-2.6-4.4-4.7-4.4-2.6.1-4.4 4.4-4.4 4.4h-17.4s-.6 4.4 8.2 5.3a10.5 10.5 0 0 0 5.3 4c-1.2 1-1.5 2-1.5 3.6z"/>
    <path fill="none" stroke="#000" stroke-width=".8" d="M415 305.6h15.6m-22.7-9.1s1.2 7.2 7 9"/>
    <path fill="url(#k)" fill-rule="evenodd" stroke="#000" stroke-width=".6" d="M66.3 170.5c.5-2.1 1.3-2.5 2.2-5.2.1-2.7-2.2-2.4-1.5-4.1 1.2-1.9.6-3.6-1.6-5 .4 2.4-3 4.7-3 6.7s1.8 1.6 1.6 4.6c0 1.8-.5 1.3-.6 3h3z" transform="translate(321.5 76.7) scale(1.2884)"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M425.7 279.2a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0z"/>
    <path fill="none" stroke="#f7c600" stroke-width="1.3" d="M413 401l12-17.7 12 17.7"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M427.4 394.3a2.4 2.4 0 1 1-4.8 0 2.4 2.4 0 0 1 4.8 0z"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M415.2 414H434s.2-2.4-2-3.8c9.8-1.4 7.2-10 15.4-10.5 1.6.3-4.3 3.7-4.3 3.7s-4.9 3.5-2.6 5.2c1.7 1.4 2.5-.8 2.8-2.5.2-1.7 7.9-2.8 6.8-7.7-1.8-4-12.6 2.7-12.6 2.7l-7.6-.1c-.5-.9-2.6-4.3-4.8-4.3-2.6 0-4.4 4.4-4.4 4.4h-17.4s-.5 4.4 8.3 5.3a10.5 10.5 0 0 0 5.2 4c-1.2.9-1.5 2-1.5 3.6z"/>
    <path fill="none" stroke="#000" stroke-width=".8" d="M416.6 410.2H432m-22.7-9.2s1.3 7.3 7 9"/>
    <path fill="url(#l)" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M814.5 330.3c.6-2.8 1.6-3.3 2.8-6.8.2-3.5-2.8-3-2-5.3 1.6-2.4.8-4.7-2-6.5.5 3.2-3.8 6.1-3.8 8.7 0 2.6 2.2 2 2 6 .1 2.2-.6 1.7-.8 3.9h3.8z" transform="translate(-406 70.7)"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M427.2 383.7a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0z"/>
    <path fill="none" stroke="#f7c600" stroke-width="1.7" d="M412.8 366l12-17.7 12 17.7"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M427.2 359.4a2.4 2.4 0 1 1-4.8 0 2.4 2.4 0 0 1 4.8 0z"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M415 379h18.7s.3-2.4-2-3.8c9.8-1.4 7.2-10 15.4-10.5 1.6.3-4.2 3.7-4.2 3.7s-5 3.5-2.7 5.2c1.8 1.4 2.5-.8 2.8-2.5.2-1.7 8-2.8 6.8-7.7-1.8-4-12.6 2.7-12.6 2.7h-7.6c-.5-1-2.6-4.4-4.8-4.4-2.6.1-4.4 4.4-4.4 4.4h-17.3s-.6 4.4 8.2 5.3a10.5 10.5 0 0 0 5.2 4c-1.2.9-1.5 2-1.5 3.6z"/>
    <path fill="none" stroke="#000" stroke-width=".8" d="M416.4 375.2h15.5m-22.6-9.1s1.2 7.2 7 9"/>
    <path fill="url(#m)" fill-rule="evenodd" stroke="#000" stroke-width=".6" d="M66.3 170.5c.5-2.1 1.3-2.5 2.2-5.2.1-2.7-2.2-2.4-1.5-4.1 1.2-1.9.6-3.6-1.6-5 .4 2.4-3 4.7-3 6.7s1.8 1.6 1.6 4.6c0 1.8-.5 1.3-.6 3h3z" transform="translate(322.9 146.3) scale(1.2884)"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M427 348.7a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0z"/>
    <path fill="none" stroke="#f7c600" stroke-width="1.7" d="M311.1 333.4l12-17.6 12.2 17.7"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M325.6 326.8a2.4 2.4 0 1 1-4.8 0 2.4 2.4 0 0 1 4.8 0z"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M313.4 346.5H332s.3-2.4-1.9-3.8c9.8-1.4 7.2-10 15.4-10.5 1.6.3-4.3 3.7-4.3 3.7s-4.9 3.5-2.6 5.2c1.7 1.4 2.5-.8 2.8-2.5.2-1.7 7.9-2.8 6.8-7.7-1.8-4-12.6 2.7-12.6 2.7H328c-.5-1-2.6-4.4-4.8-4.4-2.6 0-4.4 4.4-4.4 4.4h-17.4s-.6 4.4 8.2 5.3a10.5 10.5 0 0 0 5.3 4c-1.2.9-1.5 2-1.5 3.6z"/>
    <path fill="none" stroke="#000" stroke-width=".8" d="M314.8 342.7h15.5m-22.7-9.2s1.3 7.3 7 9"/>
    <path fill="url(#n)" fill-rule="evenodd" stroke="#000" stroke-width=".6" d="M66.3 170.5c.5-2.1 1.3-2.5 2.2-5.2.1-2.7-2.2-2.4-1.5-4.1 1.2-1.9.6-3.6-1.6-5 .4 2.4-3 4.7-3 6.7s1.8 1.6 1.6 4.6c0 1.8-.5 1.3-.6 3h3z" transform="translate(221.2 113.7) scale(1.2884)"/>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width=".8" d="M325.4 316.2a2.2 2.2 0 1 1-4.4 0 2.2 2.2 0 0 1 4.4 0z"/>
    <g stroke="#000">
      <path fill="#f7c600" fill-rule="evenodd" stroke-width=".8" d="M375.5 417.4s4.9 11.2 10.4 4.3 3.5-9.8 3.5-9.8L377 405l-3.6 7.7 2.1 4.6z"/>
      <path fill="#ffc6b5" fill-rule="evenodd" stroke-width=".8" d="M386.2 416s.8 0 1.5-1.2-1.5-1.7-2.5-3l-1 2 2 2.1zm-28.1-3l-11 5.9s-5.4 1-5.8 0c-.4-1 .1-2 3-2.2 2.8-.1 10.5-7.1 10.5-7.1l3.3 3.3zm.1-194.6l.4 4c.1 1.3-2.2 4.1-2.3 4s-1.2.1-1 1 1.7 1 1.7 1-.7 2.9 0 3c.7.1-1.8 3.7 0 4.6 1.7 1 4.7 2.2 6 1.9 1.4-.3 0 5.3 0 5.3l-3.7 8 20.8-2-4.3-7s-2-1.3-1.5-5.3c.5-3.9-.3-21.6-.3-21.6l-14.9-2-.9 5.1zm-3.8 30.9s-6.8 3-6.5 11.4c-1.7 8.2-2.7 16.3-2.7 16.3s-8 9-10.4 12.3c-2.4 3.2-6 9.8-7.4 11.6s-6.6 7.6-6.5 9.7c.1 2.2-1.2 11.8 4 12.9 1.4.5 5.7-11.1 5.7-11.1s.3-5-1.2-6 3.3-4.2 3.3-4.2l11-8.2c2-1.6 7.6-7.8 7.6-7.8l3.1-37z"/>
      <path fill="#fff" fill-rule="evenodd" stroke-width=".8" d="M362.1 242.9s1.8 4.7 5.7 4c4-.9 8.5-4.5 8.5-4.5s3.7-.2 4.2.4c.5.5 9.9 9.6 9.6 12.4-.3 2.8-4.3 2-5.8 4s-4 6.5-3.3 10 2.7 8.2 2.5 10-1.8 2.2-1.8 3.2 1.2 2.5 1.2 4.3-1.6 4.3-1.3 6c.3 1.8.4 7 .4 7l-.4 23.8s1.3.8 1.5 2.1c.1 1.4 9.2 40.7 9.2 40.7s-.4 1.2-1.4 1 3.7 6.2 3.8 8 4.7 15.5 4.6 17.4c-.1 1.9-.8 6-1.2 6.2-.4.1 3 8.6 2.4 10-.5 1.3-6 1.2-6 1.2l-1.6-.3s.2 1.8-1 2-9-.5-9-.5-2.3 3.5-3.6 3.4c-1.4-.1-3.1-2.6-3.5-2.2-.4.4 1.2 2.7.8 3.4-.4.7-7.3 2.2-8.7-1-1.3-3.3.8-2.5.4-3.2-.4-.7-3.5-2.4-4.4-1.9s2.4 1.4 2.3 2.7c-.2 1.4-3 3.4-4 3.4s-3.7-5-7.5-4.4-6.2 1.4-6.2 1.4-4.5 2-6.4 1.5c-1.9-.4-2.7-1.9-2.7-2.7s1.4-4.3 1.2-5.4-1.2-2.1-1.2-3.8a31 31 0 0 1 3.1-7.1l-.1-24.9s-2.9 0-3-1.8 4.3-39.3 5-41.7c.7-2.5 2.4-11.1 2.4-11.1s-2 1-2.1 0 6-22.4 6-22.4 1.1-10.7 1.1-13.6-.5-6.7-.5-6.7-5.6-2.4-5.7-6c-.5-5.7 5.3-8.9 6-10.8l2.7-7.5s3-5.2 7.8-6z"/>
      <path fill="#f7c600" fill-rule="evenodd" stroke-width=".8" d="M360.1 414.6s-13.1 6.9-15 7.4c-2 .5-3.1-2.3-1.2-2.8 1.9-.6 4.8-.8 4.8-.8s-4.4-3.6-4.3-3.7l6.2-2.3c.2 0 2 3.5 3.1 3.3s4.8-3 4.8-3l1.6 1.9z"/>
      <path fill="#ffc6b5" fill-rule="evenodd" stroke-width=".8" d="M378.6 418.3c1.2 1.6 1.7 4.4 4.5 2.8 2.9-1.6-1.4-4.6-1.4-4.6l-3.1 1.8z"/>
      <path fill="#ffc6b5" fill-rule="evenodd" stroke-width=".8" d="M383.8 418s1.3 1.2 2.5 0-2.4-3.9-2.4-3.9l-1.8 2 1.6 2z"/>
      <path fill="none" stroke-width=".8" d="M379.7 248.2s-12 7.7-11.8 10.4m14.5-9l-2.8 2.9m6.4.3s-6 4.8-5 8m-23-13.3s-1.8 3.6-1.4 4.9c.4 1.2 3.3 5.8 3.8 8.6.4 2.8 0 4.9 0 4.9m-5.6-11.6s.6 4.3 1.4 5c.8.9 3 4.5 3.2 6m-5.7 7s3.5 1.7 6.8-5m5.3-4.8c-.2 0-2.6 6.6 1.7 9s7.6 2.2 9.5 1.5c1.9-.6 3.9-1.9 3.9-1.9m-17-.5s.2 9 13.4 17.7m-12.9-9.3s-.1 7.8 5 11.4m-7.7-20.6s-4 11.6-7 12.8m5.8-4.3s-.1 8.4-1.2 11.4m-1.1 2s2.7 3.4 5.7 3.1c3-.3 4.2-3.8 6.2-3.2 2 .5 4 2.1 8.6 1.7m-6.7 3.5s0 7 1.2 7.6.7 7 .7 7m-18.4-17.8s-.1 6.5-1 8.8c-.8 2.3-2.4 6.2-2.1 9.6m-5.1 3.8c.6-.3 3-2.3 3-2.3m1.1 1.5s-5.8 24.8-4.2 39.7m5.6-38.5s-3 18.6-1.6 22.1m.1-22.7l11.4.8m1.6-1.4s3 1.6 7.4 1.3M363 324s-.5 32-1.3 39m18-31.7s3.5 28 5.5 30.6m-12.3-27s2.2 24.7 3.4 26.8m-34 8.7s4.1-1.4 7.9-5.4c4.3 5.8 11 .2 11 .2s10.2 7 14.8-.8c7 4.6 10.6-.6 10.6-.6s2.5 3.9 4.4 3.5m-11.2 2.7s5.3 24.7 13 31.7m-40.2-34.3s.7 20.7 2 35.3m-3-12s-.6 13.6-1.4 14.5m-6.5 1.4s1.5 5.8 9 .4c7.4-5.4 7.5 2 7.8 2.9.2.8 1.5 6.6 4.3 1.7m6-8s-1.3 12 9.4 3.3c10.7-8.8 12.4-.2 12.7 2.5"/>
      <path fill="#9c5100" fill-rule="evenodd" stroke-width=".8" d="M357.8 217.6s3 .4 4.6-.5c1.6-1 3.5-1.4 4.9.5s2.3 1.8 2.3 1.8-2 5 0 5.5c2 .6 3 .6 3 1.2.2.7-1.7 2.2-1.2 2.9.6.6 1.5 1.5 1.7 2 .1.5-1.3 2.8-.8 3.4.4.5 1.6 2.7 2.4 2.7s.3 3.4 2.7 2.6 2.3-3 2.3-3 2.6-.4 3.2-2.7c.7-2.3 2.3-2.9 2.3-2.9s3.3-1.7-1-4.4c0-19-12.5-17-12.5-17s-1.5-3.3-4-2.9c-2.3.4-2.5 3.3-4.2 3-1.8-.3-2.2-1.5-2.3-1.4-.2.2-1.7 2.9-1.7 3.5 0 .7-4.7-.9-4.3 2.6s2.7 3.4 2.6 3.1z"/>
      <path fill="none" stroke-width=".8" d="M370.8 213s-1 6 5.4 5.4c-.8 3.3 1.6 4.4 1.6 4.4m4.7 3.6c.2 0 2.9 1.8-.1 4m-9 .7s1.3 1.7 2.8 1.3c1.5-.4 3.9 1.4 3.9 1.4s2 .7 2.3.3m-17.3 2.2s4.6 1.7 7.6-5.6m-16.1-2.1l2.7.1"/>
      <path fill="none" stroke-linejoin="round" stroke-width=".8" d="M357 231.3h2.4l-2.1 1"/>
      <path fill="none" stroke-linejoin="round" stroke-width="1.4" d="M360.4 223.7c.4 0 1.9-.5 2.1-.1s-1.6.6-2.1.1z"/>
      <path fill="#ffc6b5" fill-rule="evenodd" stroke-width=".8" d="M389.6 255.8s4.4 13.4 5 16.8c.5 3.4 2.3 16.9 1.6 18.8-.7 1.9-7.6 11-8.4 13.2-.8 2.3-5.7 11.1-5.7 11.1s-1.2 8.7-1.7 9c-.6.5 1.4 2.6 1.2 3.3-.3.8-4 4.6-5.6 4.2-1.6-.4-4.2-2.3-4.3-4s.1-7.6 1.4-9.1 7.5-16.5 8-17.5c.3-.9 5.7-12.7 5.9-14.7.1-2-1.7-6.7-3.6-8.4-4.3-12.6-2.6-20.1 6.2-22.7z"/>
      <path fill="none" stroke-width=".8" d="M326.6 313.8l.2 6m-5.8-6.4s3.8 6.3 3.5 9.7"/>
      <path fill="none" stroke-linejoin="round" stroke-width=".8" d="M372 326.6s2.8-.3 2.7 4.5c1.8-5.9 5.5-6 5.5-6"/>
    </g>
    <path fill="#f7c600" fill-rule="evenodd" stroke="#000" stroke-width="1.7" d="M362.2 462a137 137 0 0 0 93.6-43.2c-.4-.4 7.5-11.5 13.9-10 6.3 1.7 15.4 20.3 26.5 24.3 5.6 8.7-1.6 16.6-4 18.2-2.3 1.6-13 6-14.6-.4-1.6-6.3-4.8-5.2-4.8-5.2s-50.7 49.6-109.4 47.6c-60.6.4-110.6-47.6-110.6-47.6l-4.3 4.8s-4.8 5.2-7.2 4.8c-2.3-.4-12.7-7.2-13.4-14-.8-6.7 6.3-11 6.3-11s17.4-13.5 19.4-20.6c4-4 11.5 2.7 11.5 2.7s46 52.8 97.1 49.6z"/>
    <path fill="none" stroke="#000" stroke-width="1.7" d="M233 431.3s4.7-1.2 6.5.7a719 719 0 0 0 13.5 13.6"/>
    <path fill="none" stroke="#000" stroke-width="1.7" d="M243.8 436.8l-4.8 3.6s12 2.3 9.3 10.1M492 431s-2.4-1.2-6 1.5c-3.8 2.8-13.1 13.2-13.1 13.2"/>
    <path fill="none" stroke="#000" stroke-width="1.7" d="M481.4 436.5l5 4s-10.6.7-8.3 10.8"/>
    <path d="M293.4 444l-.3.4a3.5 3.5 0 0 0-2.5-.2c-.6.2-2 .8-3.8 1.9l-15.7 9.2-.4-.3 4-18.4.6-4c0-.5-.4-1-1-1.6l.3-.5 8.5 6-.3.5-.3-.2c-.8-.5-1.4-.8-1.8-.8a.8.8 0 0 0-.7.4 2.3 2.3 0 0 0-.3.6l-.5 2.2-2.5 11.5 9-5.3 2.2-1.4.7-.7.3-.9c0-.3 0-.6-.3-.9a5 5 0 0 0-1.3-1.2l.3-.5 5.8 4.1m5.7 27.3l-.3.5-10-5.4.3-.5.7.3a3 3 0 0 0 1.5.5c.3 0 .6-.2.9-.4.2-.2.6-.7 1.1-1.7l6.8-12.5a7 7 0 0 0 .9-2c0-.2 0-.6-.3-1-.2-.3-.6-.6-1.1-.9l-.6-.3.2-.5 10 5.4-.3.5-.6-.3a3 3 0 0 0-1.5-.5c-.3 0-.6.2-1 .4-.2.2-.6.7-1 1.7l-7 12.5a7.1 7.1 0 0 0-.8 2l.3.9c.2.4.6.7 1.1 1l.7.3m36.3-8.5l-2.5 7.2-.6-.2a9.7 9.7 0 0 0-1.4-5.6 7.6 7.6 0 0 0-3.9-3.1 6 6 0 0 0-4.3 0 7.3 7.3 0 0 0-3.4 2.8 18.9 18.9 0 0 0-2.3 4.5c-.7 2-1.1 3.7-1.2 5.4s.3 3 1.1 4a6.8 6.8 0 0 0 3.3 2.4 8.3 8.3 0 0 0 1.6.4 12 12 0 0 0 1.7 0l1.6-4.2c.2-.8.4-1.3.3-1.6a1.6 1.6 0 0 0-.4-.9 2.7 2.7 0 0 0-1.2-.7l-.5-.2.2-.6 10 3.6-.3.5a4.5 4.5 0 0 0-1.7-.2c-.3 0-.6.2-1 .5l-.5 1.4-1.6 4.2a22.2 22.2 0 0 1-9-1.3 13.7 13.7 0 0 1-4.6-2.5 13.2 13.2 0 0 1-2.7-3.3 10.6 10.6 0 0 1-1.2-3.7c-.2-1.6 0-3.3.6-5 1-3 3-5.3 6-6.6a11.8 11.8 0 0 1 9.3-.3c1 .3 1.9.7 2.6 1.2.5.2 1 .7 2 1.5a9.8 9.8 0 0 0 1.5 1.1h.9a3 3 0 0 0 1-1l.6.3m15.3 24.6v.5l-11.3-1.7.1-.5h.7a3 3 0 0 0 1.6 0c.3-.1.5-.4.7-.7.2-.2.3-.9.5-2l2.2-14c.1-1.2.2-1.9 0-2.2 0-.3-.2-.5-.5-.8a2.8 2.8 0 0 0-1.4-.5l-.7-.1v-.6l11.3 1.7v.6l-.8-.1a3 3 0 0 0-1.5 0l-.8.7c-.2.3-.3 1-.5 2l-2.1 14.1a7.2 7.2 0 0 0-.2 2.1c.1.3.3.6.6.8.4.3.8.5 1.4.5l.7.2m29.2-7.1l-.5 7.6-19.2 1v-.6h.7a3 3 0 0 0 1.5-.4c.3-.2.5-.5.6-.8v-2l-.7-14.3a7.1 7.1 0 0 0-.3-2c0-.3-.3-.6-.7-.8-.4-.2-.9-.2-1.5-.2h-.7v-.6l11.5-.5v.6h-.9a3 3 0 0 0-1.5.4l-.6.8a7 7 0 0 0 0 2l.6 13.8c0 1.2.2 1.9.3 2.2.2.3.4.5.8.6h3.8a6.1 6.1 0 0 0 2.8-.8 5.3 5.3 0 0 0 1.9-2c.5-1 1-2.2 1.4-4h.7m21-3.7l-7.3 2.2-.3 2.3a4.7 4.7 0 0 0 0 1.8c.2.6.6 1 1.2 1 .3.2 1 0 2-.1l.3.5-7 2-.1-.5a3.3 3.3 0 0 0 1.6-1.4c.3-.7.6-2 .8-3.7l2.5-18.8.3-.1 12.6 14.8c1.2 1.4 2 2.2 2.6 2.5.5.2 1 .3 1.7.1l.1.6-10 3-.2-.6.4-.1c.8-.2 1.4-.5 1.6-.8.2-.3.2-.5.1-.8a2 2 0 0 0-.2-.6l-.8-1-1.9-2.3m-.9-1l-5.2-6.2-1 8.1 6.2-1.8m27.8-28.3l2.7 5.2-.5.2a9.1 9.1 0 0 0-2.3-2 4.3 4.3 0 0 0-2.2-.4c-.5 0-1.1.2-2 .7l-1.5.8 8 14.7 1.1 1.8c.3.2.6.3 1 .3a3 3 0 0 0 1.4-.4l.7-.4.2.5-10 5.4-.2-.5.6-.3a3 3 0 0 0 1.2-1l.2-1a6.6 6.6 0 0 0-.8-1.9l-8-14.7-1.3.7c-1.3.7-2.1 1.5-2.4 2.3-.4 1.2-.3 2.6.2 4.1l-.5.3-2.8-5.2 17.2-9.2m13-7.7l5.3 7 .3-.2c1.1-.9 1.7-1.8 1.6-2.9s-.4-2.3-1.3-3.7l.5-.4 6.8 9-.5.4a8.4 8.4 0 0 0-2.4-1.9c-.8-.4-1.5-.5-2.1-.4-.6.1-1.3.5-2.2 1.1l3.7 4.9a10 10 0 0 0 1.4 1.6c.3.2.6.2.9.2.3 0 .7-.2 1.2-.6l1-.8a7.8 7.8 0 0 0 3-4 7.8 7.8 0 0 0-.4-5l.5-.4 3.4 6.1-15 11.3-.4-.5.6-.4a3 3 0 0 0 1-1.2c.2-.3.2-.6 0-1a6.6 6.6 0 0 0-1-1.7l-8.6-11.4-1.2-1.4a1.5 1.5 0 0 0-1-.3c-.4 0-1 .2-1.5.6l-.6.4-.4-.4 14.5-11 3.9 5.2-.5.3a7.7 7.7 0 0 0-3-1.9 4.8 4.8 0 0 0-2.9.2l-2.8 1.9-1.8 1.3"/>
    <path fill="none" d="M258.4 445.6c70.9 57.5 140.4 58.5 208.4 0"/>
  </g>
</svg>
