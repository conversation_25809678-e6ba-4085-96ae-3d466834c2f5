@font-face {
  font-family: 'Poppins';
  src: url('/static/udon/local-fonts/Poppins/Poppins-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/static/udon/local-fonts/Poppins/Poppins-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/static/udon/local-fonts/Poppins/Poppins-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/static/udon/local-fonts/Poppins/Poppins-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
}
@font-face {
  font-family: 'Poppins';
  src: url('/static/udon/local-fonts/Poppins/Poppins-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}

/* overlay (https://bryanlrobinson.com/blog/how-to-css-after-elements-for-background-overlays/) */
#bg-video::after {
  content: "";
  position: absolute;
  background-color: rgba(31,39,43,0.75);
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  z-index: 1;
}
/* bring title to front */
section.heading-page > * {
  position: relative;
  z-index: 2;
}

/* waviy (https://alvarotrigo.com/blog/css-text-animations/) */
.waviy {
  position: relative;
/*   margin-bottom: 20px !important; */
}
.waviy span {
  position: relative;
  display: inline-block;
  animation: flip-letter 7s infinite;
  animation-delay: calc(.2s * var(--i));
}
@keyframes flip-letter {
  0%,80% {
    transform: rotateY(360deg) 
  }
}


.auto_update_chart_div {
  position: relative;
  max-width: 100vw;  /* to stop scrolling on x axis when in mobile */
}
#auto_update_chart {
  position: absolute;
/*   opacity: .15; */
  height: 100%;
  z-index: -1;

  /* center horizontally */
  left: 0;
  right: 0;
  bottom: 0;
  margin-left: auto;
  margin-right: auto;

  /* center vertically */
  top: 50%;
  transform: translate(0, -50%);
}






/* overlay (https://bryanlrobinson.com/blog/how-to-css-after-elements-for-background-overlays/) */
body::after {
  content: "";
  position: absolute;
  background-color: rgba(31,39,43,0.75);
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  z-index: 1;
}
/* bring title to front */
body > * {
  position: relative;
  z-index: 2;
}

.content-pad {
  border-radius: .7rem
}


.navbar .navbar-nav .nav-item .nav-link {
  border-bottom: 1px solid gray;
}

.nav-link {
  color: grey !important;
}

.circle {
  background-color: grey;
}

.highlight {
    color: white;
}

.navbar .navbar-nav .nav-item.selected .nav-link,
.navbar .navbar-nav .nav-item .nav-link:hover {
    color: white !important;
    border-bottom: 1px solid white;
}

.cd-slider-nav li.selected a+.circle,
.navbar .navbar-nav .nav-item .nav-link:hover+.circle {
    background-color: white;
}

.tm-link-white:hover,
.tm-link-white:focus {
    color: #white;
}

.form-control:focus {
    border-bottom: 1px solid white;
}

.tm-border-top .circle,
.tm-border-bottom .circle {
  background-color: white;
}

.btn {
  font-size: .85rem;
  padding: 8px 20px;
  border-radius: .35rem;
  color: white;
  background-color: rgba(255, 255, 255, .05);
}
.btn:hover,
.btn:focus {
  background-color: rgba(255, 255, 255, .1);
  color: white;
}

.tm-bg-dark {
  background-color: rgba(0,0,0,0.5);
}

.page-left {
    margin-left: 0;
}
