{% extends 'dashboard.html' %}
{% load static %}


{% block content %}
  <div class="row justify-content-center">
    <div class="col-xxl-4 col-xl-5 col-lg-8 col-11 look_up_div">
      <div class="mx-auto text-center justify-content-center py-5 my-5">

        <h2 class="page-title mb-5">Enter Domain or IP</h2>

        <form
          method="post"
          class="shadow-sm"

          hx-post="{{request.path}}"
          hx-trigger="submit"
          hx-target="#lookup_info_div"
          hx-swap="outerHTML"
        >
          {% csrf_token %}

          <div class="input-group mb-4">
            <input
              type="text"
              class="form-control form-control-lg"
              aria-label="IP or Domain"
              aria-describedby="ip-or-domain"
              placeholder=""
              name="look-up"
              {% if look_up %}value="{{look_up}}"{% endif %}
              id="lookup_input_field"
              required
              autofocus
            >
            <button
              class="btn btn-info px-5"
              type="submit"
              id="ip-or-domain"
            >Submit</button>
          </div>

        </form>

        {% include 'general/00-lookup--table.html' %}

      </div>
    </div>
  </div>
{% endblock content %}


{% block scripts %}
  <script src="{% static "js/htmx-lookup--before-after.js" %}"></script>
{% endblock scripts %}
