{% extends 'dashboard.html' %}
{% load static %}
{% load humanize %}
{% load tags-filters %}


{% block csses %}
  {# for swiper #}
  <link rel="stylesheet" href="{% static "udon/dist/assets/libs/swiper/swiper-bundle.min.css" %}">
{% endblock csses %}


{% block content %}
  {% comment %}
      __HIDDEN_DIVS_FOR_IMPORTED_JS__
      for swiper, we have to import
      dist/assets/libs/apexcharts/apexcharts.min.js and
      /dist/assets/js/stocks-dashboard.js.
      if the elements below are not present,
      js file(s) will throw error and exit.
      so we have to create them
      but we make them hidden
  {% endcomment %}
  <div id="amazon-stock"           class="d-none"></div>
  <div id="apple-stock"            class="d-none"></div>
  <div id="apple-stocks-marketcap" class="d-none"></div>
  <div id="microsoft-stock"        class="d-none"></div>
  <div id="nvidia-stock"           class="d-none"></div>
  <div id="portfolio-value"        class="d-none"></div>
  <div id="profile-views"          class="d-none"></div>
  <div id="projectAnalysis"        class="d-none"></div>
  <div id="returns-rate"           class="d-none"></div>
  <div id="total-invested"         class="d-none"></div>
  <div id="totalInvestmentsStats"  class="d-none"></div>

  <div class="row g-3">
    {% if request.user.is_superuser %}
      {# apps and dbsizes #}
      <div class="col-12">
          {# from dist/html/index-10.html #}
          <div class="card custom-card     bg-transparent shadow-none">
            <div class="card-body     p-0" id="htmx_fade_in">
              {% if apps_and_dbsizes %}
                <div class="d-flex align-items-center justify-content-between">
                  <div class="swiper swiper-basic">
                    <div class="swiper-wrapper">
                      {% for app, dbsize in apps_and_dbsizes.items %}
                        <div class="swiper-slide">
                          <div class="d-flex gap-2 flex-wrap align-items-center justify-content-between p-3 border rounded     bg-white">
                            <div class="d-flex flex-fill align-items-center">
                              <div class="me-2">
                                <span class="avatar avatar-rounded bg-light p-2 border">
                                  <i class="bi bi-database-check fs-18 {% if forloop.counter == 1 %} text-danger {% else %} text-muted{% endif %}"></i>
                                </span>
                              </div>
                              <div class="lh-1">
                                <span class="d-block mb-2 text-default">{{app}}</span>
                                <span class="d-block text-muted fs-12">{% get_percent dbsize.0 total_size_of_dbs %}%</span>
                              </div>
                            </div>
                            <div class="text-info fs-12 text-end">
                              <span class="d-block">{{dbsize.1}}</span>
                              <span class="d-block text-muted fs-11">{{dbsize.0|intcomma}} bytes</span>
                            </div>
                          </div>
                        </div>
                      {% endfor %}
                    </div>
                  </div>
                </div>
              {% else %}
                <p class="fs-15 fw-medium mb-0">No Database Statistics</p>
              {% endif %}
            </div>
          </div>
      </div>

      {# logs #}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">Logs Statistics <span class="text-gray fw-normal"><small>(updated on: {{logs_statistics.aymdhms}})</small></div>
            <div class="badge bg-dark-transparent">{{total_size_of_logs|convert_byte}}</div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th scope="col"></th>
                    <th scope="col">Name</th>
                    <th scope="col">Size</th>
                  </tr>
                </thead>
                <tbody>
                  {% for log_name, size in logs_statistics.items %}
                    {% if not log_name == "aymdhms" %}
                      <tr>
                        <td class="ps-2">{{forloop.counter0}}</td>
                        <td class="text-nowrap">{{log_name}}</td>
                        <td>{{size|convert_byte}}</td>
                      </tr>
                    {% endif %}
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{logs_statistics|length|add:"-1"|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {# databases #}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">Databases Statistics <span class="text-gray fw-normal"><small>(updated on: {{databases_statistics.aymdhms}})</small></div>
            <div class="badge bg-dark-transparent">{{total_size_of_dbs|convert_byte}}</div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th scope="col"></th>
                    <th scope="col">Name</th>
                    <th scope="col">Size</th>
                  </tr>
                </thead>
                <tbody>
                  {% for database_name, size in databases_statistics.items %}
                    {% if not database_name == "aymdhms" %}
                      <tr>
                        <td class="ps-2">{{forloop.counter0}}</td>
                        <td class="text-nowrap">{{database_name}}</td>
                        <td>{{size|convert_byte}}</td>
                      </tr>
                    {% endif %}
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{databases_statistics|length|add:"-1"|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {# parsed dates #}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">Parsed Dates Statistics <span class="text-gray fw-normal"><small>(updated on: {{parsed_dates_statistics.aymdhms}})</small></div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th scope="col"></th>
                    <th scope="col">Name</th>
                    <th scope="col">Start</th>
                    <th scope="col">End</th>
                    <th scope="col">Count</th>
                  </tr>
                </thead>
                <tbody>
                  {% for name, dates in parsed_dates_statistics.items %}
                    {% if not name == "aymdhms" %}
                      <tr>
                        <td class="ps-2">{{forloop.counter0}}</td>
                        <td class="text-nowrap">{{name}}</td>
                        {% if dates %}                          
                          <td>{{dates.0}}</td>
                          <td>{{dates|last}}</td>
                          <td>{{dates|length}}</td>
                        {% else %}
                          <td></td>
                          <td></td>
                          <td></td>
                        {% endif %}
                      </tr>
                    {% endif %}
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{parsed_dates_statistics|length|add:"-1"|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {# disk usage #}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">Disk Usage Statistics <span class="text-gray fw-normal"><small>(updated on: {{disk_usage_statistics.aymdhms}})</small></div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th scope="col"></th>
                    <th scope="col">Name</th>
                    <th scope="col">Space</th>
                    <th scope="col">Percent</th>
                  </tr>
                </thead>
                <tbody>
                  {% for name, space in disk_usage_statistics.items %}
                    {% if not name == "aymdhms" %}
                      <tr>
                        <td class="ps-2">{{forloop.counter0}}</td>
                        <td class="text-nowrap">{{name}}</td>
                        <td>{{space|convert_byte}} <small class="text-muted">({{space|intcomma}} bytes)</small></td>
                        <td>
                          {% if not name == "Total" %}
                            {% get_percent space disk_usage_statistics.Total %}%</td>
                          {% endif %}
                      </tr>
                    {% endif %}
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{disk_usage_statistics|length|add:"-1"|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}

    {# sensor #}
    {% if sensor_objects %}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">
              {% include '00-header-title.html' with mode="no-date" ttl="Sensor" %}
            </div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th class="ps-2" scope="col">ID</th>
                    <th scope="col">Name</th>
                    <th scope="col">Address</th>
                    <th scope="col">Description</th>
                  </tr>
                </thead>

                <tbody>
                  {% for so in sensor_objects %}
                    <tr>
                      <td class="ps-2">{{ so.id|intcomma }}</td>
                      <td>{{so.name}}</td>
                      <td>{{so.address}}</td>
                      <td>{%if so.description %}{{so.description}}{% endif %}</td>  {% comment %}JUMP_1 if statement because the field is allowed to be left blank in database{% endcomment %}
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{sensor_objects|length|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    {# sensor / #}

    {# module #}
    {% if module_objects %}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">
              {% include '00-header-title.html' with mode="no-date" ttl="Module" %}
            </div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th class="ps-2" scope="col">ID</th>
                    <th scope="col">Name</th>
                    <th scope="col">Description</th>
                  </tr>
                </thead>

                <tbody>
                  {% for mo in module_objects %}
                    <tr>
                      <td class="ps-2">{{ mo.id|intcomma }}</td>
                      <td>{{mo.name}}</td>
                      <td>{%if mo.description %}{{mo.description}}{% endif %}</td>  {% comment %}JUMP_1 if statement because the field is allowed to be left blank in database{% endcomment %}
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{module_objects|length|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    {# module / #}

    {# static ip #}
    {% if static_ip_objects %}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">
              {% include '00-header-title.html' with mode="no-date" ttl="Static IP" %}
            </div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th class="ps-2" scope="col">ID</th>
                    <th scope="col">Computer Name</th>
                    <th scope="col">Real Name</th>
                    <th scope="col">Address</th>
                    <th scope="col">Virtual Address</th>
                    <th scope="col">Description</th>
                  </tr>
                </thead>

                <tbody>
                  {% for sio in static_ip_objects %}
                    <tr>
                      <td class="ps-2">{{ sio.id|intcomma }}</td>
                      <td>{{sio.computer_name}}</td>
                      <td>{{sio.real_name}}</td>
                      <td>{%if sio.address %}{{sio.address}}{% endif %}</td>  {% comment %}JUMP_1 if statement because the field is allowed to be left blank in database{% endcomment %}
                      <td>{%if sio.virtual_address %}{{sio.virtual_address}}{% endif %}</td>  {% comment %}JUMP_1 if statement because the field is allowed to be left blank in database{% endcomment %}
                      <td>{%if sio.description %}{{sio.description}}{% endif %}</td>  {% comment %}JUMP_1 if statement because the field is allowed to be left blank in database{% endcomment %}
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{static_ip_objects|length|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    {# static ip / #}

    {# public ip #}
    {% if public_ip_objects %}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">
              {% include '00-header-title.html' with mode="no-date" ttl="Public IP" %}
            </div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th class="ps-2" scope="col">ID</th>
                    <th scope="col">Address</th>
                    <th scope="col">Description</th>
                  </tr>
                </thead>

                <tbody>
                  {% for pio in public_ip_objects %}
                    <tr>
                      <td class="ps-2">{{ pio.id|intcomma }}</td>
                      <td>{{pio.address}}</td>
                      <td>{%if pio.description %}{{pio.description}}{% endif %}</td>  {% comment %}JUMP_1 if statement because the field is allowed to be left blank in database{% endcomment %}
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{public_ip_objects|length|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    {# public ip / #}

    {# interface #}
    {% if interface_objects %}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">
              {% include '00-header-title.html' with mode="no-date" ttl="Interface" %}
            </div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th class="ps-2" scope="col">ID</th>
                    <th scope="col">Real Interface</th>
                    <th scope="col">Interface</th>
                    <th scope="col">Sensors</th>
                    <th scope="col">Description</th>
                  </tr>
                </thead>

                <tbody>
                  {% for io in interface_objects %}
                    <tr>
                      <td class="ps-2">{{ io.id|intcomma }}</td>
                      <td>{{io.real_interface}}</td>
                      <td>{{io.interface}}</td>
                      <td>{{io.sensors__}}</td>
                      <td>{%if io.description %}{{io.description}}{% endif %}</td>  {% comment %}JUMP_1 if statement because the field is allowed to be left blank in database{% endcomment %}
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{interface_objects|length|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    {# interface / #}

    {# firewall rule #}
    {% if firewallrule_objects %}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">
              {% include '00-header-title.html' with mode="no-date" ttl="Firewall Rule" %}
            </div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th class="ps-2" scope="col">ID</th>
                    <th scope="col">Tracking ID</th>
                    <th scope="col">Name</th>
                    <th scope="col">Interface</th>
                    <th scope="col">Description</th>
                  </tr>
                </thead>

                <tbody>
                  {% for fro in firewallrule_objects %}
                    <tr>
                      <td class="ps-2">{{ fro.id|intcomma }}</td>
                      <td>{{fro.tracking_id}}</td>
                      <td>{{fro.name}}</td>
                      <td>{{fro.interface__}}</td>
                      <td>{%if fro.descriptfron %}{{fro.descriptfron}}{% endif %}</td>  {% comment %}JUMP_1 if statement because the field is allowed to be left blank in database{% endcomment %}
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{firewallrule_objects|length|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    {# interface / #}

    {# gateway #}
    {% if gateway_objects %}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">
              {% include '00-header-title.html' with mode="no-date" ttl="Gateway" %}
            </div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th class="ps-2" scope="col">ID</th>
                    <th scope="col">Name</th>
                    <th scope="col">Address</th>
                    <th scope="col">Description</th>
                  </tr>
                </thead>

                <tbody>
                  {% for go in gateway_objects %}
                    <tr>
                      <td class="ps-2">{{ go.id|intcomma }}</td>
                      <td>{{go.name}}</td>
                      <td>{{go.address}}</td>
                      <td>{%if go.description %}{{go.description}}{% endif %}</td>  {% comment %}JUMP_1 if statement because the field is allowed to be left blank in database{% endcomment %}
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{gateway_objects|length|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    {# gateway / #}

    {# windowsserver #}
    {% if windowsserver_objects %}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">
              {% include '00-header-title.html' with mode="no-date" ttl="Windows Server" %}
            </div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th class="ps-2" scope="col">ID</th>
                    <th scope="col">Name</th>
                    <th scope="col">Address</th>
                    <th scope="col">Description</th>
                  </tr>
                </thead>

                <tbody>
                  {% for ws_o in windowsserver_objects %}
                    <tr>
                      <td class="ps-2">{{ ws_o.id|intcomma }}</td>
                      <td>{{ws_o.name}}</td>
                      <td>{{ws_o.address}}</td>
                      <td>{%if ws_o.description %}{{ws_o.description}}{% endif %}</td>  {% comment %}JUMP_1 if statement because the field is allowed to be left blank in database{% endcomment %}
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{windowsserver_objects|length|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    {# windowsserver / #}

    {# switch #}
    {% if switch_objects %}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">
              {% include '00-header-title.html' with mode="no-date" ttl="Switch" %}
            </div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th class="ps-2" scope="col">ID</th>
                    <th scope="col">Name</th>
                    <th scope="col">Address</th>
                    <th scope="col">Description</th>
                  </tr>
                </thead>

                <tbody>
                  {% for s_o in switch_objects %}
                    <tr>
                      <td class="ps-2">{{ s_o.id|intcomma }}</td>
                      <td>{{s_o.name}}</td>
                      <td>{{s_o.address}}</td>
                      <td>{%if s_o.description %}{{s_o.description}}{% endif %}</td>  {% comment %}JUMP_1 if statement because the field is allowed to be left blank in database{% endcomment %}
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{switch_objects|length|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    {# switch / #}

    {# vmware #}
    {% if vmware_objects %}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">
              {% include '00-header-title.html' with mode="no-date" ttl="VMware" %}
            </div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th class="ps-2" scope="col">ID</th>
                    <th scope="col">Name</th>
                    <th scope="col">Address</th>
                    <th scope="col">Description</th>
                  </tr>
                </thead>

                <tbody>
                  {% for v_o in vmware_objects %}
                    <tr>
                      <td class="ps-2">{{ v_o.id|intcomma }}</td>
                      <td>{{v_o.name}}</td>
                      <td>{{v_o.address}}</td>
                      <td>{%if v_o.description %}{{v_o.description}}{% endif %}</td>  {% comment %}JUMP_1 if statement because the field is allowed to be left blank in database{% endcomment %}
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{vmware_objects|length|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    {# vmware / #}

    {# router #}
    {% if router_objects %}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">
              {% include '00-header-title.html' with mode="no-date" ttl="Router" %}
            </div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th class="ps-2" scope="col">ID</th>
                    <th scope="col">Name</th>
                    <th scope="col">Address</th>
                    <th scope="col">Description</th>
                  </tr>
                </thead>

                <tbody>
                  {% for r_o in router_objects %}
                    <tr>
                      <td class="ps-2">{{ r_o.id|intcomma }}</td>
                      <td>{{r_o.name}}</td>
                      <td>{{r_o.address}}</td>
                      <td>{%if r_o.description %}{{r_o.description}}{% endif %}</td>  {% comment %}JUMP_1 if statement because the field is allowed to be left blank in database{% endcomment %}
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{router_objects|length|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    {# router / #}

    {# routerboard #}
    {% if routerboard_objects %}
      <div class="col-xxl-4 col-xl-6 col-12">
        <div class="card custom-card shadow     h-100">
          <div class="card-header justify-content-between">
            <div class="card-title">
              {% include '00-header-title.html' with mode="no-date" ttl="RouterBoard" %}
            </div>
          </div>
          <div class="card-body p-0">
            <div class="low_height">
              <table class="table table-striped table-borderless table-sm">
                <thead class="{{class_for_thead}}">
                  <tr>
                    <th class="ps-2" scope="col">ID</th>
                    <th scope="col">Name</th>
                    <th scope="col">Address</th>
                    <th scope="col">Description</th>
                  </tr>
                </thead>

                <tbody>
                  {% for rb_o in routerboard_objects %}
                    <tr>
                      <td class="ps-2">{{ rb_o.id|intcomma }}</td>
                      <td>{{rb_o.name}}</td>
                      <td>{{rb_o.address}}</td>
                      <td>{%if rb_o.description %}{{rb_o.description}}{% endif %}</td>  {% comment %}JUMP_1 if statement because the field is allowed to be left blank in database{% endcomment %}
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {# from dist/html/task-details.html #}
          <div class="card-footer">
            <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
              <div>
                <span class="text-muted fs-12">NO.</span>
                <span class="fs-12 fw-medium">{{routerboard_objects|length|intcomma}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    {# routerboard / #}

    {# firehol #}
    {% if request.user.is_superuser%}
      {% if firehol_objects %}
        <div class="col-xxl-4 col-xl-6 col-12">
          <div class="card custom-card shadow     h-100">
            <div class="card-header justify-content-between">
              <div class="card-title">
                {% include '00-header-title.html' with mode="no-date" ttl="FireHOL" %}
              </div>
            </div>
            <div class="card-body p-0">
              <div class="low_height">
                <table class="table table-striped table-borderless table-sm">
                  <thead class="{{class_for_thead}}">
                    <tr>
                      <th class="ps-2" scope="col">ID</th>
                      <th scope="col">Name</th>
                      <th scope="col">URL</th>
                      <th scope="col">Description</th>
                    </tr>
                  </thead>

                  <tbody>
                    {% for fh_o in firehol_objects %}
                      <tr>
                        <td class="ps-2">{{ fh_o.id|intcomma }}</td>
                        <td>{{fh_o.name}}</td>
                        <td><a target="_blank" href="{{fh_o.url}}">Link</a></td>
                        <td>{%if fh_o.description %}{{fh_o.description}}{% endif %}</td>  {% comment %}JUMP_1 if statement because the field is allowed to be left blank in database{% endcomment %}
                      </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
            </div>
            {# from dist/html/task-details.html #}
            <div class="card-footer">
              <div class="d-flex align-items-center justify-content-between gap-2 flex-wrap">
                <div>
                  <span class="text-muted fs-12">NO.</span>
                  <span class="fs-12 fw-medium">{{firehol_objects|length|intcomma}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      {% endif %}
    {% endif %}
    {# firehol / #}
  </div>
{% endblock content %}


{% block scripts %}
  {% include '00-js-for-swiper.html' %}
  <script src="{% static "udon/dist/assets/js/stocks-dashboard.js" %}"></script>
{% endblock scripts %}
