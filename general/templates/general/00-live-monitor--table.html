{% load tags-filters %}
{% load humanize %}


{# top row #}
<div class="align-items-center mb-3 live_monitor_info">
  <div class="card border-0 mb-0">
    <div class="card-body p-2 py-0">
      {# borrowed from widgets.html at Start:: row-7 #}
      <div class="d-flex flex-wrap justify-content-between">

        {# status #}
        <div class="me-5 d-flex align-items-center">
            <div class="me-2">
                <span
                  class="avatar avatar-rounded bg-info-transparent text-info     btn toggle_live_monitor_status_button"
                  data-bs-toggle="tooltip" data-bs-placement="right" title="Running (Press P to Pause)"
                  onclick="toggleLiveMonitorStatus()"
                >
                  <i class="ri-play-line fs-16 toggle_live_monitor_status_button_icon"></i>
                </span>
            </div>
            <div class="flex-fill">
                <p class="fs-18 mb-0 text-dark fw-medium">Status</p>
                <span class="text-muted fs-12 live_monitor_status">Running</span>
            </div>
        </div>

        {# date #}
        <div class="me-5 d-flex align-items-center">
            <div class="me-2">
                <span class="avatar avatar-rounded bg-info-transparent text-info"><i class="ri-calendar-line fs-16"></i></span>
            </div>
            <div class="flex-fill">
                <p class="fs-18 mb-0 text-dark fw-medium">Date</p>
                <span class="text-muted fs-12">{{today_ymd}}</span>
            </div>
        </div>

        {# updated at #}
        <div class="me-5 d-flex align-items-center">
            <div class="me-2">
                <span class="avatar avatar-rounded bg-info-transparent text-info"><i class="ri-time-line fs-16"></i></span>
            </div>
            <div class="flex-fill">
                <p class="fs-18 mb-0 text-dark fw-medium">Started At</p>
                <span class="text-muted fs-12">{{started_at}}</span>
            </div>
        </div>

        {# updated at #}
        <div class="me-5 d-flex align-items-center">
            <div class="me-2">
                <span class="avatar avatar-rounded bg-info-transparent text-info"><i class="ri-time-line fs-16"></i></span>
            </div>
            <div class="flex-fill">
                <p class="fs-18 mb-0 text-dark fw-medium">Updated At</p>
                <span class="text-muted fs-12">{{updated_at}}</span>
            </div>
        </div>

        {# elapsed #}
        <div class="me-5 d-flex align-items-center">
            <div class="me-2">
                <span class="avatar avatar-rounded bg-info-transparent text-info"><i class="ri-timer-line fs-16"></i></span>
            </div>
            <div class="flex-fill">
                <p class="fs-18 mb-0 text-dark fw-medium">Elapsed</p>
                <span class="text-muted fs-12">{{elapsed}}</span>
            </div>
        </div>

        {# updates #}
        <div class="me-5 d-flex align-items-center">
            <div class="me-2">
                <span class="avatar avatar-rounded bg-info-transparent text-info"><i class="ri-restart-line fs-16"></i></span>
            </div>
            <div class="flex-fill">
                <p class="fs-18 mb-0 text-dark fw-medium">Updates</p>
                <span class="text-muted fs-12">{{updatescount|intcomma}}</span>
            </div>
        </div>

      </div>
    </div>
  </div>
</div>
{# top row / #}

{# table #}
<table class="table table-striped table-borderless table-sm">
  <thead class="{{class_for_thead}} d-none">
    <tr>
      <th scope="col"></th>
      <th scope="col"></th>
    </tr>
  </thead>
  <tbody>
    {% for line in lines %}
      <tr
        {# is warning/critical line #}
        {% with line|striptags|is_warning_or_critical_line as warn_or_crit %}
          {% if   warn_or_crit == "is_warning"  %} class="text-warning"
          {% elif warn_or_crit == "is_critical" %} class="text-danger"
          {% endif %}
        {% endwith %}
      >
        <td>{{ forloop.counter }}</td>
        <td>{{ line }}</td>
      </tr>
    {% empty %}
      {% include '00-no-results-found.html' %}
    {% endfor %}
  </tbody>
</table>
{# table /#}


{# set cookies so they'll be reused by python #}
<script>
  document.cookie="updatescount=" + "{{ updatescount }}";
</script>
