{% extends 'dashboard.html' %}
{% load static %}


{% block content %}
  {% include 'general/00-live-monitor--inside-content.html' %}
{% endblock content %}


{% block scripts %}
  <script type="module" src="{% static "js/toggle-live-monitor-button.js" %}"></script>
  <script>

    // set cookies on start-up
    document.cookie="livemonitorstatus=live_monitor_is_running";
    document.cookie="startedat=" + "{{started_at}}";
    document.cookie="updatescount=-1";
    document.cookie="elapsed=0";

    let paused = false;
    function toggleLiveMonitorStatus() {

      // NOTE keep these variables inside function.
      //      when the page starts up, the following elements do not exist.
      //      they will be created later (i.e. after a few seconds) inside #live_monitor_table
      let toggle_live_monitor_status_button      = document.querySelector(".toggle_live_monitor_status_button");
      let toggle_live_monitor_status_button_icon = document.querySelector(".toggle_live_monitor_status_button_icon");
      let live_monitor_status                    = document.querySelector(".live_monitor_status");

      paused = !paused;

      if (paused) {

        document.cookie="livemonitorstatus=live_monitor_is_paused";

        // toggle_live_monitor_status_button.classList.remove("text-success");
        // toggle_live_monitor_status_button.classList.add("text-secondary");

        toggle_live_monitor_status_button_icon.classList.remove("ri-play-line");
        toggle_live_monitor_status_button_icon.classList.add("ri-pause-line");

        toggle_live_monitor_status_button.title = "Paused (Press P to Play)";  // used as tooltip
        live_monitor_status.innerHTML = "Paused";

      } else {

        document.cookie="livemonitorstatus=live_monitor_is_running";

        // toggle_live_monitor_status_button.classList.remove("text-secondary");
        // toggle_live_monitor_status_button.classList.add("text-success");

        toggle_live_monitor_status_button_icon.classList.remove("ri-pause-line");
        toggle_live_monitor_status_button_icon.classList.add("ri-play-line");

        toggle_live_monitor_status_button.title = "Running (Press P to Play)";  // used as tooltip
        live_monitor_status.innerHTML = "Running";

      }
    }

  </script>
{% endblock scripts %}
