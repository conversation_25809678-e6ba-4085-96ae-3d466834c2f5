<div class="row g-3" id="dropdown_hx_destination">
    <div class="col-12">
        <div class="card custom-card shadow">
            <div class="card-body">
                {# from checkout.html #}
                <ul class="nav nav-tabs tab-style-2 d-sm-flex d-block border-bottom border-block-end-dashed mb-3" id="myTab1" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="live_monitor_table_tab" data-bs-toggle="tab"
                            data-bs-target="#live_monitor_table_tab_pane" type="button" role="tab"
                            aria-controls="live_monitor_table_tab" aria-selected="true"><i
                                class="ri-table-line me-2 align-middle"></i>Table</button>
                    </li>
                </ul>

                <div class="tab-content" id="myTabContent">
                    <div class="tab-pane show active border-0 p-0" id="live_monitor_table_tab_pane" role="tabpanel"
                        aria-labelledby="live_monitor_table_tab_pane" tabindex="0">
                        <div
                          hx-include="#dropdown_form"
                          hx-get="{% url "general-live-monitor-url" %}"
                          hx-trigger="load, every {{refresh_to_show}}s"
                          hx-target="#live_monitor_table"
                          hx-swap="innerHTML"
                        >
                        </div>
                        {% comment %}
                          exceptionally removed full_height class
                          to let div go as low as it needs
                        {% endcomment %}
                        <div id="live_monitor_table"></div>  {# id used above by hx #}
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>


{% if from_dropdown %}
  {% include '00-set-cookies.html' %}
  {% include '00-set-dropdowns.html' %}
{% endif %}
