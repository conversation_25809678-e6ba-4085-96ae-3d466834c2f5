{% load static %}
{% load humanize %}


<div class="row g-3" id="dropdown_hx_destination">

  <div class="col-xxl-8 col-12" id="chart_card">
    {% include '00-chart-level.html' with chart_height="med_height" %}
  </div>

  <div class="col-xxl-4 col-12" id="chart_card">
    {% include '00-chart-doughnut--card.html' with  chart_url_base="snort-graphical-overview-url" chart_title="Source IP" chart_slug="source-ip" chart_mode="aggregated" %}
  </div>

  <div class="col-xxl-8 col-12" id="chart_card">
    {% include '00-chart-malicious-chart.html' with ttl="Malicious - DNS" app_slug="dns" chart_height="med_height" %}
  </div>

  <div class="col-xxl-4 col-12" id="chart_card">
    {% include '00-chart-doughnut--card.html' with chart_url_base="snort-graphical-overview-url" chart_title="Destination Port" chart_slug="destination-port" chart_mode="aggregated" %}
  </div>

  <div class="col-12" id="chart_card">
    {% include '00-chart-graphical-category--multi-date--card.html' with chart_url_base="snort-graphical-category-url" chart_slug="time" chart_title="Time" %}
  </div>

  <div class="col-xl-6" id="chart_card">
    {% include '00-chart-country.html' with chart_height="med_height" chart_title="GeoLocation - Country - Domain" chart_slug="country" geo_mode="domain" %}
  </div>

  <div class="col-xl-6" id="chart_card">
    {% include '00-chart-country.html' with chart_height="med_height" chart_title="GeoLocation - Country - IP" chart_slug="country" geo_mode="ip" %}
  </div>

  {% comment %}
  <div class="col-xxl-4 col-12" id="chart_card">
    {% include '00-swiper--card.html' with swiper_dict=top_malicious_domains swiper_mode="malicious-domain" %}
  </div>

  <div class="col-xxl-3 col-12" id="chart_card">
    {% include '00-swiper--card.html' with swiper_dict=top_malicious_ips swiper_mode="malicious-ip" %}
  </div>
  {% endcomment %}
</div>


{% if from_dropdown %}
  {% comment %}
    NOTE 1. ...
         2. ?version=* is added to prevent caching when loading graph.js
            so that a new version of graphs.js is loaded
            when htmx from dropdown is fired
            (https://stackoverflow.com/a/7413243/14388247)
         3. {% now "YmdGis" %} gives 20231220103114
            (https://docs.djangoproject.com/en/5.1/ref/templates/builtins/#std-templatefilter-date)
  {% endcomment %}
  <script type="module" src="{% static "js/graphs.js" %}?version={% now "YmdGis" %}"></script>

  {% include '00-set-cookies.html' %}
  {% include '00-set-dropdowns.html' %}

  {% include '00-js-for-swiper.html' %}
{% endif %}
