from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
from django.shortcuts import render
from django.utils.safestring import mark_safe

from collections import defaultdict
from datetime import datetime
from os import path
from re import compile, escape as re_escape, IGNORECASE

from rahavard import (
    comes_from_htmx,
    convert_byte,
    convert_second,
    sort_dict,
)

# from dns.views import api__get_rows_from_toptable as dns__api__get_rows_from_toptable

from base.utils_classes import (
    SnortConfig,
)

from base.utils_constants import (
    LAST_LINES,
    LIVE_MONITOR_DB_HEADERS,
    REFRESHES,
    SEARCH_SIGNS,
)

from base.utils import (
    # aggregate_values_of_dicts,
    break_name_of_database,
    # create_date_range,
    get_field_headers_and_values,
    get_parsed_dirs,
    get_rts_dts_dets,
    # get_sum_of_values,
    get_to_shows,
    get_today_ymd,
    lookup_fullinfo,
    read_statistics_file,
    tail_file,
    # trim_dict,
)

from base.models import (
    FireHOL,
    FirewallRule,
    Gateway,
    Interface,
    Module,
    PublicIP,
    Router,
    RouterBoard,
    Sensor,
    StaticIP,
    Switch,
    VMware,
    WindowsServer,
)

from base.handlers import (
    bad_request,
)


APP_TITLE = 'General'
APP_SLUG  = 'general'


@login_required
def homepage(request):
    chosensensorname, \
    date_to_show,     \
    date_end_to_show, \
    from_dropdown,    \
    recent_to_show,   \
    top_to_show = get_to_shows(
        request,
        'chosen-sensor-name',
        'date',
        'date-end',
        'from-dropdown',
        'recent',
        'top',
    )

    main_title = 'Home'

    snort_src_dir = f'{SnortConfig.get_logs_parsed_dir()}/{chosensensorname}'
    snort_parsed_dirs = get_parsed_dirs(snort_src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

    if not snort_parsed_dirs:
        return bad_request(request, error_msg=f'No Databases for {SnortConfig.TITLE.value}')

    recent_to_show, \
    date_to_show, \
    date_end_to_show = get_rts_dts_dets(recent_to_show, date_to_show, date_end_to_show)

    '''
    date_range = create_date_range(date_to_show, date_end_to_show)

    ## malicious domain
    malicious_domains_and_counts = {}
    for d_r in date_range:
        day_dict = dns__api__get_rows_from_toptable(
            request,
            ymd=d_r,
            slug='malicious-domain',  ## chart_slug or table_slug
        )
        malicious_domains_and_counts[d_r] = day_dict
    ##
    top_malicious_domains                    = aggregate_values_of_dicts(malicious_domains_and_counts.values())
    top_malicious_domains__total_before_trim = get_sum_of_values(top_malicious_domains)
    top_malicious_domains__len_before_trim   = len(top_malicious_domains)
    top_malicious_domains                    = trim_dict(top_malicious_domains, top_to_show)
    top_malicious_domains__total_after_trim  = get_sum_of_values(top_malicious_domains)

    ## malicious ip
    malicious_ips_and_counts = {}
    for d_r in date_range:
        day_dict = dns__api__get_rows_from_toptable(
            request,
            ymd=d_r,
            slug='malicious-ip',  ## chart_slug or table_slug
        )
        malicious_ips_and_counts[d_r] = day_dict
    ##
    top_malicious_ips                    = aggregate_values_of_dicts(malicious_ips_and_counts.values())
    top_malicious_ips__total_before_trim = get_sum_of_values(top_malicious_ips)
    top_malicious_ips__len_before_trim   = len(top_malicious_ips)
    top_malicious_ips                    = trim_dict(top_malicious_ips, top_to_show)
    top_malicious_ips__total_after_trim  = get_sum_of_values(top_malicious_ips)
    '''

    if from_dropdown:
        html_file = '00-homepage--inside-content.html'
    else:
        html_file = 'homepage.html'

    return render(
        request,
        f'{APP_SLUG}/{html_file}',
        context={
            'multi_day_report_allowed': True,
            'main_title': main_title,

            'date_end_to_show': date_end_to_show,
            'date_to_show': date_to_show,
            'from_dropdown': from_dropdown,
            'limit_to_show': 0,  ## do NOT comment. level chart needs this
            'recent_to_show': recent_to_show,
            'top_to_show': top_to_show,

            # 'top_malicious_domains':                    top_malicious_domains,
            # 'top_malicious_domains__total_before_trim': top_malicious_domains__total_before_trim,
            # 'top_malicious_domains__len_before_trim':   top_malicious_domains__len_before_trim,
            # 'top_malicious_domains__total_after_trim':  top_malicious_domains__total_after_trim,

            # 'top_malicious_ips':                    top_malicious_ips,
            # 'top_malicious_ips__total_before_trim': top_malicious_ips__total_before_trim,
            # 'top_malicious_ips__len_before_trim':   top_malicious_ips__len_before_trim,
            # 'top_malicious_ips__total_after_trim':  top_malicious_ips__total_after_trim,

            'chosensensorname': chosensensorname,

            ## on homepage, displayed info is mostly based on snort
            ## so let's calculate parsed_dirs based on snort
            'parsed_dirs': snort_parsed_dirs,
        },
    )

@login_required
def overview(request):
    main_title = 'Overview'
    chosensensorname = get_to_shows(request, 'chosen-sensor-name')

    if request.user.is_superuser:
        ## database statistics --------------------

        apps_and_dbsizes = defaultdict(int)

        databases_statistics = read_statistics_file('databases')
        for database, size in databases_statistics.items():
            if database == 'aymdhms':
                continue
            slug, object_name, ymd = break_name_of_database(database)
            apps_and_dbsizes[slug] += size

        apps_and_dbsizes = sort_dict(apps_and_dbsizes, based_on='value', reverse=True)

        apps_and_dbsizes = {
            app_name: [size, convert_byte(size)]
            for app_name, size in apps_and_dbsizes.items()
        }

        ## get total
        total_size_of_dbs = sum(
            v for k, v in databases_statistics.items()
            if not k == 'aymdhms'
        )

        ## logs statistics --------------------

        logs_statistics = read_statistics_file('logs')

        ## get total
        total_size_of_logs = sum(
            v for k, v in logs_statistics.items()
            if not k == 'aymdhms'
        )

        databases_statistics = databases_statistics
        apps_and_dbsizes     = apps_and_dbsizes
        total_size_of_dbs    = total_size_of_dbs

        logs_statistics    = logs_statistics
        total_size_of_logs = total_size_of_logs

        parsed_dates_statistics = read_statistics_file('parsed-dates')
        disk_usage_statistics   = read_statistics_file('disk-usage')

        firehol_objects = FireHOL.active_objects.all()
    else:
        databases_statistics = {}
        apps_and_dbsizes     = {}
        total_size_of_dbs    = 0

        logs_statistics    = {}
        total_size_of_logs = 0

        parsed_dates_statistics = {}
        disk_usage_statistics   = {}

        firehol_objects = []

    return render(
        request,
        f'{APP_SLUG}/overview.html',
        context={
            'main_title': main_title,

            'gateway_objects':       Gateway.active_objects.all(),
            'interface_objects':     Interface.active_objects.all(),
            'firewallrule_objects':  FirewallRule.active_objects.all(),
            'module_objects':        Module.active_objects.all(),
            'public_ip_objects':     PublicIP.active_objects.all(),
            'sensor_objects':        Sensor.active_objects.all(),
            'static_ip_objects':     StaticIP.active_objects.all(),
            'router_objects':        Router.active_objects.all(),
            'routerboard_objects':   RouterBoard.active_objects.all(),
            'switch_objects':        Switch.active_objects.all(),
            'vmware_objects':        VMware.active_objects.all(),
            'windowsserver_objects': WindowsServer.active_objects.all(),

            'firehol_objects': firehol_objects,

            'databases_statistics': databases_statistics,
            'apps_and_dbsizes':     apps_and_dbsizes,
            'total_size_of_dbs':    total_size_of_dbs,

            'logs_statistics':    logs_statistics,
            'total_size_of_logs': total_size_of_logs,

            'parsed_dates_statistics': parsed_dates_statistics,
            'disk_usage_statistics':   disk_usage_statistics,

            'chosensensorname': chosensensorname,
            'parsed_dirs': None,  ## to prevent date picker from showing
            'recents_to_show': None,  ## to prevent recents dropdown from showing
        },
    )

@login_required
def lookup(request):
    chosensensorname, \
    look_up = get_to_shows(
        request,
        'chosen-sensor-name',
        'look-up',
    )

    main_title = 'Lookup'

    if comes_from_htmx(request):
        lookup_full_info = lookup_fullinfo(ip_or_domain=look_up, timeout=2)

        return render(
            request,
            f'{APP_SLUG}/00-lookup--table.html',
            context={
                'is_hx': True,
                'look_up': look_up,
                'lookup_full_info': lookup_full_info,
            },
        )

    return render(
        request,
        f'{APP_SLUG}/lookup.html',
        context={
            'main_title': main_title,
            'is_hx': False,
            'look_up': look_up,
            'parsed_dirs': None,  ## to prevent date picker from showing
            'recents_to_show': None,  ## to prevent recents dropdown from showing
            'chosensensorname': chosensensorname,
        },
    )

@login_required
def live_monitor(request):
    chosensensorname,  \
    from_dropdown,     \
    last_line_to_show, \
    logical_operator,  \
    match_case,        \
    refresh_to_show = get_to_shows(
        request,
        'chosen-sensor-name',
        'from-dropdown',
        'last-line',
        'logical-operator',
        'match-case',
        'refresh',
    )

    ## in live monitor page,
    ## when the page loads up,
    ## we don't want refresh_to_show to be 0
    ## (note: if moments later the user decides
    ##  to manually set refresh_to_show to 0
    ##  using the dropdown, this block won't get triggered)
    if not from_dropdown and not refresh_to_show:
        refresh_to_show = REFRESHES.min_nonzero

    main_title = 'Live Monitor'

    today_ymd = get_today_ymd()

    today_weekday_short = datetime.now().strftime('%a')  ## Sat
    today_log = f'{settings.LOGS_DIR}/{today_ymd}--{today_weekday_short}.log'

    if not path.exists(today_log):
        if comes_from_htmx(request):
            return HttpResponse(f'<h5 class="text-gray text-center py-5 my-5">No Log for {today_ymd}<h5>')
        return bad_request(request, error_msg=f'No Log for {today_ymd}')

    field_headers_and_values = get_field_headers_and_values(request, LIVE_MONITOR_DB_HEADERS)

    if not from_dropdown and comes_from_htmx(request):
        if request.COOKIES.get('livemonitorstatus') == 'live_monitor_is_paused':
            ## return an empty response (https://stackoverflow.com/a/41140880/)
            return HttpResponse(status=204)


        ## handling field_headers_and_values
        ## is different in this function
        ## because we are searching in .log lines
        ## rather than in database rows
        if field_headers_and_values:
            lines = tail_file(today_log, LAST_LINES.max)

            values = []
            for field_header, field_values in field_headers_and_values.items():
                for value in field_values:
                    values.append(
                        ## remove leading/trailing *^$
                        value.strip(f'{SEARCH_SIGNS.asterisk}{SEARCH_SIGNS.caret}{SEARCH_SIGNS.dollar}')
                    )

            matched_lines = []
            for line in lines:
                found = False
                for value in values:
                    if match_case:
                        rgx    = compile(re_escape(value))
                        line_  = str(line)
                        value_ = value
                    else:
                        rgx    = compile(re_escape(value), IGNORECASE)
                        line_  = str(line).lower()
                        value_ = value.lower()

                    if value_ not in line_:
                        continue

                    ## highlight matched word
                    ## (https://stackoverflow.com/a/59245114)
                    line = mark_safe(
                        rgx.sub(
                            lambda _: f'<mark>{_.group()}</mark>',
                            str(line),
                        )
                    )

                    found = True

                if found:
                    matched_lines.append(line)

            lines = matched_lines[-last_line_to_show:]
        else:
            lines = tail_file(today_log, last_line_to_show)

        ## lines = [
        ##     '2023-08-24 13:07:41 Sensor1 (auth/alert) [snort] [1:70856:1] https [Classification:...',
        ##     '2023-08-24 13:07:41 Sensor1 (auth/alert) [snort] [120:3:2] (http_inspect) NO CONTENT-LENGTH...',
        ##     ...
        ## ]

        started_at   = request.COOKIES.get('startedat', '')
        updatescount = int(request.COOKIES.get('updatescount', -1)) + 1

        return render(
            request,
            f'{APP_SLUG}/00-live-monitor--table.html',
            context={
                'elapsed': convert_second(updatescount * refresh_to_show, verbose=False),
                'lines': lines,
                'started_at': started_at,
                'today_ymd': today_ymd,
                'updated_at': datetime.now().strftime('%a %H:%M:%S'),  ## JUMP_6
                'updatescount': updatescount,
            },
        )

    if from_dropdown:
        html_file = '00-live-monitor--inside-content.html'
    else:
        html_file = 'live-monitor.html'

    return render(
        request,
        f'{APP_SLUG}/{html_file}',
        context={
            'is_live_monitor': True,

            'db_headers': LIVE_MONITOR_DB_HEADERS,
            'field_headers_and_values': field_headers_and_values,
            'from_dropdown': from_dropdown,
            'last_line_to_show': last_line_to_show,
            'last_lines': LAST_LINES.values,
            'logical_operator': logical_operator,
            'main_title': main_title,
            'match_case': match_case,
            'parsed_dirs': None,  ## to prevent from being shown in dropdowns
            'recents_to_show': None,  ## to prevent recents dropdown from showing
            'refresh_to_show': refresh_to_show,
            'refreshes_allowed': True,
            'started_at': datetime.now().strftime('%a %H:%M:%S'),  ## JUMP_6
            'today_ymd': today_ymd,

            'chosensensorname': chosensensorname,
        },
    )
