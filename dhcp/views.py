from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render

from functools import lru_cache
from itertools import chain
from re import sub

from MySQLdb import connect
from natsort import natsorted
from rahavard import (
    calculate_offset,
    clear_messages,
    comes_from_htmx,
    create_id_for_htmx_indicator,
    get_percent,
    sort_dict,
)

from base.utils_classes import (
    DHCPConfig,
    MYSQLConfig,
)

from base.utils_constants import (
    LOGICAL_OPERATORS,
    LRU_CACHE_MAXSIZE,
    MAX_KEY_LENGTH,
    REFRESHES,
    SEARCH_SIGNS,
)

from base.utils import (
    aggregate_values_of_dicts,
    create_date_range,
    create_name_of_database,
    create_warning_message,
    get_field_headers_and_values,
    get_id_condition,
    get_name_of_function,
    get_parsed_dirs,
    get_rts_dts_dets,
    get_sum_of_values,
    get_time_condition,
    get_to_shows,
    get_today_ymd,
    highlight_searched_items_in_db_rows,
    paginate,
    trim_dict,
    trim_keys,
)

from base.handlers import (
    bad_request,
)

from base.utils_views import (
    detailed_activity as detailed_activity_,
    overall_activity as overall_activity_,
)

from .all import (
    all_charts,
    all_tables,
)


APP_TITLE = DHCPConfig.TITLE.value
APP_SLUG  = DHCPConfig.SLUG.value


@login_required
def detailed_report(request):
    chosensensorname, \
    date_to_show,     \
    from_dropdown,    \
    latest_id,        \
    limit_to_show,    \
    logical_operator, \
    match_case,       \
    page_number,      \
    refresh_to_show,  \
    time_end_to_show, \
    time_to_show = get_to_shows(
        request,
        'chosen-sensor-name',
        'date',
        'from-dropdown',
        'latest-id',
        'limit',
        'logical-operator',
        'match-case',
        'page',
        'refresh',
        'time-end',
        'time',  ## 18:45

        pick_lowest=True,
    )

    main_title = f'{APP_TITLE} - Detailed Report'

    src_dir = DHCPConfig.get_logs_parsed_dir()
    parsed_dirs = get_parsed_dirs(src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

    if not parsed_dirs:
        return bad_request(request, error_msg=f'No Databases for {APP_TITLE}')

    if not date_to_show:
        date_to_show = parsed_dirs[-1]  ## 2023-05-26

    if date_to_show == get_today_ymd():
        refreshes_allowed = True
        newest_on_top     = True
    else:
        refresh_to_show   = REFRESHES.default
        refreshes_allowed = False
        newest_on_top     = False

    database_name = create_name_of_database(APP_SLUG, date_to_show)

    field_headers_and_values = get_field_headers_and_values(request, DHCPConfig.DB_HEADERS.value)

    ## when we make a change in dropdowns,
    ## we should reset latest_id
    ## to start looking up in whole database
    ## not just in rows with IDs higher than current latest_id
    if from_dropdown:
        latest_id = 0

    if comes_from_htmx(request):
        db_rows = api__get_db_rows_for_detailed_report(
            request,
            database_name,
            date_to_show,
            field_headers_and_values,
            latest_id,
            limit_to_show,
            logical_operator,
            match_case,
            newest_on_top,
            page_number,
            refresh_to_show,
            time_end_to_show,
            time_to_show,
        )

        ## get latest_id__db
        latest_id_changed = False
        if db_rows:
            try:
                if newest_on_top and not refresh_to_show:
                    latest_id__db = db_rows[-1][0]

                    if page_number == 1:
                        latest_id_changed = latest_id__db > latest_id
                    else:
                        latest_id_changed = latest_id__db < latest_id
                else:
                    latest_id__db = db_rows[0][0]

                    latest_id_changed = latest_id__db > latest_id
            except Exception:
                latest_id__db = 0
        else:
            latest_id__db = 0

        if refresh_to_show or newest_on_top:
            ## refresh_to_show exists
            ## but no new rows found in database
            ## since last refresh
            if not latest_id_changed and refresh_to_show and not from_dropdown:
                ## return an empty response (https://stackoverflow.com/a/41140880/)
                return HttpResponse(status=204)

        if from_dropdown:
            html_file = '00-detailed-report--inside-content.html'
        else:
            html_file = '00-detailed-report--rows.html'

        db_rows = highlight_searched_items_in_db_rows(
            db_rows,
            field_headers_and_values,
            match_case,
            DHCPConfig.DB_HEADERS_WITH_INDEXES.value,
            multi_day_report_allowed=False,
        )

        return render(
            request,
            f'{APP_SLUG}/{html_file}',
            context={
                'page_has_scrollable_tables': True,

                'date_to_show': date_to_show,
                'db_headers': DHCPConfig.DB_HEADERS.value,
                'db_rows': db_rows,
                'from_dropdown': from_dropdown,
                'id_for_htmx_indicator': create_id_for_htmx_indicator('detailed_report'),
                'latest_id': latest_id__db,
                'limit_to_show': limit_to_show,
                'logical_operator': logical_operator,
                'match_case': match_case,
                'newest_on_top': newest_on_top,
                'page_number': page_number,
                'refresh_to_show': refresh_to_show,
                'refreshes_allowed': refreshes_allowed,
                'time_end_to_show': time_end_to_show,
                'time_to_show': time_to_show,

                'chosensensorname': chosensensorname,
        })

    return render(
        request,
        f'{APP_SLUG}/detailed-report.html',
        context={
            'main_title': main_title,
            'is_detailed_report': True,
            'multi_day_report_allowed': False,
            'page_has_scrollable_tables': True,

            'date_to_show': date_to_show,
            'db_headers': DHCPConfig.DB_HEADERS.value,
            'field_headers_and_values': field_headers_and_values,
            'latest_id': 0,
            'limit_to_show': limit_to_show,
            'logical_operator': logical_operator,
            'match_case': match_case,
            'newest_on_top': newest_on_top,
            'page_number': 0,  ## set to 0 to prevent page input from showing
            'parsed_dirs': parsed_dirs,
            'recents_to_show': None,  ## to prevent recents dropdown from showing
            'refresh_to_show': refresh_to_show,
            'refreshes_allowed': refreshes_allowed,
            'time_end_to_show': time_end_to_show,
            'time_to_show': time_to_show,

            'chosensensorname': chosensensorname,
        },
    )

@login_required
def search(request):
    chosensensorname, \
    date_end_to_show, \
    date_to_show,     \
    from_dropdown,    \
    limit_to_show,    \
    logical_operator, \
    match_case,       \
    page_number,      \
    recent_to_show,   \
    refresh_to_show,  \
    time_end_to_show, \
    time_to_show = get_to_shows(
        request,
        'chosen-sensor-name',
        'date-end',
        'date',
        'from-dropdown',
        'limit',
        'logical-operator',
        'match-case',
        'page',
        'recent',
        'refresh',
        'time-end',
        'time',  ## 18:45

        pick_lowest=True,
    )

    main_title = f'{APP_TITLE} - Search'

    src_dir = DHCPConfig.get_logs_parsed_dir()
    parsed_dirs = get_parsed_dirs(src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

    if not parsed_dirs:
        return bad_request(request, error_msg=f'No Databases for {APP_TITLE}')

    recent_to_show, \
    date_to_show, \
    date_end_to_show = get_rts_dts_dets(recent_to_show, date_to_show, date_end_to_show)

    date_range = create_date_range(date_to_show, date_end_to_show)

    field_headers_and_values = get_field_headers_and_values(request, DHCPConfig.DB_HEADERS.value)

    ## when we make a change in dropdowns,
    ## we should reset latest_id
    ## to start looking up in whole database
    ## not just in rows with IDs higher than current latest_id
    if from_dropdown:
        latest_id = 0

    if comes_from_htmx(request):
        ########################################
        ## dropdown switches/menus changed

        if from_dropdown and not field_headers_and_values:
            return HttpResponse(status=204)

        ########################################
        ## search input is populated and enter key is pressed

        if from_dropdown and field_headers_and_values:
            return render(
                request,
                f'{APP_SLUG}/00-search--inside-content.html',
                context={
                    'page_has_scrollable_tables': True,

                    'date_end_to_show': date_end_to_show,
                    'date_range': date_range,
                    'date_to_show': date_to_show,
                    'db_headers': DHCPConfig.DB_HEADERS.value,
                    'field_headers_and_values': field_headers_and_values,
                    'from_dropdown': from_dropdown,
                    'id_for_htmx_indicator': create_id_for_htmx_indicator('search', date_to_show),
                    'limit_to_show': limit_to_show,
                    'logical_operator': logical_operator,
                    'match_case': match_case,
                    'page_number': 0,  ## set to 0 so that htmx in table can fire
                    'time_end_to_show': time_end_to_show,
                    'time_to_show': time_to_show,

                    'chosensensorname': chosensensorname,
            })

        ########################################
        ## coming from htmx in table

        database_name = create_name_of_database(APP_SLUG, date_to_show)

        latest_id = 0
        newest_on_top = False
        refresh_to_show = REFRESHES.default
        db_rows = api__get_db_rows_for_detailed_report(
            request,
            database_name,
            logical_operator,
            date_to_show,
            field_headers_and_values,
            latest_id,
            limit_to_show,
            match_case,
            newest_on_top,
            page_number,
            refresh_to_show,
            time_end_to_show,
            time_to_show,
        )

        db_rows = highlight_searched_items_in_db_rows(
            db_rows,
            field_headers_and_values,
            match_case,
            DHCPConfig.DB_HEADERS_WITH_INDEXES.value,
            multi_day_report_allowed=False,
        )

        return render(
            request,
            f'{APP_SLUG}/00-search--rows.html',
            context={
                'page_has_scrollable_tables': True,

                'date_end_to_show': date_end_to_show,
                'date_to_show': date_to_show,
                'db_headers': DHCPConfig.DB_HEADERS.value,
                'db_rows': db_rows,
                # 'from_dropdown': from_dropdown,
                'id_for_htmx_indicator': create_id_for_htmx_indicator('search', date_to_show),
                'limit_to_show': limit_to_show,
                'logical_operator': logical_operator,
                'match_case': match_case,
                'page_number': page_number,
                'time_end_to_show': time_end_to_show,
                'time_to_show': time_to_show,

                'chosensensorname': chosensensorname,
        })

    return render(
        request,
        f'{APP_SLUG}/search.html',
        context={
            'is_search': True,
            'main_title': main_title,
            'multi_day_report_allowed': True,
            'page_has_scrollable_tables': True,

            'date_end_to_show': date_end_to_show,
            'date_range': date_range,
            'date_to_show': date_to_show,
            'db_headers': DHCPConfig.DB_HEADERS.value,
            'field_headers_and_values': field_headers_and_values,
            'limit_to_show': limit_to_show,
            'logical_operator': logical_operator,
            'match_case': match_case,
            'page_number': 0,  ## set to 0 to prevent page input from showing
            'parsed_dirs': parsed_dirs,
            'time_end_to_show': time_end_to_show,
            'time_to_show': time_to_show,

            'chosensensorname': chosensensorname,
        },
    )

@login_required
def combined_overview(request):
    '''a combination of tabular_overview and graphical_overview'''

    chosensensorname, \
    date_end_to_show, \
    date_to_show,     \
    from_dropdown,    \
    limit_to_show,    \
    recent_to_show,   \
    top_to_show  = get_to_shows(
        request,
        'chosen-sensor-name',
        'date-end',
        'date',
        'from-dropdown',
        'limit',
        'recent',
        'top',
    )

    main_title = f'{APP_TITLE} - Combined Overview'

    src_dir = DHCPConfig.get_logs_parsed_dir()
    parsed_dirs = get_parsed_dirs(src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

    if not parsed_dirs:
        return bad_request(request, error_msg=f'No Databases for {APP_TITLE}')

    recent_to_show, \
    date_to_show, \
    date_end_to_show = get_rts_dts_dets(recent_to_show, date_to_show, date_end_to_show)

    if from_dropdown:
        html_file = '00-combined-overview--inside-content.html'
    else:
        html_file = 'combined-overview.html'

    return render(
        request,
        f'{APP_SLUG}/{html_file}',
        context={
            'page_has_scrollable_tables': True,
            'multi_day_report_allowed': True,
            'main_title': main_title,

            'date_end_to_show': date_end_to_show,
            'date_to_show': date_to_show,
            'from_dropdown': from_dropdown,
            'limit_to_show': limit_to_show,
            'page_number': 0,  ## set to 0 to prevent page input from showing
            'recent_to_show': recent_to_show,
            'top_to_show': top_to_show,

            'all_tables': all_tables,
            'all_charts': all_charts,
            'parsed_dirs': parsed_dirs,

            'chosensensorname': chosensensorname,
        },
    )

@login_required
def tabular_overview(request):
    chosensensorname, \
    date_end_to_show, \
    date_to_show,     \
    from_dropdown,    \
    limit_to_show,    \
    overview,         \
    page_number,      \
    recent_to_show,   \
    table_slug = get_to_shows(
        request,
        'chosen-sensor-name',
        'date-end',
        'date',
        'from-dropdown',
        'limit',
        'overview',
        'page',
        'recent',
        'table-slug',
    )

    main_title = f'{APP_TITLE} - Tabular Overview'

    src_dir = DHCPConfig.get_logs_parsed_dir()
    parsed_dirs = get_parsed_dirs(src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

    if not parsed_dirs:
        return bad_request(request, error_msg=f'No Databases for {APP_TITLE}')

    recent_to_show, \
    date_to_show, \
    date_end_to_show = get_rts_dts_dets(recent_to_show, date_to_show, date_end_to_show)

    if comes_from_htmx(request):
        overview_total_no = 0
        overview_sum      = 0
        overview_maximum  = 0
        overview_minimum  = 0
        overview_average  = 0

        if from_dropdown:
            rows = []
            page_number = 0  ## set to 0 to prevent page input from showing
            id_for_htmx_indicator = ''
            html_file = '00-tabular-overview--inside-content.html'
        else:
            table_slug = table_slug.lower()

            main = {}
            for d_r in create_date_range(date_to_show, date_end_to_show):
                day_dict = api__get_rows_from_toptable(
                    request,
                    ymd=d_r,
                    slug=table_slug,  ## chart_slug or table_slug
                )
                main[d_r] = day_dict
            ## main = {
            ##     '2023-06-26': {'192.168.5.221': 2172242, '192.168.5.57': 225469, ...},
            ##     '2023-06-27': {'192.168.5.221': 2512710, '192.168.5.36': 560321, ...},
            ## }

            item_tops       = aggregate_values_of_dicts(main.values())
            item_tops_total = get_sum_of_values(item_tops)

            rows = []

            if overview:
                try:
                    dic_values        = item_tops.values()
                    overview_total_no = len(item_tops)
                    overview_sum      = item_tops_total
                    overview_maximum  = max(dic_values)
                    overview_minimum  = min(dic_values)
                    overview_average  = int(overview_sum / overview_total_no)
                except:
                    pass
                html_file = '00-overview--card-footer.html'
            else:
                if table_slug == 'time':
                    item_tops = sort_dict(item_tops, based_on='key', reverse=False)
                else:
                    item_tops = paginate(item_tops, limit_to_show, page_number)

                id_ = calculate_offset(page_number, limit_to_show)
                for item, count in item_tops.items():
                    id_ += 1
                    percent = get_percent(smaller_number=count, total_number=item_tops_total)
                    rows.append([id_, item, count, percent])

                html_file = '00-tabular-overview--rows.html'

            id_for_htmx_indicator = create_id_for_htmx_indicator(table_slug)

        return render(
            request,
            f'{APP_SLUG}/{html_file}',
            context={
                'page_has_scrollable_tables': True,
                'multi_day_report_allowed': True,

                'date_end_to_show': date_end_to_show,
                'date_to_show': date_to_show,
                'from_dropdown': from_dropdown,
                'limit_to_show': limit_to_show,
                'page_number': page_number,
                'recent_to_show': recent_to_show,

                'all_tables': all_tables,
                'table_slug': table_slug,
                'id_for_htmx_indicator': id_for_htmx_indicator,
                'parsed_dirs': parsed_dirs,
                'rows': rows,

                'overview_total_no': overview_total_no,
                'overview_sum': overview_sum,
                'overview_maximum': overview_maximum,
                'overview_minimum': overview_minimum,
                'overview_average': overview_average,
                'overview_title': f'{main_title}: {get_column_name_and_table_name_from_slug(table_slug)[0]}',
                # 'overview_object': f'Sensor: {chosensensorname}',

                'chosensensorname': chosensensorname,
        })

    return render(
        request,
        f'{APP_SLUG}/tabular-overview.html',
        context={
            'page_has_scrollable_tables': True,
            'multi_day_report_allowed': True,
            'main_title': main_title,

            'limit_to_show': limit_to_show,
            'date_to_show': date_to_show,
            'date_end_to_show': date_end_to_show,
            'recent_to_show': recent_to_show,
            'page_number': 0,  ## set to 0 to prevent page input from showing

            'all_tables': all_tables,
            'parsed_dirs': parsed_dirs,

            'chosensensorname': chosensensorname,
        },
    )

@login_required
def detailed_trend(request, table_slug):
    chosensensorname, \
    date_end_to_show, \
    date_to_show,     \
    from_dropdown,    \
    limit_to_show,    \
    page_number,      \
    recent_to_show = get_to_shows(
        request,
        'chosen-sensor-name',
        'date-end',
        'date',
        'from-dropdown',
        'limit',
        'page',
        'recent',
    )

    table_slug = table_slug.lower()

    src_dir = DHCPConfig.get_logs_parsed_dir()
    parsed_dirs = get_parsed_dirs(src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

    if not parsed_dirs:
        return bad_request(request, error_msg=f'No Databases for {APP_TITLE}')

    column_name, _ = get_column_name_and_table_name_from_slug(table_slug)
    main_title = f'{APP_TITLE} - Detailed Trend: {column_name}'

    recent_to_show, \
    date_to_show, \
    date_end_to_show = get_rts_dts_dets(recent_to_show, date_to_show, date_end_to_show)

    date_range = create_date_range(date_to_show, date_end_to_show)

    if from_dropdown or comes_from_htmx(request):
        main = {}

        if from_dropdown:
            html_file = '00-detailed-trend--inside-content.html'
            page_number = 0
        else:
            html_file = '00-detailed-trend--rows.html'
            for d_r in date_range:
                main[d_r] = api__get_rows_from_toptable(
                    request,
                    ymd=d_r,
                    slug=table_slug,  ## chart_slug or table_slug
                )

            ## main = {
            ##     '2024-12-25': {'DNS Update Request': 1078, 'DNS Update Failed': 1052, ...},
            ##     '2024-12-26': {'DNS Update Failed': 1032, 'Renew': 1008, ...},
            ##     ...
            ## }

            names = [info.keys() for ymd, info in main.items()]
            names = natsorted(set(chain(*names)))
            ## '--> ['DNS record not deleted', 'Database Cleanup Begin', ...]

            main_matrixed = {}
            for name in names:
                main_matrixed[name] = [info.get(name, 0) for ymd, info in main.items()]

            main = paginate(main_matrixed, limit_to_show, page_number)

        return render(
            request,
            f'{APP_SLUG}/{html_file}',
            context={
                'page_has_scrollable_tables': True,

                'date_end_to_show': date_end_to_show,
                'date_to_show': date_to_show,
                'from_dropdown': from_dropdown,
                'limit_to_show': limit_to_show,
                'page_number': page_number,
                'recent_to_show': recent_to_show,

                'column_name': column_name,
                'date_range': date_range,
                'id_for_htmx_indicator': create_id_for_htmx_indicator('detailed_trend', table_slug),
                'main': main,
                'table_slug': table_slug,

                'chosensensorname': chosensensorname,
            },
        )

    return render(
        request,
        f'{APP_SLUG}/detailed-trend.html',
        context={
            'page_has_scrollable_tables': True,
            'multi_day_report_allowed': True,

            'main_title': main_title,

            'date_end_to_show': date_end_to_show,
            'date_to_show': date_to_show,
            'limit_to_show': limit_to_show,
            'page_number': 0,  ## set to 0 to prevent page input from showing
            'recent_to_show': recent_to_show,

            'column_name': column_name,
            'date_range': date_range,
            'parsed_dirs': parsed_dirs,
            'table_slug': table_slug,

            'chosensensorname': chosensensorname,
        },
    )

@login_required
def tabular_category(request, table_slug):
    by_date_ymd,      \
    chosensensorname, \
    date_end_to_show, \
    date_to_show,     \
    from_dropdown,    \
    limit_to_show,    \
    overview,         \
    page_number,      \
    recent_to_show,   \
    section = get_to_shows(
        request,
        'by-date-ymd',
        'chosen-sensor-name',
        'date-end',
        'date',
        'from-dropdown',
        'limit',
        'overview',
        'page',
        'recent',
        'section',
    )

    table_slug = table_slug.lower()

    src_dir = DHCPConfig.get_logs_parsed_dir()
    parsed_dirs = get_parsed_dirs(src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

    if not parsed_dirs:
        return bad_request(request, error_msg=f'No Databases for {APP_TITLE}')

    column_name, _ = get_column_name_and_table_name_from_slug(table_slug)
    main_title = f'{APP_TITLE} - Tabular Category: {column_name}'

    recent_to_show, \
    date_to_show, \
    date_end_to_show = get_rts_dts_dets(recent_to_show, date_to_show, date_end_to_show)

    date_range = create_date_range(date_to_show, date_end_to_show)

    if from_dropdown or comes_from_htmx(request):
        overview_total_no  = 0
        overview_sum       = 0
        overview_maximum   = 0
        overview_minimum   = 0
        overview_average   = 0

        if from_dropdown:
            rows = []
            page_number = 0  ## set to 0 to prevent page input from showing
            id_for_htmx_indicator = ''
            html_file = '00-tabular-category--inside-content.html'
        else:
            if section == 'by-date':
                ## get dictionary for the specific date
                dic = api__get_rows_from_toptable(
                    request,
                    ymd=by_date_ymd,
                    slug=table_slug,  ## chart_slug or table_slug
                )
                ## '--> {'https': 1685698, 'google': 745508, 'ssh': 594496, ...}

                id_for_htmx_indicator = create_id_for_htmx_indicator(section, table_slug, by_date_ymd)

            elif section == 'tops':
                main = {}
                for d_r in create_date_range(date_to_show, date_end_to_show):
                    day_dict = api__get_rows_from_toptable(
                        request,
                        ymd=d_r,
                        slug=table_slug,  ## chart_slug or table_slug
                    )
                    main[d_r] = day_dict
                ## main = {
                ##     '2023-06-26': {'192.168.5.221': 2172242, '192.168.5.57': 225469, ...},
                ##     '2023-06-27': {'192.168.5.221': 2512710, '192.168.5.36': 560321, ...},
                ## }

                ## get aggregate of count for each item (e.g. https) during the date range
                dic = aggregate_values_of_dicts(main.values())
                ## '--> {'https': 1553484, 'http': 363197, 'firefox': 124596, ...}

                id_for_htmx_indicator = create_id_for_htmx_indicator(section)

            dic_total = get_sum_of_values(dic)
            ## '--> 2587049 (will be used to calculate percent for each item)

            rows = []

            if overview:
                try:
                    dic_values        = dic.values()
                    overview_total_no = len(dic)
                    overview_sum      = dic_total
                    overview_maximum  = max(dic_values)
                    overview_minimum  = min(dic_values)
                    overview_average  = int(overview_sum / overview_total_no)
                except:
                    pass
                html_file = '00-overview--card-footer.html'
            else:
                if table_slug == 'time':
                    dic = sort_dict(dic, based_on='key', reverse=False)
                else:
                    dic = paginate(dic, limit_to_show, page_number)

                id_ = calculate_offset(page_number, limit_to_show)
                for item, count in dic.items():
                    id_ += 1
                    percent = get_percent(smaller_number=count, total_number=dic_total)
                    rows.append([id_, item, count, percent])

                html_file = '00-tabular-category--rows.html'

        return render(
            request,
            f'{APP_SLUG}/{html_file}',
            context={
                'page_has_scrollable_tables': True,

                'date_end_to_show': date_end_to_show,
                'date_to_show': date_to_show,
                'from_dropdown': from_dropdown,
                'limit_to_show': limit_to_show,
                'page_number': page_number,
                'recent_to_show': recent_to_show,

                'table_slug': table_slug,
                'column_name': column_name,
                'date_range': date_range,
                'id_for_htmx_indicator': id_for_htmx_indicator,
                'rows': rows,

                'overview_total_no': overview_total_no,
                'overview_sum': overview_sum,
                'overview_maximum': overview_maximum,
                'overview_minimum': overview_minimum,
                'overview_average': overview_average,
                'overview_title': main_title,

                'section': section,  ## tops/by-date
                'by_date_ymd': by_date_ymd,

                'chosensensorname': chosensensorname,
            },
        )

    return render(
        request,
        f'{APP_SLUG}/tabular-category.html',
        context={
            'page_has_scrollable_tables': True,
            'multi_day_report_allowed': True,

            'main_title': main_title,

            'limit_to_show': limit_to_show,
            'date_to_show':     date_to_show,
            'date_end_to_show': date_end_to_show,
            'recent_to_show':   recent_to_show,
            'page_number': 0,  ## set to 0 to prevent page input from showing

            'table_slug': table_slug,
            'column_name': column_name,

            ## to create tables in by-date section
            'date_range': date_range,

            'chosensensorname': chosensensorname,

            'parsed_dirs': parsed_dirs,
        },
    )

@login_required
def graphical_overview(request, mode):
    chart_slug,       \
    chosensensorname, \
    date_end_to_show, \
    date_to_show,     \
    from_dropdown,    \
    recent_to_show,   \
    top_to_show = get_to_shows(
        request,
        'chart-slug',
        'chosen-sensor-name',
        'date-end',
        'date',
        'from-dropdown',
        'recent',
        'top',
    )

    mode = mode.lower()

    main_title = f'{APP_TITLE} - Graphical Overview: {mode.capitalize()}'

    src_dir = DHCPConfig.get_logs_parsed_dir()
    parsed_dirs = get_parsed_dirs(src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

    if not parsed_dirs:
        return bad_request(request, error_msg=f'No Databases for {APP_TITLE}')

    recent_to_show, \
    date_to_show, \
    date_end_to_show = get_rts_dts_dets(recent_to_show, date_to_show, date_end_to_show)

    if chart_slug:
        chart_slug = chart_slug.lower()

        main = {}
        for d_r in create_date_range(date_to_show, date_end_to_show):
            day_dict = api__get_rows_from_toptable(
                request,
                ymd=d_r,
                slug=chart_slug,  ## chart_slug or table_slug
            )
            main[d_r] = day_dict
        ## main = {
        ##     '2023-06-26': {'192.168.5.221': 2172242, '192.168.5.57': 225469, ...},
        ##     '2023-06-27': {'192.168.5.221': 2512710, '192.168.5.36': 560321, ...},
        ## }

        if chart_slug == 'time':
            main = sort_dict(main, based_on='key', reverse=False)

        ## for drilldowns
        if mode == 'daily':
            dates = list(main.keys())
            names = []
            counts = []
            totals = []
            for d in dates:
                date_info = main[d]
                date_names = list(date_info.keys())
                date_counts = list(date_info.values())
                date_total = sum(date_counts)

                names.append(date_names)
                counts.append(date_counts)
                totals.append(date_total)

            ## currently commented because it messes up pie drilldowns
            # if not chart_slug == 'time':
            #     names  = names[:top_to_show]
            #     counts = counts[:top_to_show]

            names_counts = []

        elif mode == 'aggregated':
            ## get aggregate of count for each item (e.g. https) during the date range
            aggregated_ = aggregate_values_of_dicts(main.values())
            ## '--> {'https': 1553484, 'http': 363197, 'firefox': 124596, ...}

            dates  = []
            names  = list(aggregated_.keys())
            counts = list(aggregated_.values())
            totals = []

            if not chart_slug == 'time':
                aggregated_ = trim_dict(aggregated_, top_to_show)

                names  = names[:top_to_show]
                counts = counts[:top_to_show]

            names_counts = [
                {
                    'name': k,
                    'y': v,
                }
                for k, v in aggregated_.items()
            ]

        graph_title, _ = get_column_name_and_table_name_from_slug(chart_slug)
        return JsonResponse(
            data={
                'dates': dates,
                'names': names,
                'names_counts': names_counts,
                'counts': counts,
                'totals': totals,

                ## to be used in graph's subtitle
                'date_to_show': date_to_show,
                'date_end_to_show': date_end_to_show,
                'graph_title': f'{APP_TITLE} - {graph_title}',
                'module_name': None,  ## None because dhcp is module-independent
                'chosensensorname': None,  ## None because dhcp is sensor-independent
                'chosensensorip': None,  ## None because dhcp is sensor-independent
            })

    if from_dropdown:
        html_file = '00-graphical-overview--inside-content.html'
    else:
        html_file = 'graphical-overview.html'

    return render(
        request,
        f'{APP_SLUG}/{html_file}',
        context={
            'multi_day_report_allowed': True,
            'main_title': main_title,

            'date_end_to_show': date_end_to_show,
            'date_to_show': date_to_show,
            'from_dropdown': from_dropdown,
            'recent_to_show': recent_to_show,
            'top_to_show': top_to_show,

            'all_charts': all_charts,
            'mode': mode,
            'parsed_dirs': parsed_dirs,

            'chosensensorname': chosensensorname,
        },
    )

@login_required
def graphical_category(request, chart_slug):
    chosensensorname, \
    date_end_to_show, \
    date_to_show,     \
    from_dropdown,    \
    graph_type,       \
    recent_to_show,   \
    top_to_show = get_to_shows(
        request,
        'chosen-sensor-name',
        'date-end',
        'date',
        'from-dropdown',
        'graph-type',
        'recent',
        'top',
    )

    chart_slug = chart_slug.lower()

    src_dir = DHCPConfig.get_logs_parsed_dir()
    parsed_dirs = get_parsed_dirs(src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

    if not parsed_dirs:
        return bad_request(request, error_msg=f'No Databases for {APP_TITLE}')

    graph_title, _ = get_column_name_and_table_name_from_slug(chart_slug)
    main_title = f'{APP_TITLE} - Graphical Category: {graph_title}'

    recent_to_show, \
    date_to_show, \
    date_end_to_show = get_rts_dts_dets(recent_to_show, date_to_show, date_end_to_show)

    if graph_type:
        main = {}
        for d_r in create_date_range(date_to_show, date_end_to_show):
            day_dict = api__get_rows_from_toptable(
                request,
                ymd=d_r,
                slug=chart_slug,  ## chart_slug or table_slug
            )
            main[d_r] = day_dict
        ## main = {
        ##     '2023-06-26': {'192.168.5.221': 2172242, '192.168.5.57': 225469, ...},
        ##     '2023-06-27': {'192.168.5.221': 2512710, '192.168.5.36': 560321, ...},
        ## }

        if graph_type == 'aggregated':
            aggregated_ = aggregate_values_of_dicts(main.values())
            ## '--> {'https': 1553484, 'http': 363197, 'firefox': 124596, ...}

            if chart_slug == 'time':
                aggregated_ = sort_dict(aggregated_, based_on='key', reverse=False)
                ## {'00:00 - 00:59': 345224, '01:00 - 01:59': 357348, '02:00 - 02:59': 319163, ...}

            elif chart_slug in 'question-name':
                aggregated_ = trim_keys(aggregated_, max_key_length=MAX_KEY_LENGTH)

            dates  = list(aggregated_.keys())
            counts = list(aggregated_.values())

            if not chart_slug == 'time':
                dates  = dates[:top_to_show]
                counts = counts[:top_to_show]

            return JsonResponse(
                data={
                    'dates':  dates,
                    'counts': counts,

                    ## to be used in graph's subtitle
                    'date_to_show': date_to_show,
                    'date_end_to_show': date_end_to_show,
                    'graph_title': f'{APP_TITLE} - {graph_title}',
                    'module_name': None,  ## None because dhcp is module-independent
                    'chosensensorname': None,  ## None because dhcp is sensor-independent
                    'chosensensorip': None,  ## None because dhcp is sensor-independent
                })

        elif graph_type == 'date-specific':
            dict_ = main.get(date_to_show, {})  ## {'https': 707341, 'http': 270394, ...},

            if chart_slug in 'question-name':
                dict_ = trim_keys(dict_, max_key_length=MAX_KEY_LENGTH)

            names  = list(dict_.keys())
            counts = list(dict_.values())

            if not chart_slug == 'time':
                names  = names[:top_to_show]
                counts = counts[:top_to_show]

            return JsonResponse(
                data={
                    'names':  names,
                    'counts': counts,

                    ## to be used in graph's subtitle
                    'date_to_show': date_to_show,
                    'date_end_to_show': None,
                    'graph_title': f'{APP_TITLE} - {graph_title}',
                    'module_name': None,  ## None because dhcp is module-independent
                    'chosensensorname': None,  ## None because dhcp is sensor-independent
                    'chosensensorip': None,  ## None because dhcp is sensor-independent
                })

    if chart_slug == 'time':
        top_to_show = 0

    if from_dropdown:
        html_file = '00-graphical-category--inside-content.html'
    else:
        html_file = 'graphical-category.html'

    return render(
        request,
        f'{APP_SLUG}/{html_file}',
        context={
            'multi_day_report_allowed': True,
            'main_title': main_title,

            'date_to_show':     date_to_show,
            'date_end_to_show': date_end_to_show,
            'from_dropdown':    from_dropdown,
            'recent_to_show':   recent_to_show,
            'top_to_show':      top_to_show,

            'chart_slug': chart_slug,
            'column_name': graph_title,
            'graph_type': graph_type,
            'parsed_dirs': parsed_dirs,
            'date_range': create_date_range(date_to_show, date_end_to_show),  ##  used to create cards in 'By Date' section
            'chosensensorname': chosensensorname,
        },
    )

@login_required
def overall_trend(request):
    chosensensorname, \
    date_end_to_show, \
    date_to_show,     \
    from_dropdown,    \
    recent_to_show = get_to_shows(
        request,
        'chosen-sensor-name',
        'date-end',
        'date',
        'from-dropdown',
        'recent',
    )

    main_title = f'{APP_TITLE} - Overall Trend'

    src_dir = DHCPConfig.get_logs_parsed_dir()
    parsed_dirs = get_parsed_dirs(src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

    if not parsed_dirs:
        return bad_request(request, error_msg=f'No Databases for {APP_TITLE}')

    recent_to_show, \
    date_to_show, \
    date_end_to_show = get_rts_dts_dets(recent_to_show, date_to_show, date_end_to_show)

    date_range = create_date_range(date_to_show, date_end_to_show)

    if comes_from_htmx(request):
        main = {}
        for d_r in date_range:
            database_name = create_name_of_database(APP_SLUG, d_r)
            try:
                with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
                    with conn.cursor() as cur:
                        cur.execute('SELECT MAX(ID) FROM timetoptable;')
                        time = cur.fetchone()[0] or 0
                        cur.execute('SELECT MAX(ID) FROM descriptiontoptable;')
                        description = cur.fetchone()[0] or 0
                        cur.execute('SELECT MAX(ID) FROM sourceiptoptable;')
                        source_ip = cur.fetchone()[0] or 0
                        cur.execute('SELECT MAX(ID) FROM hostnametoptable;')
                        host_name = cur.fetchone()[0] or 0
                        cur.execute('SELECT MAX(ID) FROM macaddresstoptable;')
                        mac_addresse = cur.fetchone()[0] or 0

                        main[d_r] = {
                            'time': time,
                            'description': description,
                            'source-ip': source_ip,
                            'host-name': host_name,
                            'mac-address': mac_addresse,
                        }

            except Exception as exc:
                warning_message = create_warning_message(request, APP_TITLE, d_r, get_name_of_function(), exc)
                messages.add_message(request, messages.WARNING, warning_message)
                main[d_r] = {}

        ## main = {
        ##     '2023-08-24': {'real_interface': 145, 'reason': 144, 'action': 13, ...},
        ##     '2023-08-25': {'real_interface': 156, 'reason': 155, 'action': 16, ...},
        ##     ...
        ## }

        main_matrixed = {
            'Time':        [info.get('time',        0) for ymd, info in main.items()],
            'Description': [info.get('description', 0) for ymd, info in main.items()],
            'Source IP':   [info.get('source-ip',   0) for ymd, info in main.items()],
            'Host Name':   [info.get('host-name',   0) for ymd, info in main.items()],
            'MAC Address': [info.get('mac-address', 0) for ymd, info in main.items()],
        }
        ## main_matrixed =
        ## {
        ##     'Time': [24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24],
        ##     'GID:SID': [145, 156, 261, 174, 150, 200, 170, 159, 115, 192, 184, 0, 0, 0],
        ##     'Description': [144, 155, 261, 173, 149, 198, 169, 158, 114, 191, 183, 0, 0, 0],
        ##     ...
        ## }

        main = main_matrixed

        if from_dropdown:
            html_file = '00-overall-trend--inside-content.html'
        else:
            html_file = '00-overall-trend--rows.html'

        return render(
            request,
            f'{APP_SLUG}/{html_file}',
            context={
                'date_end_to_show': date_end_to_show,
                'date_range': date_range,
                'date_to_show': date_to_show,
                'from_dropdown': from_dropdown,
                'main': main,
                'recent_to_show': recent_to_show,

                'chosensensorname': chosensensorname,
            },
        )

    return render(
        request,
        f'{APP_SLUG}/overall-trend.html',
        context={
            'main_title': main_title,
            'multi_day_report_allowed': True,

            'date_end_to_show': date_end_to_show,
            'date_range': date_range,
            'date_to_show': date_to_show,
            'parsed_dirs': parsed_dirs,
            'recent_to_show': recent_to_show,

            'chosensensorname': chosensensorname,
        },
    )

@login_required
def detailed_activity(request):
    chart_slug,       \
    chosensensorname, \
    date_to_show,     \
    from_dropdown = get_to_shows(
        request,
        'chart-slug',
        'chosen-sensor-name',
        'date',
        'from-dropdown',
    )

    return detailed_activity_(
        request=request,
        chart_slug=chart_slug,
        datetoshow=date_to_show,
        from_dropdown=from_dropdown,

        app_title=APP_TITLE,
        app_slug=APP_SLUG,
        src_dir=DHCPConfig.get_logs_parsed_dir(),

        is_sensor_independent=True,

        chosensensorname=chosensensorname,
    )

@login_required
def overall_activity(request):
    chart_slug,       \
    chosensensorname, \
    date_end_to_show, \
    date_to_show,     \
    from_dropdown,    \
    recent_to_show = get_to_shows(
        request,
        'chart-slug',
        'chosen-sensor-name',
        'date-end',
        'date',
        'from-dropdown',
        'recent',
    )

    return overall_activity_(
        request=request,
        chart_slug=chart_slug,
        recenttoshow=recent_to_show,
        datetoshow=date_to_show,
        dateendtoshow=date_end_to_show,
        from_dropdown=from_dropdown,

        app_title=APP_TITLE,
        app_slug=APP_SLUG,
        src_dir=DHCPConfig.get_logs_parsed_dir(),

        is_sensor_independent=True,

        chosensensorname=chosensensorname,
    )

## --------------------------------

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def get_column_name_and_table_name_from_slug(slug: str) -> tuple[str | None, str | None]:
    '''
    Gets a URL-friendly slug and returns a tuple containing the human-readable
    column name and the corresponding database table name.

    Args:
        slug (str): URL-friendly identifier (e.g., 'source-ip')

    Returns:
        tuple: (column_name, table_name) e.g., ('Source IP', 'sourceiptoptable')
               Returns (None, None) if slug is not recognized

    Examples:
        >>> get_column_name_and_table_name_from_slug('source-ip')
        ('Source IP', 'sourceiptoptable')

        >>> get_column_name_and_table_name_from_slug('unknown-slug')
        (None, None)
    '''
    ## __HAS_TEST__

    return {
        ## slug         column_name     table_name
        'time':        ('Time',        'timetoptable'),
        'description': ('Description', 'descriptiontoptable'),
        'source-ip':   ('Source IP',   'sourceiptoptable'),
        'host-name':   ('Host Name',   'hostnametoptable'),
        'mac-address': ('MAC Address', 'macaddresstoptable'),

        'millisecond': ('Millisecond', 'millisecondtoptable'),
    }.get(slug, (None, None))

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def _api__get_rows_from_toptable__cached(ymd, slug, object_name=''):
    column_name, \
    table_name = get_column_name_and_table_name_from_slug(slug)

    if not column_name or not table_name:
        return {}

    try:
        database_name = create_name_of_database(APP_SLUG, ymd, object_name)
        with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:
                cur.execute(f'SELECT `{column_name}`, Count FROM {table_name};')
                day_info = dict(cur)  # .fetchall()

        return day_info
    except Exception as exc_obj:
        return {'is_exc': True, 'exc_obj': exc_obj}

def api__get_rows_from_toptable(request, ymd, slug, object_name=''):
    '''slug is chart_slug or table_slug'''

    day_info = _api__get_rows_from_toptable__cached(ymd, slug, object_name)

    if day_info.get('is_exc', False):
        warning_message = create_warning_message(request, APP_TITLE, ymd, get_name_of_function(), day_info.get('exc_obj'))
        messages.add_message(request, messages.WARNING, warning_message)
        return {}

    return day_info

def api__get_db_rows_for_detailed_report(
    request,
    database_name,
    date_to_show,
    field_headers_and_values,
    latest_id,
    limit_to_show,
    logical_operator,
    match_case,
    newest_on_top,
    page_number,
    refresh_to_show,
    time_end_to_show,
    time_to_show,
):
    ## __BY_AI__ optimized by copilot

    if refresh_to_show or newest_on_top:
        db_offset = 0
        time_condition, time_values = '', []
        id_condition = get_id_condition(field_headers_and_values, latest_id, newest_on_top, refresh_to_show)
    else:
        db_offset = calculate_offset(page_number, limit_to_show)
        time_condition, time_values = get_time_condition(field_headers_and_values, time_to_show, time_end_to_show)
        id_condition = ''

    if newest_on_top:
        order_by_statemnt = 'ORDER BY ID DESC'
    else:
        order_by_statemnt = ''

    try:
        with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:

                ## search in specific column(s)
                if field_headers_and_values:
                    where_condition = ''
                    search_values = []

                    if match_case:
                        case_sensitive = 'BINARY'  ## is case sensitive
                    else:
                        case_sensitive = ''

                    for field_header, field_values in field_headers_and_values.items():
                        field_condition = ''
                        for value in field_values:
                            startswith_asterisk = value.startswith(SEARCH_SIGNS.asterisk)
                            endswith_asterisk   = value.endswith(SEARCH_SIGNS.asterisk)
                            startswith_caret    = value.startswith(SEARCH_SIGNS.caret)
                            endswith_dollar     = value.endswith(SEARCH_SIGNS.dollar)

                            ## add conditions ======================
                            if any([
                                not startswith_asterisk and not endswith_asterisk,  ## guard
                                startswith_caret and endswith_dollar,  ## ^guard$
                                field_header == 'ID',
                            ]):
                                equal_like = '='
                            else:
                                equal_like = 'LIKE'

                            field_condition += f'`{field_header}` {equal_like} {case_sensitive} %s {LOGICAL_OPERATORS.for_field} '

                            ## add placeholders ======================

                            ## remove leading/trailing *^$
                            value = value.strip(f'{SEARCH_SIGNS.asterisk}{SEARCH_SIGNS.caret}{SEARCH_SIGNS.dollar}')

                            if equal_like == '=':
                                search_values.append(value)
                            else:
                                ## use LIKE together with %s (https://stackoverflow.com/a/9244646/)
                                if startswith_asterisk or endswith_asterisk:
                                    if startswith_asterisk and endswith_asterisk:
                                        value_ = f'%{value}%'
                                    elif startswith_asterisk and not endswith_asterisk:
                                        value_ = f'%{value}'
                                    elif not startswith_asterisk and endswith_asterisk:
                                        value_ = f'{value}%'
                                else:
                                    value_ = f'%{value}%'

                                search_values.append(value_)

                        if field_condition:
                            field_condition = sub(f' {LOGICAL_OPERATORS.for_field} $', '', field_condition).strip()
                            ## '--> "Source Port" LIKE ? AND "Description" LIKE ?

                            if where_condition:
                                where_condition += f' {logical_operator} ({field_condition})'
                            else:
                                where_condition = f'({field_condition})'


                    if where_condition:
                        where_condition = sub(f' {logical_operator} $', '', where_condition).strip()
                        ## '--> ("Description" LIKE ? OR "Description" LIKE ?) AND ("Source IP" LIKE ? OR "Source IP" LIKE ?)


                    cur.execute(f'''
                        {DHCPConfig.get_select_statement()}
                        WHERE ({where_condition})
                        {time_condition}
                        {id_condition}
                        {order_by_statemnt}

                        LIMIT %s
                        OFFSET %s
                    ;''', tuple(search_values + time_values + [limit_to_show, db_offset]))

                else:
                    cur.execute(f'''
                        {DHCPConfig.get_select_statement()}
                        {time_condition}
                        {id_condition}
                        {order_by_statemnt}

                        LIMIT %s
                        OFFSET %s
                    ;''', tuple(time_values + [limit_to_show, db_offset]))

                db_rows = cur.fetchall()

    except Exception as exc:
        clear_messages(request)
        warning_message = create_warning_message(request, APP_TITLE, date_to_show, get_name_of_function(), exc)
        messages.warning(request, warning_message)
        db_rows = []

    return db_rows
