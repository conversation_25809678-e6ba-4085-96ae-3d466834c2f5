{% load tags-filters %}


<div class="row g-3" id="dropdown_hx_destination">
  {% create_id_for_htmx_indicator "detailed_report" as id_for_htmx_indicator %}
  <div class="col-12">
    <div class="card custom-card shadow">
      <div class="card-header justify-content-between">
        {# left #}
        <div class="card-title">
          {% include '00-header-title.html' with mode="single-date" ttl=date_to_show daysago=date_to_show|days_ago %}
        </div>
        {# right #}
        <div class="d-flex align-items-center">
          {% if newest_on_top %}
            {% include '00-newest-on-top.html' %}
          {% endif %}
          {% if refreshes_allowed and refresh_to_show %}
            {% include '00-refresh.html' with refresh_to_show=refresh_to_show %}
          {% endif %}
          {% if not refresh_to_show and page_has_scrollable_tables %}
            {% include '00-scrollable-table-icon.html' %}
          {% endif %}
        </div>
      </div>
      <div class="card-body">
        <div class="full_height">
          <table class="table table-striped table-bordered table-sm sortable">
            <thead class="{{class_for_thead}}">
              <tr>
                {% for d_h in db_headers %}
                  <th scope="col">{{d_h}}</th>
                {% endfor %}
              </tr>
            </thead>
            <tbody
              {% if refreshes_allowed and refresh_to_show %}
                hx-include="#dropdown_form"
                hx-get="{% url "dhcp-detailed-report-url" %}"
                hx-vals='{"page": "1"}'
                hx-trigger="load, every {{refresh_to_show}}s"
                hx-swap="afterbegin"
                {% comment %}^^ afterbegin to prepend newest rows to top{% endcomment %}
              {% endif %}
            >
              {% include 'dhcp/00-detailed-report--rows.html' with id_for_htmx_indicator=id_for_htmx_indicator %}
            </tbody>
          </table>

          {% include '00-htmx-indicator.html' with id_for_htmx_indicator=id_for_htmx_indicator %}
        </div>
      </div>
    </div>
  </div>
</div>


{% if from_dropdown %}
  {% include '00-set-cookies.html' %}
  {% include '00-set-dropdowns.html' %}
{% endif %}
