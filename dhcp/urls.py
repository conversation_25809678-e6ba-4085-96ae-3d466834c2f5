from django.urls import path

from . import views


urlpatterns = [
    path('combined-overview/',                   views.combined_overview,  name='dhcp-combined-overview-url'),
    path('detailed-activity/',                   views.detailed_activity,  name='dhcp-detailed-activity-url'),
    path('detailed-report/',                     views.detailed_report,    name='dhcp-detailed-report-url'),
    path('detailed-trend/<str:table_slug>/',     views.detailed_trend,     name='dhcp-detailed-trend-url'),
    path('graphical-category/<str:chart_slug>/', views.graphical_category, name='dhcp-graphical-category-url'),
    path('graphical-overview/<str:mode>/',       views.graphical_overview, name='dhcp-graphical-overview-url'),
    path('overall-activity/',                    views.overall_activity,   name='dhcp-overall-activity-url'),
    path('overall-trend/',                       views.overall_trend,      name='dhcp-overall-trend-url'),
    path('search/',                              views.search,             name='dhcp-search-url'),
    path('tabular-category/<str:table_slug>/',   views.tabular_category,   name='dhcp-tabular-category-url'),
    path('tabular-overview/',                    views.tabular_overview,   name='dhcp-tabular-overview-url'),
]
