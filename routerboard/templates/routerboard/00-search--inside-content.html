{% load tags-filters %}


<div class="row g-3" id="dropdown_hx_destination">
  {% if field_headers_and_values %}
    {% for ymd in date_range %}
      {% create_id_for_htmx_indicator "search" ymd as id_for_htmx_indicator %}
      <div class="col-12">
        <div class="card custom-card shadow">
          <div class="card-header justify-content-between">
            {# left #}
            <div class="card-title">
              {% include '00-header-title.html' with mode="single-date" ttl=ymd daysago=ymd|days_ago %}
            </div>
            {# right #}
            {% if page_has_scrollable_tables %}
              {% include '00-scrollable-table-icon.html' %}
            {% endif %}
          </div>
          <div class="card-body">
            <div class="med_height">
              <table class="table table-striped table-bordered table-sm sortable">
                <thead class="{{class_for_thead}}">
                  <tr>
                    {% for d_h in db_headers %}
                      <th scope="col">{{d_h}}</th>
                    {% endfor %}
                  </tr>
                </thead>
                <tbody>
                  {% include 'routerboard/00-search--rows.html' with date_to_show=ymd date_end_to_show="None" id_for_htmx_indicator=id_for_htmx_indicator %}
                </tbody>
              </table>

              {% include '00-htmx-indicator.html' with id_for_htmx_indicator=id_for_htmx_indicator %}
            </div>
          </div>
        </div>
      </div>
    {% endfor %}
  {% endif %}
</div>


{% if from_dropdown %}
  {% include '00-set-cookies.html' %}
  {% include '00-set-dropdowns.html' %}
{% endif %}
