'''
    This app is a copy of useraudit, but:
        1. all functions take an extra parameter: routerboardname
'''


from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
from django.shortcuts import render

from re import sub

from MySQLdb import connect
from rahavard import (
    calculate_offset,
    comes_from_htmx,
    create_id_for_htmx_indicator,
)

from base.utils_classes import (
    RouterBoardConfig,
    MYSQLConfig,
)

from base.utils_constants import (
    LOGICAL_OPERATORS,
    REFRESHES,
    SEARCH_SIGNS,
)

from base.utils import (
    create_date_range,
    create_name_of_database,
    create_warning_message,
    get_field_headers_and_values,
    get_id_condition,
    get_name_of_function,
    get_parsed_dirs,
    get_rts_dts_dets,
    get_time_condition,
    get_to_shows,
    get_today_ymd,
    highlight_searched_items_in_db_rows,
    paginate,
    remove_id_column,
    reverse_date_range,
)

from base.handlers import (
    bad_request,
)

from base.utils_views import (
    detailed_activity as detailed_activity_,
    overall_activity as overall_activity_,
)


APP_TITLE = RouterBoardConfig.TITLE.value
APP_SLUG  = RouterBoardConfig.SLUG.value


@login_required
def detailed_report(request, routerboardname):
    chosensensorname, \
    date_end_to_show, \
    date_to_show,     \
    from_dropdown,    \
    latest_id,        \
    limit_to_show,    \
    logical_operator, \
    match_case,       \
    page_number,      \
    recent_to_show,   \
    refresh_to_show,  \
    time_end_to_show, \
    time_to_show = get_to_shows(
        request,
        'chosen-sensor-name',
        'date-end',
        'date',
        'from-dropdown',
        'latest-id',
        'limit',
        'logical-operator',
        'match-case',
        'page',
        'recent',
        'refresh',
        'time-end',
        'time',  ## 18:45

        pick_lowest=True,
    )

    main_title = f'{APP_TITLE}: {routerboardname} - Detailed Report'

    src_dir = f'{RouterBoardConfig.get_logs_parsed_dir()}/{routerboardname}'
    parsed_dirs = get_parsed_dirs(src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

    if not parsed_dirs:
        return bad_request(request, error_msg=f'No Databases for {routerboardname}')

    recent_to_show, \
    date_to_show, \
    date_end_to_show = get_rts_dts_dets(recent_to_show, date_to_show, date_end_to_show)

    date_range = create_date_range(date_to_show, date_end_to_show)

    if get_today_ymd() in date_range:
        refreshes_allowed = True
        newest_on_top     = True

        date_range = reverse_date_range(date_range)
    else:
        refresh_to_show   = REFRESHES.default
        refreshes_allowed = False
        newest_on_top     = False

    field_headers_and_values = get_field_headers_and_values(request, RouterBoardConfig.DB_HEADERS.value)

    ## when we make a change in dropdowns,
    ## we should reset latest_id
    ## to start looking up in whole database
    ## not just in rows with IDs higher than current latest_id
    if from_dropdown:
        latest_id = 0

    if comes_from_htmx(request):
        db_rows = []

        for d_r in date_range:
            database_name = create_name_of_database(APP_SLUG, d_r, routerboardname)

            day_db_rows = api__get_db_rows_for_detailed_report(
                request,
                database_name,
                d_r,
                field_headers_and_values,
                latest_id,
                limit_to_show,
                logical_operator,
                match_case,
                newest_on_top,
                page_number,
                refresh_to_show,
                time_end_to_show,
                time_to_show,

                limit_needed=False,
            )

            if day_db_rows:
                db_rows.extend(day_db_rows)

        ## get latest_id__db
        latest_id_changed = False
        if db_rows:
            try:
                if newest_on_top and not refresh_to_show:
                    latest_id__db = db_rows[-1][0]

                    if page_number == 1:
                        latest_id_changed = latest_id__db > latest_id
                    else:
                        latest_id_changed = latest_id__db < latest_id
                else:
                    latest_id__db = db_rows[0][0]

                    latest_id_changed = latest_id__db > latest_id
            except Exception:
                latest_id__db = 0
        else:
            latest_id__db = 0

        if refresh_to_show or newest_on_top:
            ## refresh_to_show exists
            ## but no new rows found in database
            ## since last refresh
            if not latest_id_changed and refresh_to_show and not from_dropdown:
                ## return an empty response (https://stackoverflow.com/a/41140880/)
                return HttpResponse(status=204)

        db_rows = paginate(db_rows, limit_to_show, page_number)
        db_rows = [remove_id_column(_) for _ in db_rows]

        if from_dropdown:
            html_file = '00-detailed-report--inside-content.html'
        else:
            html_file = '00-detailed-report--rows.html'

        db_rows = highlight_searched_items_in_db_rows(
            db_rows,
            field_headers_and_values,
            match_case,
            RouterBoardConfig.DB_HEADERS_WITH_INDEXES.value,
            multi_day_report_allowed=True,
        )

        return render(
            request,
            f'{APP_SLUG}/{html_file}',
            context={
                'page_has_scrollable_tables': True,

                'date_end_to_show': date_end_to_show,
                'date_to_show': date_to_show,
                'db_headers': remove_id_column(RouterBoardConfig.DB_HEADERS.value),
                'db_rows': db_rows,
                'from_dropdown': from_dropdown,
                'id_for_htmx_indicator': create_id_for_htmx_indicator('detailed_report'),
                'latest_id': latest_id__db,
                'limit_to_show': limit_to_show,
                'logical_operator': logical_operator,
                'match_case': match_case,
                'newest_on_top': newest_on_top,
                'page_number': page_number,
                'recent_to_show': recent_to_show,
                'refresh_to_show': refresh_to_show,
                'refreshes_allowed': refreshes_allowed,
                'time_end_to_show': time_end_to_show,
                'time_to_show': time_to_show,

                'chosensensorname': chosensensorname,
                'routerboard_name': routerboardname,
        })

    return render(
        request,
        f'{APP_SLUG}/detailed-report.html',
        context={
            'main_title': main_title,
            'is_detailed_report': True,
            'multi_day_report_allowed': True,
            'page_has_scrollable_tables': True,

            'date_end_to_show': date_end_to_show,
            'date_to_show': date_to_show,
            'db_headers': remove_id_column(RouterBoardConfig.DB_HEADERS.value),
            'field_headers_and_values': field_headers_and_values,
            'latest_id': 0,
            'limit_to_show': limit_to_show,
            'logical_operator': logical_operator,
            'match_case': match_case,
            'newest_on_top': newest_on_top,
            'page_number': 0,  ## set to 0 to prevent page input from showing
            'parsed_dirs': parsed_dirs,
            'recent_to_show': recent_to_show,
            'refresh_to_show': refresh_to_show,
            'refreshes_allowed': refreshes_allowed,
            'time_end_to_show': time_end_to_show,
            'time_to_show': time_to_show,

            'chosensensorname': chosensensorname,
            'routerboard_name': routerboardname,
        },
    )

@login_required
def search(request, routerboardname):
    chosensensorname, \
    date_end_to_show, \
    date_to_show,     \
    from_dropdown,    \
    limit_to_show,    \
    logical_operator, \
    match_case,       \
    page_number,      \
    recent_to_show,   \
    refresh_to_show,  \
    time_end_to_show, \
    time_to_show = get_to_shows(
        request,
        'chosen-sensor-name',
        'date-end',
        'date',
        'from-dropdown',
        'limit',
        'logical-operator',
        'match-case',
        'page',
        'recent',
        'refresh',
        'time-end',
        'time',  ## 18:45

        pick_lowest=True,
    )

    main_title = f'{APP_TITLE}: {routerboardname} - Search'

    src_dir = f'{RouterBoardConfig.get_logs_parsed_dir()}/{routerboardname}'
    parsed_dirs = get_parsed_dirs(src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

    if not parsed_dirs:
        return bad_request(request, error_msg=f'No Databases for {APP_TITLE}')

    recent_to_show, \
    date_to_show, \
    date_end_to_show = get_rts_dts_dets(recent_to_show, date_to_show, date_end_to_show)

    date_range = create_date_range(date_to_show, date_end_to_show)

    field_headers_and_values = get_field_headers_and_values(request, RouterBoardConfig.DB_HEADERS.value)

    if comes_from_htmx(request):
        ########################################
        ## dropdown switches/menus changed

        if from_dropdown and not field_headers_and_values:
            return HttpResponse(status=204)

        ########################################
        ## search input is populated and enter key is pressed

        if from_dropdown and field_headers_and_values:
            return render(
                request,
                f'{APP_SLUG}/00-search--inside-content.html',
                context={
                    'page_has_scrollable_tables': True,

                    'date_end_to_show': date_end_to_show,
                    'date_range': date_range,
                    'date_to_show': date_to_show,
                    'db_headers': RouterBoardConfig.DB_HEADERS.value,
                    'field_headers_and_values': field_headers_and_values,
                    'from_dropdown': from_dropdown,
                    'id_for_htmx_indicator': create_id_for_htmx_indicator('search', date_to_show),
                    'limit_to_show': limit_to_show,
                    'logical_operator': logical_operator,
                    'match_case': match_case,
                    'page_number': 0,  ## set to 0 so that htmx in table can fire
                    'time_end_to_show': time_end_to_show,
                    'time_to_show': time_to_show,

                    'chosensensorname': chosensensorname,
                    'routerboard_name': routerboardname,
            })

        ########################################
        ## coming from htmx in table

        database_name = create_name_of_database(APP_SLUG, date_to_show, routerboardname)

        latest_id = 0
        newest_on_top = False
        refresh_to_show = REFRESHES.default
        db_rows = api__get_db_rows_for_detailed_report(
            request,
            database_name,
            date_to_show,
            field_headers_and_values,
            latest_id,
            limit_to_show,
            logical_operator,
            match_case,
            newest_on_top,
            page_number,
            refresh_to_show,
            time_end_to_show,
            time_to_show,

            limit_needed=True,
        )

        db_rows = highlight_searched_items_in_db_rows(
            db_rows,
            field_headers_and_values,
            match_case,
            RouterBoardConfig.DB_HEADERS_WITH_INDEXES.value,
            multi_day_report_allowed=True,
        )

        return render(
            request,
            f'{APP_SLUG}/00-search--rows.html',
            context={
                'page_has_scrollable_tables': True,

                'date_end_to_show': date_end_to_show,
                'date_to_show': date_to_show,
                'db_headers': RouterBoardConfig.DB_HEADERS.value,
                'db_rows': db_rows,
                # 'from_dropdown': from_dropdown,
                'id_for_htmx_indicator': create_id_for_htmx_indicator('search', date_to_show),
                'limit_to_show': limit_to_show,
                'logical_operator': logical_operator,
                'match_case': match_case,
                'page_number': page_number,
                'time_end_to_show': time_end_to_show,
                'time_to_show': time_to_show,

                'chosensensorname': chosensensorname,
                'routerboard_name': routerboardname,
        })

    return render(
        request,
        f'{APP_SLUG}/search.html',
        context={
            'is_search': True,
            'main_title': main_title,
            'multi_day_report_allowed': True,
            'page_has_scrollable_tables': True,

            'date_end_to_show': date_end_to_show,
            'date_range': date_range,
            'date_to_show': date_to_show,
            'db_headers': RouterBoardConfig.DB_HEADERS.value,
            'field_headers_and_values': field_headers_and_values,
            'limit_to_show': limit_to_show,
            'logical_operator': logical_operator,
            'match_case': match_case,
            'page_number': 0,  ## set to 0 to prevent page input from showing
            'parsed_dirs': parsed_dirs,
            'time_end_to_show': time_end_to_show,
            'time_to_show': time_to_show,

            'chosensensorname': chosensensorname,
            'routerboard_name': routerboardname,
        },
    )

@login_required
def detailed_activity(request, routerboardname):
    chart_slug,       \
    chosensensorname, \
    date_to_show,     \
    from_dropdown = get_to_shows(
        request,
        'chart-slug',
        'chosen-sensor-name',
        'date',
        'from-dropdown',
    )

    return detailed_activity_(
        request=request,
        chart_slug=chart_slug,
        datetoshow=date_to_show,
        from_dropdown=from_dropdown,

        app_title=APP_TITLE,
        app_slug=APP_SLUG,
        src_dir=f'{RouterBoardConfig.get_logs_parsed_dir()}/{routerboardname}',

        is_sensor_independent=True,

        chosensensorname=chosensensorname,
        routerboardname=routerboardname,
    )

@login_required
def overall_activity(request, routerboardname):
    chart_slug,       \
    chosensensorname, \
    date_end_to_show, \
    date_to_show,     \
    from_dropdown,    \
    recent_to_show = get_to_shows(
        request,
        'chart-slug',
        'chosen-sensor-name',
        'date-end',
        'date',
        'from-dropdown',
        'recent',
    )

    return overall_activity_(
        request=request,
        chart_slug=chart_slug,
        recenttoshow=recent_to_show,
        datetoshow=date_to_show,
        dateendtoshow=date_end_to_show,
        from_dropdown=from_dropdown,

        app_title=APP_TITLE,
        app_slug=APP_SLUG,
        src_dir=f'{RouterBoardConfig.get_logs_parsed_dir()}/{routerboardname}',

        is_sensor_independent=True,

        chosensensorname=chosensensorname,
        routerboardname=routerboardname,
    )

## --------------------------------

def api__get_db_rows_for_detailed_report(
    request,
    database_name,
    date_to_show,
    field_headers_and_values,
    latest_id,
    limit_to_show,
    logical_operator,
    match_case,
    newest_on_top,
    page_number,
    refresh_to_show,
    time_end_to_show,
    time_to_show,

    limit_needed,
):
    ## __BY_AI__ optimized by copilot

    if refresh_to_show or newest_on_top:
        # db_offset      = 0
        time_condition, time_values = '', []
        id_condition = get_id_condition(field_headers_and_values, latest_id, newest_on_top, refresh_to_show)
    else:
        # db_offset      = calculate_offset(page_number, limit_to_show)
        time_condition, time_values = get_time_condition(field_headers_and_values, time_to_show, time_end_to_show)
        id_condition   = ''

    if limit_needed:
        db_offset = calculate_offset(page_number, limit_to_show)
        limit_and_offset_expression = '''
            LIMIT %s
            OFFSET %s
        '''
        limit_and_offset_parameters = [limit_to_show, db_offset]
    else:
        limit_and_offset_expression = ''
        limit_and_offset_parameters = []

    if newest_on_top:
        order_by_statemnt = 'ORDER BY ID DESC'
    else:
        order_by_statemnt = ''

    try:
        with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:

                ## search in specific column(s)
                if field_headers_and_values:
                    where_condition = ''
                    search_values = []

                    if match_case:
                        case_sensitive = 'BINARY'  ## is case sensitive
                    else:
                        case_sensitive = ''

                    for field_header, field_values in field_headers_and_values.items():
                        field_condition = ''
                        for value in field_values:
                            startswith_asterisk = value.startswith(SEARCH_SIGNS.asterisk)
                            endswith_asterisk   = value.endswith(SEARCH_SIGNS.asterisk)
                            startswith_caret    = value.startswith(SEARCH_SIGNS.caret)
                            endswith_dollar     = value.endswith(SEARCH_SIGNS.dollar)

                            ## add conditions ======================
                            if any([
                                not startswith_asterisk and not endswith_asterisk,  ## guard
                                startswith_caret and endswith_dollar,  ## ^guard$
                                field_header == 'ID',
                            ]):
                                equal_like = '='
                            else:
                                equal_like = 'LIKE'

                            field_condition += f'`{field_header}` {equal_like} {case_sensitive} %s {LOGICAL_OPERATORS.for_field} '

                            ## add placeholders ======================

                            ## remove leading/trailing *^$
                            value = value.strip(f'{SEARCH_SIGNS.asterisk}{SEARCH_SIGNS.caret}{SEARCH_SIGNS.dollar}')

                            if equal_like == '=':
                                search_values.append(value)
                            else:
                                ## use LIKE together with %s (https://stackoverflow.com/a/9244646/)
                                if startswith_asterisk or endswith_asterisk:
                                    if startswith_asterisk and endswith_asterisk:
                                        value_ = f'%{value}%'
                                    elif startswith_asterisk and not endswith_asterisk:
                                        value_ = f'%{value}'
                                    elif not startswith_asterisk and endswith_asterisk:
                                        value_ = f'{value}%'
                                else:
                                    value_ = f'%{value}%'

                                search_values.append(value_)

                        if field_condition:
                            field_condition = sub(f' {LOGICAL_OPERATORS.for_field} $', '', field_condition).strip()
                            ## '--> "Source Port" LIKE ? AND "Description" LIKE ?

                            if where_condition:
                                where_condition += f' {logical_operator} ({field_condition})'
                            else:
                                where_condition = f'({field_condition})'


                    if where_condition:
                        where_condition = sub(f' {logical_operator} $', '', where_condition).strip()
                        ## '--> ("Description" LIKE ? OR "Description" LIKE ?) AND ("Source IP" LIKE ? OR "Source IP" LIKE ?)


                    cur.execute(f'''
                        {RouterBoardConfig.get_select_statement()}
                        WHERE ({where_condition})
                        {time_condition}
                        {id_condition}
                        {order_by_statemnt}

                        {limit_and_offset_expression}
                    ;''', tuple(search_values + time_values + limit_and_offset_parameters))

                else:
                    cur.execute(f'''
                        {RouterBoardConfig.get_select_statement()}
                        {time_condition}
                        {id_condition}
                        {order_by_statemnt}

                        {limit_and_offset_expression}
                    ;''', tuple(time_values + limit_and_offset_parameters))

                day_db_rows = cur.fetchall()

    except Exception as exc:
        warning_message = create_warning_message(request, APP_TITLE, date_to_show, get_name_of_function(), exc)
        messages.add_message(request, messages.WARNING, warning_message)
        day_db_rows = []

    return day_db_rows
