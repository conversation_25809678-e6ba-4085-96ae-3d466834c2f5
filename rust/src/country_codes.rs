use std::collections::HashMap;

#[derive(Debug)]
pub struct CountryCode {
    pub alpha2: &'static str,
    pub alpha3: &'static str,
}

lazy_static::lazy_static! {
    pub static ref COUNTRY_CODES: HashMap<&'static str, CountryCode> = {
        let mut m = HashMap::new();
    "Anonymous Proxy" => CountryCode { alpha2: "A1", alpha3: "" },
    "Asia/Pacific Region" => CountryCode { alpha2: "AP", alpha3: "" },
    "Europe" => CountryCode { alpha2: "EU", alpha3: "" },
    "Satellite Provider" => CountryCode { alpha2: "A2", alpha3: "" },
    "Côte d'Ivoire" => CountryCode { alpha2: "CI", alpha3: "CIV" },
    "Korea (the Democratic People's Republic of)" => CountryCode { alpha2: "KP", alpha3: "PRK" },
    "Lao People's Democratic Republic (the)" => CountryCode { alpha2: "LA", alpha3: "LAO" },
    "Lao People's Democratic Republic" => CountryCode { alpha2: "LA", alpha3: "LAO" },
    "Afghanistan" => CountryCode { alpha2: "AF", alpha3: "AFG" },
    "Aland Islands" => CountryCode { alpha2: "AX", alpha3: "ALA" },
    "Albania" => CountryCode { alpha2: "AL", alpha3: "ALB" },
    "Algeria" => CountryCode { alpha2: "DZ", alpha3: "DZA" },
    "American Samoa" => CountryCode { alpha2: "AS", alpha3: "ASM" },
    "Andorra" => CountryCode { alpha2: "AD", alpha3: "AND" },
    "Angola" => CountryCode { alpha2: "AO", alpha3: "AGO" },
    "Anguilla" => CountryCode { alpha2: "AI", alpha3: "AIA" },
    "Antarctica" => CountryCode { alpha2: "AQ", alpha3: "ATA" },
    "Antigua and Barbuda" => CountryCode { alpha2: "AG", alpha3: "ATG" },
    "Argentina" => CountryCode { alpha2: "AR", alpha3: "ARG" },
    "Armenia" => CountryCode { alpha2: "AM", alpha3: "ARM" },
    "Aruba" => CountryCode { alpha2: "AW", alpha3: "ABW" },
    "Australia" => CountryCode { alpha2: "AU", alpha3: "AUS" },
    "Austria" => CountryCode { alpha2: "AT", alpha3: "AUT" },
    "Azerbaijan" => CountryCode { alpha2: "AZ", alpha3: "AZE" },
    "Bahamas (the)" => CountryCode { alpha2: "BS", alpha3: "BHS" },
    "Bahamas" => CountryCode { alpha2: "BS", alpha3: "BHS" },
    "Bahrain" => CountryCode { alpha2: "BH", alpha3: "BHR" },
    "Bangladesh" => CountryCode { alpha2: "BD", alpha3: "BGD" },
    "Barbados" => CountryCode { alpha2: "BB", alpha3: "BRB" },
    "Belarus" => CountryCode { alpha2: "BY", alpha3: "BLR" },
    "Belgium" => CountryCode { alpha2: "BE", alpha3: "BEL" },
    "Belize" => CountryCode { alpha2: "BZ", alpha3: "BLZ" },
    "Benin" => CountryCode { alpha2: "BJ", alpha3: "BEN" },
    "Bermuda" => CountryCode { alpha2: "BM", alpha3: "BMU" },
    "Bhutan" => CountryCode { alpha2: "BT", alpha3: "BTN" },
    "Bolivia (Plurinational State of)" => CountryCode { alpha2: "BO", alpha3: "BOL" },
    "Bolivia" => CountryCode { alpha2: "BO", alpha3: "BOL" },
    "Bonaire, Sint Eustatius and Saba" => CountryCode { alpha2: "BQ", alpha3: "BES" },
    "Bonaire, Sint Eustatius, and Saba" => CountryCode { alpha2: "BQ", alpha3: "BES" },
    "Bosnia and Herzegovina" => CountryCode { alpha2: "BA", alpha3: "BIH" },
    "Botswana" => CountryCode { alpha2: "BW", alpha3: "BWA" },
    "Bouvet Island" => CountryCode { alpha2: "BV", alpha3: "BVT" },
    "Brazil" => CountryCode { alpha2: "BR", alpha3: "BRA" },
    "British Indian Ocean Territory (the)" => CountryCode { alpha2: "IO", alpha3: "IOT" },
    "British Indian Ocean Territory" => CountryCode { alpha2: "IO", alpha3: "IOT" },
    "British Virgin Islands" => CountryCode { alpha2: "VG", alpha3: "VGB" },
    "Brunei Darussalam" => CountryCode { alpha2: "BN", alpha3: "BRN" },
    "Brunei" => CountryCode { alpha2: "BN", alpha3: "BRN" },
    "Bulgaria" => CountryCode { alpha2: "BG", alpha3: "BGR" },
    "Burkina Faso" => CountryCode { alpha2: "BF", alpha3: "BFA" },
    "Burundi" => CountryCode { alpha2: "BI", alpha3: "BDI" },
    "Cabo Verde" => CountryCode { alpha2: "CV", alpha3: "CPV" },
    "Cambodia" => CountryCode { alpha2: "KH", alpha3: "KHM" },
    "Cameroon" => CountryCode { alpha2: "CM", alpha3: "CMR" },
    "Canada" => CountryCode { alpha2: "CA", alpha3: "CAN" },
    "Cape Verde" => CountryCode { alpha2: "CV", alpha3: "CPV" },
    "Cayman Islands (the)" => CountryCode { alpha2: "KY", alpha3: "CYM" },
    "Cayman Islands" => CountryCode { alpha2: "KY", alpha3: "CYM" },
    "Central African Republic (the)" => CountryCode { alpha2: "CF", alpha3: "CAF" },
    "Central African Republic" => CountryCode { alpha2: "CF", alpha3: "CAF" },
    "Chad" => CountryCode { alpha2: "TD", alpha3: "TCD" },
    "Chile" => CountryCode { alpha2: "CL", alpha3: "CHL" },
    "China" => CountryCode { alpha2: "CN", alpha3: "CHN" },
    "Christmas Island" => CountryCode { alpha2: "CX", alpha3: "CXR" },
    "Cocos (Keeling) Islands (the)" => CountryCode { alpha2: "CC", alpha3: "CCK" },
    "Cocos (Keeling) Islands" => CountryCode { alpha2: "CC", alpha3: "CCK" },
    "Colombia" => CountryCode { alpha2: "CO", alpha3: "COL" },
    "Comoros (the)" => CountryCode { alpha2: "KM", alpha3: "COM" },
    "Comoros" => CountryCode { alpha2: "KM", alpha3: "COM" },
    "Congo (the Democratic Republic of the)" => CountryCode { alpha2: "CD", alpha3: "COD" },
    "Congo (the)" => CountryCode { alpha2: "CG", alpha3: "COG" },
    "Congo" => CountryCode { alpha2: "CG", alpha3: "COG" },
    "Cook Islands (the)" => CountryCode { alpha2: "CK", alpha3: "COK" },
    "Cook Islands" => CountryCode { alpha2: "CK", alpha3: "COK" },
    "Costa Rica" => CountryCode { alpha2: "CR", alpha3: "CRI" },
    "Croatia" => CountryCode { alpha2: "HR", alpha3: "HRV" },
    "Cuba" => CountryCode { alpha2: "CU", alpha3: "CUB" },
    "Curacao" => CountryCode { alpha2: "CW", alpha3: "CUW" },
    "Curaçao" => CountryCode { alpha2: "CW", alpha3: "CUW" },
    "Cyprus" => CountryCode { alpha2: "CY", alpha3: "CYP" },
    "Czech Republic" => CountryCode { alpha2: "CZ", alpha3: "CZE" },
    "Czechia" => CountryCode { alpha2: "CZ", alpha3: "CZE" },
    "Denmark" => CountryCode { alpha2: "DK", alpha3: "DNK" },
    "Djibouti" => CountryCode { alpha2: "DJ", alpha3: "DJI" },
    "Dominica" => CountryCode { alpha2: "DM", alpha3: "DMA" },
    "Dominican Republic (the)" => CountryCode { alpha2: "DO", alpha3: "DOM" },
    "Dominican Republic" => CountryCode { alpha2: "DO", alpha3: "DOM" },
    "DR Congo" => CountryCode { alpha2: "CD", alpha3: "COD" },
    "East Timor" => CountryCode { alpha2: "TL", alpha3: "TLS" },
    "Ecuador" => CountryCode { alpha2: "EC", alpha3: "ECU" },
    "Egypt" => CountryCode { alpha2: "EG", alpha3: "EGY" },
    "El Salvador" => CountryCode { alpha2: "SV", alpha3: "SLV" },
    "Equatorial Guinea" => CountryCode { alpha2: "GQ", alpha3: "GNQ" },
    "Eritrea" => CountryCode { alpha2: "ER", alpha3: "ERI" },
    "Estonia" => CountryCode { alpha2: "EE", alpha3: "EST" },
    "Eswatini" => CountryCode { alpha2: "SZ", alpha3: "SWZ" },
    "Ethiopia" => CountryCode { alpha2: "ET", alpha3: "ETH" },
    "Falkland Islands (the) [Malvinas]" => CountryCode { alpha2: "FK", alpha3: "FLK" },
    "Falkland Islands" => CountryCode { alpha2: "FK", alpha3: "FLK" },
    "Faroe Islands (the)" => CountryCode { alpha2: "FO", alpha3: "FRO" },
    "Faroe Islands" => CountryCode { alpha2: "FO", alpha3: "FRO" },
    "Federated States of Micronesia" => CountryCode { alpha2: "FM", alpha3: "FSM" },
    "Fiji" => CountryCode { alpha2: "FJ", alpha3: "FJI" },
    "Finland" => CountryCode { alpha2: "FI", alpha3: "FIN" },
    "France" => CountryCode { alpha2: "FR", alpha3: "FRA" },
    "French Guiana" => CountryCode { alpha2: "GF", alpha3: "GUF" },
    "French Polynesia" => CountryCode { alpha2: "PF", alpha3: "PYF" },
    "French Southern Territories (the)" => CountryCode { alpha2: "TF", alpha3: "ATF" },
    "French Southern Territories" => CountryCode { alpha2: "TF", alpha3: "ATF" },
    "Gabon" => CountryCode { alpha2: "GA", alpha3: "GAB" },
    "Gambia (the)" => CountryCode { alpha2: "GM", alpha3: "GMB" },
    "Gambia" => CountryCode { alpha2: "GM", alpha3: "GMB" },
    "Georgia" => CountryCode { alpha2: "GE", alpha3: "GEO" },
    "Germany" => CountryCode { alpha2: "DE", alpha3: "DEU" },
    "Ghana" => CountryCode { alpha2: "GH", alpha3: "GHA" },
    "Gibraltar" => CountryCode { alpha2: "GI", alpha3: "GIB" },
    "Greece" => CountryCode { alpha2: "GR", alpha3: "GRC" },
    "Greenland" => CountryCode { alpha2: "GL", alpha3: "GRL" },
    "Grenada" => CountryCode { alpha2: "GD", alpha3: "GRD" },
    "Guadeloupe" => CountryCode { alpha2: "GP", alpha3: "GLP" },
    "Guam" => CountryCode { alpha2: "GU", alpha3: "GUM" },
    "Guatemala" => CountryCode { alpha2: "GT", alpha3: "GTM" },
    "Guernsey" => CountryCode { alpha2: "GG", alpha3: "GGY" },
    "Guinea" => CountryCode { alpha2: "GN", alpha3: "GIN" },
    "Guinea-Bissau" => CountryCode { alpha2: "GW", alpha3: "GNB" },
    "Guyana" => CountryCode { alpha2: "GY", alpha3: "GUY" },
    "Haiti" => CountryCode { alpha2: "HT", alpha3: "HTI" },
    "Hashemite Kingdom of Jordan" => CountryCode { alpha2: "JO", alpha3: "JOR" },
    "Heard Island 9 and McDonald Islands" => CountryCode { alpha2: "HM", alpha3: "HMD" },
    "Heard Island and McDonald Islands" => CountryCode { alpha2: "HM", alpha3: "HMD" },
    "Holy See (the)" => CountryCode { alpha2: "VA", alpha3: "VAT" },
    "Holy See" => CountryCode { alpha2: "VA", alpha3: "VAT" },
    "Honduras" => CountryCode { alpha2: "HN", alpha3: "HND" },
    "Hong Kong" => CountryCode { alpha2: "HK", alpha3: "HKG" },
    "Hungary" => CountryCode { alpha2: "HU", alpha3: "HUN" },
    "Iceland" => CountryCode { alpha2: "IS", alpha3: "ISL" },
    "India" => CountryCode { alpha2: "IN", alpha3: "IND" },
    "Indonesia" => CountryCode { alpha2: "ID", alpha3: "IDN" },
    "Iran (Islamic Republic of)" => CountryCode { alpha2: "IR", alpha3: "IRN" },
    "Iran" => CountryCode { alpha2: "IR", alpha3: "IRN" },
    "Iraq" => CountryCode { alpha2: "IQ", alpha3: "IRQ" },
    "Ireland" => CountryCode { alpha2: "IE", alpha3: "IRL" },
    "Isle of Man" => CountryCode { alpha2: "IM", alpha3: "IMN" },
    "Israel" => CountryCode { alpha2: "IL", alpha3: "ISR" },
    "Italy" => CountryCode { alpha2: "IT", alpha3: "ITA" },
    "Ivory Coast" => CountryCode { alpha2: "CI", alpha3: "CIV" },
    "Jamaica" => CountryCode { alpha2: "JM", alpha3: "JAM" },
    "Japan" => CountryCode { alpha2: "JP", alpha3: "JPN" },
    "Jersey" => CountryCode { alpha2: "JE", alpha3: "JEY" },
    "Jordan" => CountryCode { alpha2: "JO", alpha3: "JOR" },
    "Kazakhstan" => CountryCode { alpha2: "KZ", alpha3: "KAZ" },
    "Kenya" => CountryCode { alpha2: "KE", alpha3: "KEN" },
    "Kiribati" => CountryCode { alpha2: "KI", alpha3: "KIR" },
    "Korea (the Republic of)" => CountryCode { alpha2: "KR", alpha3: "KOR" },
    "Kosovo" => CountryCode { alpha2: "XK", alpha3: "XKX" },
    "Kuwait" => CountryCode { alpha2: "KW", alpha3: "KWT" },
    "Kyrgyzstan" => CountryCode { alpha2: "KG", alpha3: "KGZ" },
    "Laos" => CountryCode { alpha2: "LA", alpha3: "LAO" },
    "Latvia" => CountryCode { alpha2: "LV", alpha3: "LVA" },
    "Lebanon" => CountryCode { alpha2: "LB", alpha3: "LBN" },
    "Lesotho" => CountryCode { alpha2: "LS", alpha3: "LSO" },
    "Liberia" => CountryCode { alpha2: "LR", alpha3: "LBR" },
    "Libya" => CountryCode { alpha2: "LY", alpha3: "LBY" },
    "Liechtenstein" => CountryCode { alpha2: "LI", alpha3: "LIE" },
    "Lithuania" => CountryCode { alpha2: "LT", alpha3: "LTU" },
    "Luxembourg" => CountryCode { alpha2: "LU", alpha3: "LUX" },
    "Macao" => CountryCode { alpha2: "MO", alpha3: "MAC" },
    "Macedonia (the former Yugoslav Republic of)" => CountryCode { alpha2: "MK", alpha3: "MKD" },
    "Macedonia" => CountryCode { alpha2: "MK", alpha3: "MKD" },
    "Madagascar" => CountryCode { alpha2: "MG", alpha3: "MDG" },
    "Malawi" => CountryCode { alpha2: "MW", alpha3: "MWI" },
    "Malaysia" => CountryCode { alpha2: "MY", alpha3: "MYS" },
    "Maldives" => CountryCode { alpha2: "MV", alpha3: "MDV" },
    "Mali" => CountryCode { alpha2: "ML", alpha3: "MLI" },
    "Malta" => CountryCode { alpha2: "MT", alpha3: "MLT" },
    "Marshall Islands (the)" => CountryCode { alpha2: "MH", alpha3: "MHL" },
    "Marshall Islands" => CountryCode { alpha2: "MH", alpha3: "MHL" },
    "Martinique" => CountryCode { alpha2: "MQ", alpha3: "MTQ" },
    "Mauritania" => CountryCode { alpha2: "MR", alpha3: "MRT" },
    "Mauritius" => CountryCode { alpha2: "MU", alpha3: "MUS" },
    "Mayotte" => CountryCode { alpha2: "YT", alpha3: "MYT" },
    "Mexico" => CountryCode { alpha2: "MX", alpha3: "MEX" },
    "Micronesia (Federated States of)" => CountryCode { alpha2: "FM", alpha3: "FSM" },
    "Micronesia" => CountryCode { alpha2: "FM", alpha3: "FSM" },
    "Moldova (the Republic of)" => CountryCode { alpha2: "MD", alpha3: "MDA" },
    "Moldova" => CountryCode { alpha2: "MD", alpha3: "MDA" },
    "Monaco" => CountryCode { alpha2: "MC", alpha3: "MCO" },
    "Mongolia" => CountryCode { alpha2: "MN", alpha3: "MNG" },
    "Montenegro" => CountryCode { alpha2: "ME", alpha3: "MNE" },
    "Montserrat" => CountryCode { alpha2: "MS", alpha3: "MSR" },
    "Morocco" => CountryCode { alpha2: "MA", alpha3: "MAR" },
    "Mozambique" => CountryCode { alpha2: "MZ", alpha3: "MOZ" },
    "Myanmar [Burma]" => CountryCode { alpha2: "MM", alpha3: "MMR" },
    "Myanmar" => CountryCode { alpha2: "MM", alpha3: "MMR" },
    "Namibia" => CountryCode { alpha2: "NA", alpha3: "NAM" },
    "Nauru" => CountryCode { alpha2: "NR", alpha3: "NRU" },
    "Nepal" => CountryCode { alpha2: "NP", alpha3: "NPL" },
    "Netherlands (the)" => CountryCode { alpha2: "NL", alpha3: "NLD" },
    "Netherlands" => CountryCode { alpha2: "NL", alpha3: "NLD" },
    "New Caledonia" => CountryCode { alpha2: "NC", alpha3: "NCL" },
    "New Zealand" => CountryCode { alpha2: "NZ", alpha3: "NZL" },
    "Nicaragua" => CountryCode { alpha2: "NI", alpha3: "NIC" },
    "Niger (the)" => CountryCode { alpha2: "NE", alpha3: "NER" },
    "Niger" => CountryCode { alpha2: "NE", alpha3: "NER" },
    "Nigeria" => CountryCode { alpha2: "NG", alpha3: "NGA" },
    "Niue" => CountryCode { alpha2: "NU", alpha3: "NIU" },
    "Norfolk Island" => CountryCode { alpha2: "NF", alpha3: "NFK" },
    "North Korea" => CountryCode { alpha2: "KP", alpha3: "PRK" },
    "North Macedonia" => CountryCode { alpha2: "MK", alpha3: "MKD" },
    "Northern Mariana Islands (the)" => CountryCode { alpha2: "MP", alpha3: "MNP" },
    "Northern Mariana Islands" => CountryCode { alpha2: "MP", alpha3: "MNP" },
    "Norway" => CountryCode { alpha2: "NO", alpha3: "NOR" },
    "Oman" => CountryCode { alpha2: "OM", alpha3: "OMN" },
    "Pakistan" => CountryCode { alpha2: "PK", alpha3: "PAK" },
    "Palau" => CountryCode { alpha2: "PW", alpha3: "PLW" },
    "Palestine (State of)" => CountryCode { alpha2: "PS", alpha3: "PSE" },
    "Palestine" => CountryCode { alpha2: "PS", alpha3: "PSE" },
    "Panama" => CountryCode { alpha2: "PA", alpha3: "PAN" },
    "Papua New Guinea" => CountryCode { alpha2: "PG", alpha3: "PNG" },
    "Paraguay" => CountryCode { alpha2: "PY", alpha3: "PRY" },
    "Peru" => CountryCode { alpha2: "PE", alpha3: "PER" },
    "Philippines (the)" => CountryCode { alpha2: "PH", alpha3: "PHL" },
    "Philippines" => CountryCode { alpha2: "PH", alpha3: "PHL" },
    "Pitcairn" => CountryCode { alpha2: "PN", alpha3: "PCN" },
    "Poland" => CountryCode { alpha2: "PL", alpha3: "POL" },
    "Portugal" => CountryCode { alpha2: "PT", alpha3: "PRT" },
    "Puerto Rico" => CountryCode { alpha2: "PR", alpha3: "PRI" },
    "Qatar" => CountryCode { alpha2: "QA", alpha3: "QAT" },
    "Republic of Korea" => CountryCode { alpha2: "KR", alpha3: "KOR" },
    "Republic of Lithuania" => CountryCode { alpha2: "LT", alpha3: "LTU" },
    "Republic of Moldova" => CountryCode { alpha2: "MD", alpha3: "MDA" },
    "Republic of North Macedonia" => CountryCode { alpha2: "MK", alpha3: "MKD" },
    "Republic of the Congo" => CountryCode { alpha2: "CG", alpha3: "COG" },
    "Reunion" => CountryCode { alpha2: "RE", alpha3: "REU" },
    "Romania" => CountryCode { alpha2: "RO", alpha3: "ROU" },
    "Russia" => CountryCode { alpha2: "RU", alpha3: "RUS" },
    "Russian Federation (the)" => CountryCode { alpha2: "RU", alpha3: "RUS" },
    "Russian Federation" => CountryCode { alpha2: "RU", alpha3: "RUS" },
    "Rwanda" => CountryCode { alpha2: "RW", alpha3: "RWA" },
    "Réunion" => CountryCode { alpha2: "RE", alpha3: "REU" },
    "Saint Barthelemy" => CountryCode { alpha2: "BL", alpha3: "BLM" },
    "Saint Barthélemy" => CountryCode { alpha2: "BL", alpha3: "BLM" },
    "Saint Helena (Ascension and Tristan da Cunha)" => CountryCode { alpha2: "SH", alpha3: "SHN" },
    "Saint Helena" => CountryCode { alpha2: "SH", alpha3: "SHN" },
    "Saint Kitts and Nevis" => CountryCode { alpha2: "KN", alpha3: "KNA" },
    "Saint Lucia" => CountryCode { alpha2: "LC", alpha3: "LCA" },
    "Saint Martin (Deutch Part)" => CountryCode { alpha2: "SX", alpha3: "SXM" },
    "Saint Martin (French part)" => CountryCode { alpha2: "MF", alpha3: "MAF" },
    "Saint Martin" => CountryCode { alpha2: "MF", alpha3: "MAF" },
    "Saint Pierre and Miquelon" => CountryCode { alpha2: "PM", alpha3: "SPM" },
    "Saint Vincent and the Grenadines" => CountryCode { alpha2: "VC", alpha3: "VCT" },
    "Samoa" => CountryCode { alpha2: "WS", alpha3: "WSM" },
    "San Marino" => CountryCode { alpha2: "SM", alpha3: "SMR" },
    "Sao Tome and Principe" => CountryCode { alpha2: "ST", alpha3: "STP" },
    "Saudi Arabia" => CountryCode { alpha2: "SA", alpha3: "SAU" },
    "São Tomé and Príncipe" => CountryCode { alpha2: "ST", alpha3: "STP" },
    "Senegal" => CountryCode { alpha2: "SN", alpha3: "SEN" },
    "Serbia" => CountryCode { alpha2: "RS", alpha3: "SRB" },
    "Seychelles" => CountryCode { alpha2: "SC", alpha3: "SYC" },
    "Sierra Leone" => CountryCode { alpha2: "SL", alpha3: "SLE" },
    "Singapore" => CountryCode { alpha2: "SG", alpha3: "SGP" },
    "Sint Maarten (Dutch part)" => CountryCode { alpha2: "SX", alpha3: "SXM" },
    "Sint Maarten" => CountryCode { alpha2: "SX", alpha3: "SXM" },
    "Slovak Republic" => CountryCode { alpha2: "SK", alpha3: "SVK" },
    "Slovakia" => CountryCode { alpha2: "SK", alpha3: "SVK" },
    "Slovenia" => CountryCode { alpha2: "SI", alpha3: "SVN" },
    "Solomon Islands" => CountryCode { alpha2: "SB", alpha3: "SLB" },
    "Somalia" => CountryCode { alpha2: "SO", alpha3: "SOM" },
    "South Africa" => CountryCode { alpha2: "ZA", alpha3: "ZAF" },
    "South Georgia and the South Sandwich Islands" => CountryCode { alpha2: "GS", alpha3: "SGS" },
    "South Korea" => CountryCode { alpha2: "KR", alpha3: "KOR" },
    "South Sudan" => CountryCode { alpha2: "SS", alpha3: "SSD" },
    "Spain" => CountryCode { alpha2: "ES", alpha3: "ESP" },
    "Sri Lanka" => CountryCode { alpha2: "LK", alpha3: "LKA" },
    "St Kitts and Nevis" => CountryCode { alpha2: "KN", alpha3: "KNA" },
    "St Vincent and Grenadines" => CountryCode { alpha2: "VC", alpha3: "VCT" },
    "Sudan (the)" => CountryCode { alpha2: "SD", alpha3: "SDN" },
    "Sudan" => CountryCode { alpha2: "SD", alpha3: "SDN" },
    "Suriname" => CountryCode { alpha2: "SR", alpha3: "SUR" },
    "Svalbard and Jan Mayen" => CountryCode { alpha2: "SJ", alpha3: "SJM" },
    "Swaziland" => CountryCode { alpha2: "SZ", alpha3: "SWZ" },
    "Sweden" => CountryCode { alpha2: "SE", alpha3: "SWE" },
    "Switzerland" => CountryCode { alpha2: "CH", alpha3: "CHE" },
    "Syria" => CountryCode { alpha2: "SY", alpha3: "SYR" },
    "Syrian (Arab Republic)" => CountryCode { alpha2: "SY", alpha3: "SYR" },
    "Taiwan (Province of China)" => CountryCode { alpha2: "TW", alpha3: "TWN" },
    "Taiwan" => CountryCode { alpha2: "TW", alpha3: "TWN" },
    "Tajikistan" => CountryCode { alpha2: "TJ", alpha3: "TJK" },
    "Tanzania" => CountryCode { alpha2: "TZ", alpha3: "TZA" },
    "Tanzania, United Republic of" => CountryCode { alpha2: "TZ", alpha3: "TZA" },
    "Thailand" => CountryCode { alpha2: "TH", alpha3: "THA" },
    "The Netherlands" => CountryCode { alpha2: "NL", alpha3: "NLD" },
    "Timor Leste" => CountryCode { alpha2: "TL", alpha3: "TLS" },
    "Timor-Leste" => CountryCode { alpha2: "TL", alpha3: "TLS" },
    "Togo" => CountryCode { alpha2: "TG", alpha3: "TGO" },
    "Tokelau" => CountryCode { alpha2: "TK", alpha3: "TKL" },
    "Tonga" => CountryCode { alpha2: "TO", alpha3: "TON" },
    "Trinidad and Tobago" => CountryCode { alpha2: "TT", alpha3: "TTO" },
    "Tunisia" => CountryCode { alpha2: "TN", alpha3: "TUN" },
    "Turkey" => CountryCode { alpha2: "TR", alpha3: "TUR" },
    "Turkmenistan" => CountryCode { alpha2: "TM", alpha3: "TKM" },
    "Turks and Caicos Islands (the)" => CountryCode { alpha2: "TC", alpha3: "TCA" },
    "Turks and Caicos Islands" => CountryCode { alpha2: "TC", alpha3: "TCA" },
    "Tuvalu" => CountryCode { alpha2: "TV", alpha3: "TUV" },
    "Türkiye" => CountryCode { alpha2: "TR", alpha3: "TUR" },
    "U.S. Virgin Islands" => CountryCode { alpha2: "VI", alpha3: "VIR" },
    "Uganda" => CountryCode { alpha2: "UG", alpha3: "UGA" },
    "Ukraine" => CountryCode { alpha2: "UA", alpha3: "UKR" },
    "United Arab Emirates (the)" => CountryCode { alpha2: "AE", alpha3: "ARE" },
    "United Arab Emirates" => CountryCode { alpha2: "AE", alpha3: "ARE" },
    "United Kingdom of Great Britain and Northern Ireland (the)" => CountryCode { alpha2: "GB", alpha3: "GBR" },
    "United Kingdom of Great Britain and Northern Ireland" => CountryCode { alpha2: "GB", alpha3: "GBR" },
    "United Kingdom" => CountryCode { alpha2: "GB", alpha3: "GBR" },
    "United Republic of Tanzania" => CountryCode { alpha2: "TZ", alpha3: "TZA" },
    "United States Minor Outlying Islands (the)" => CountryCode { alpha2: "UM", alpha3: "UMI" },
    "United States Minor Outlying Islands" => CountryCode { alpha2: "UM", alpha3: "UMI" },
    "United States of America (the)" => CountryCode { alpha2: "US", alpha3: "USA" },
    "United States of America" => CountryCode { alpha2: "US", alpha3: "USA" },
    "United States" => CountryCode { alpha2: "US", alpha3: "USA" },
    "Uruguay" => CountryCode { alpha2: "UY", alpha3: "URY" },
    "US Virgin Islands" => CountryCode { alpha2: "VI", alpha3: "VIR" },
    "Uzbekistan" => CountryCode { alpha2: "UZ", alpha3: "UZB" },
    "Vanuatu" => CountryCode { alpha2: "VU", alpha3: "VUT" },
    "Vatican" => CountryCode { alpha2: "VA", alpha3: "VAT" },
    "Vatican City" => CountryCode { alpha2: "VA", alpha3: "VAT" },
    "Venezuela (Bolivarian Republic of)" => CountryCode { alpha2: "VE", alpha3: "VEN" },
    "Venezuela" => CountryCode { alpha2: "VE", alpha3: "VEN" },
    "Viet Nam" => CountryCode { alpha2: "VN", alpha3: "VNM" },
    "Vietnam" => CountryCode { alpha2: "VN", alpha3: "VNM" },
    "Virgin Islands (British)" => CountryCode { alpha2: "VG", alpha3: "VGB" },
    "Virgin Islands (U.S.)" => CountryCode { alpha2: "VI", alpha3: "VIR" },
    "Wallis and Futuna" => CountryCode { alpha2: "WF", alpha3: "WLF" },
    "Western Sahara" => CountryCode { alpha2: "EH", alpha3: "ESH" },
    "Yemen" => CountryCode { alpha2: "YE", alpha3: "YEM" },
    "Zambia" => CountryCode { alpha2: "ZM", alpha3: "ZMB" },
    "Zimbabwe" => CountryCode { alpha2: "ZW", alpha3: "ZWE" },
    "Åland Islands" => CountryCode { alpha2: "AX", alpha3: "ALA" },
    "Åland" => CountryCode { alpha2: "AX", alpha3: "ALA" },
        m
    };
}
