// src/utils_constants.rs

pub const PICK_AN_OPTION: &str = "Pick an Option";

// to avoid the error:
// UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa8 in position 5923: invalid start byte
// (https://stackoverflow.com/questions/45529507/unicodedecodeerror-utf-8-codec-cant-decode-byte-0x96-in-position-35-invalid)
// NOTE using encoding='ISO-8859-1' will mess up persian/arabic characters
pub const ACTION_ON_ERROR: &str = "ignore";

pub const MAX_KEY_LENGTH: usize = 20;

pub const MIN_LOG_SIZE: usize = 1024 * 1024 * 20;  // 20 MB

pub const PRINT_ENDPOINT: &str = "\r";

// CACHE_LIFE_SPAN
// LRU_CACHE_MAXSIZE
// BINARY_PATHS
// ON_TRUE
// LOGICAL_OPERATORS
// SEARCH_SIGNS
// LIMITS
// TOPS_TO_SHOW
// REFRESHES
// LAST_LINES
// RECENTS_TO_SHOW
// TIMEOUTS
// MAX_TRIES
// SEASONS_LIST
// MONTHS_LIST
// HTTP_HEADERS
// LIVE_MONITOR_DB_HEADERS
