use dotenv::from_path;
use lazy_static::lazy_static;
use std::env;
use std::path::Path;
use std::sync::Once;


pub const HOUR_KEYS: [&str; 24] = [
    "00:00 - 00:59",
    "01:00 - 01:59",
    "02:00 - 02:59",
    "03:00 - 03:59",
    "04:00 - 04:59",
    "05:00 - 05:59",
    "06:00 - 06:59",
    "07:00 - 07:59",
    "08:00 - 08:59",
    "09:00 - 09:59",
    "10:00 - 10:59",
    "11:00 - 11:59",
    "12:00 - 12:59",
    "13:00 - 13:59",
    "14:00 - 14:59",
    "15:00 - 15:59",
    "16:00 - 16:59",
    "17:00 - 17:59",
    "18:00 - 18:59",
    "19:00 - 19:59",
    "20:00 - 20:59",
    "21:00 - 21:59",
    "22:00 - 22:59",
    "23:00 - 23:59",
];

pub const EVENT_TYPES: [&str; 8] = [
    "(local7/alert)",
    "(local7/crit)",
    "(local7/debug)",
    "(local7/emerg)",
    "(local7/err)",
    "(local7/info)",
    "(local7/notice)",
    "(local7/warning)",
];

pub const EVENT_TYPES_DAEMON: [&str; 8] = [
    "(daemon/alert)",
    "(daemon/crit)",
    "(daemon/debug)",
    "(daemon/emerg)",
    "(daemon/err)",
    "(daemon/info)",
    "(daemon/notice)",
    "(daemon/warning)",
];

lazy_static! {
    pub static ref EVENT_TYPES_CRITICALS: Vec<&'static str> = EVENT_TYPES
        .iter()
        .cloned()
        .filter(|e| e.contains("/alert") || e.contains("/crit") || e.contains("/emerg") || e.contains("/err"))
        .collect();

    pub static ref EVENT_TYPES_WARNINGS: Vec<&'static str> = EVENT_TYPES
        .iter()
        .cloned()
        .filter(|e| e.contains("/warning"))
        .collect();

    pub static ref EVENT_TYPES_DAEMON_CRITICALS: Vec<&'static str> = EVENT_TYPES_DAEMON
        .iter()
        .cloned()
        .filter(|e| e.contains("/alert") || e.contains("/crit") || e.contains("/emerg") || e.contains("/err"))
        .collect();

    pub static ref EVENT_TYPES_DAEMON_WARNINGS: Vec<&'static str> = EVENT_TYPES_DAEMON
        .iter()
        .cloned()
        .filter(|e| e.contains("/warning"))
        .collect();
}

// -------------------------------
// *Config


// load .env
static INIT_ENV: Once = Once::new();
fn ensure_env_loaded() {
    INIT_ENV.call_once(|| {
        let env_path = Path::new("../.env");
        from_path(env_path).expect("Failed to load .env");
    });
}


#[derive(Debug, Clone)]
pub enum MYSQLValue {
    Str(String),
    Int(usize),
    List(Vec<String>),
}


#[derive(Debug)]
pub enum MYSQLConfig {
    ID_DATA_TYPE,
    DATE_DATA_TYPE,
    TIME_DATA_TYPE,
    DEFAULT_DATA_TYPE,
    INDEXED_COLUMN_DATA_TYPE,
    COUNT_DATA_TYPE,
    BUILTIN_DATABASES,
    INFILE_CHUNKSIZE,
    ENCLOSED_BY,
    INDEX_PREFIX_LENGTH,
    INDEX_TYPE,
    TERMINATED_BY,
    DB_NAME_SEPARATOR,
    TABLE_NAME_SEPARATOR,
    POOL_CHUNKSIZE,
    NON_DATED_DATABASES,

    MYSQL_HOST,
    MYSQL_MASTER,
    MYSQL_MASTER_PASSWD,

    MYSQL_R_USER,
    MYSQL_R_USER_PASSWD,
}


impl MYSQLConfig {
    pub fn value(&self) -> MYSQLValue {
        // ✅ always loads .env first
        ensure_env_loaded();

        match self {
            Self::ID_DATA_TYPE => MYSQLValue::Str("INT PRIMARY KEY AUTO_INCREMENT".to_string()),
            Self::DATE_DATA_TYPE => MYSQLValue::Str("VARCHAR(10)".to_string()),
            Self::TIME_DATA_TYPE => MYSQLValue::Str("VARCHAR(13)".to_string()),
            Self::DEFAULT_DATA_TYPE => MYSQLValue::Str("MEDIUMTEXT".to_string()),
            Self::INDEXED_COLUMN_DATA_TYPE => MYSQLValue::Str("VARCHAR(5000)".to_string()),
            Self::COUNT_DATA_TYPE => MYSQLValue::Str("INT".to_string()),
            Self::BUILTIN_DATABASES => MYSQLValue::List(vec![
                "information_schema".to_string(),
                "mysql".to_string(),
                "performance_schema".to_string(),
                "sys".to_string(),
            ]),
            Self::INFILE_CHUNKSIZE => MYSQLValue::Int(5_000_000),
            Self::ENCLOSED_BY => MYSQLValue::Str("".to_string()),
            Self::INDEX_PREFIX_LENGTH => MYSQLValue::Int(100),
            Self::INDEX_TYPE => MYSQLValue::Str("BTREE".to_string()),
            Self::TERMINATED_BY => MYSQLValue::Str("-*@*-".to_string()),
            Self::DB_NAME_SEPARATOR => MYSQLValue::Str("__".to_string()),
            Self::TABLE_NAME_SEPARATOR => MYSQLValue::Str("__".to_string()),
            Self::POOL_CHUNKSIZE => MYSQLValue::Int(100_000),
            Self::NON_DATED_DATABASES => MYSQLValue::List(vec![
                "geolocation".to_string(),
                "malicious".to_string(),
            ]),

            // Loaded from .env
            // __TODO__ use as MASTER_CREDS and R_USER_CREDS dictionaries
            Self::MYSQL_HOST => MYSQLValue::Str(env::var("MYSQL_HOST").unwrap_or_default()),
            Self::MYSQL_MASTER => MYSQLValue::Str(env::var("MYSQL_MASTER").unwrap_or_default()),
            Self::MYSQL_MASTER_PASSWD => MYSQLValue::Str(env::var("MYSQL_MASTER_PASSWD").unwrap_or_default()),
            //
            Self::MYSQL_R_USER => MYSQLValue::Str(env::var("MYSQL_R_USER").unwrap_or_default()),
            Self::MYSQL_R_USER_PASSWD => MYSQLValue::Str(env::var("MYSQL_R_USER_PASSWD").unwrap_or_default()),
        }
    }

    pub fn get_infile_statement() -> &'static str {
        ensure_env_loaded();  // Optional, in case DEBUG is loaded from .env

        match env::var("DEBUG").as_deref() {
            Ok("True") | Ok("true") | Ok("1") => "LOAD DATA LOCAL INFILE",
            _ => "LOAD DATA INFILE",
        }
    }
}


pub mod GeoLocationConfig {
    use super::{MYSQLConfig, MYSQLValue, ensure_env_loaded};
    use std::env;

    pub const TITLE: &str = "GeoLocation";
    pub const SLUG: &str = "geolocation";

    pub const DB_HEADERS_WITH_INDEXES__DOMAIN: &[(&str, usize)] = &[
        ("ID", 0),
        ("Domain", 1),
        ("Country", 2),
        ("Country Code", 3),
        ("Region", 4),
        ("City", 5),
        ("Latitude", 6),
        ("Longitude", 7),
        ("Timezone", 8),
        ("ISP", 9),
        ("Organization", 10),
        ("IP", 11),
    ];

    pub const DB_HEADERS__DOMAIN: [&str; 12] = [
        "ID",
        "Domain",
        "Country",
        "Country Code",
        "Region",
        "City",
        "Latitude",
        "Longitude",
        "Timezone",
        "ISP",
        "Organization",
        "IP",
    ];

    pub fn db_columns_domain() -> String {
        let MYSQLValue::Str(default_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else { panic!("Invalid config") };
        let MYSQLValue::Str(id_type) = MYSQLConfig::ID_DATA_TYPE.value() else { panic!("Invalid config") };

        format!(
            "
            ID             {id_type},
            Domain         {default_type},
            Country        {default_type},
            `Country Code` {default_type},
            Region         {default_type},
            City           {default_type},
            Latitude       {default_type},
            Longitude      {default_type},
            Timezone       {default_type},
            ISP            {default_type},
            Organization   {default_type},
            IP             {default_type}
        "
        )
    }

    pub const DB_KEYS__DOMAIN: &str = "
        Domain,
        Country,
        `Country Code`,
        Region,
        City,
        Latitude,
        Longitude,
        Timezone,
        ISP,
        Organization,
        IP
    ";

    pub const DB_MARKS__DOMAIN: &str = "%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s";

    pub const DB_HEADERS__IP: [&str; 19] = [
        "ID", "IP", "Type", "Continent", "Continent Code", "Country", "Country Code", "City",
        "Latitude", "Longitude", "Is EU", "ASN", "Organization", "ISP", "Domain",
        "Timezone", "Flag Emoji", "Flag Emoji Unicode", "Flag IMG"
    ];

    pub const DB_HEADERS_WITH_INDEXES__IP: &[(&str, usize)] = &[
        ("ID", 0), ("IP", 1), ("Type", 2), ("Continent", 3), ("Continent Code", 4),
        ("Country", 5), ("Country Code", 6), ("City", 7), ("Latitude", 8), ("Longitude", 9),
        ("Is EU", 10), ("ASN", 11), ("Organization", 12), ("ISP", 13), ("Domain", 14),
        ("Timezone", 15), ("Flag Emoji", 16), ("Flag Emoji Unicode", 17), ("Flag IMG", 18)
    ];

    pub fn db_columns_ip() -> String {
        let MYSQLValue::Str(default_type) = MYSQLConfig::DEFAULT_DATA_TYPE.value() else { panic!("Invalid config") };
        let MYSQLValue::Str(id_type) = MYSQLConfig::ID_DATA_TYPE.value() else { panic!("Invalid config") };

        format!(
            "
            ID                   {id_type},
            IP                   {default_type},
            Type                 {default_type},
            Continent            {default_type},
            `Continent Code`     {default_type},
            Country              {default_type},
            `Country Code`       {default_type},
            City                 {default_type},
            Latitude             {default_type},
            Longitude            {default_type},
            `Is EU`              {default_type},
            ASN                  {default_type},
            Organization         {default_type},
            ISP                  {default_type},
            Domain               {default_type},
            Timezone             {default_type},
            `Flag Emoji`         {default_type},
            `Flag Emoji Unicode` {default_type},
            `Flag IMG`           {default_type}
        "
        )
    }

    pub const DB_KEYS__IP: &str = "
        IP,
        Type,
        Continent,
        `Continent Code`,
        Country,
        `Country Code`,
        City,
        Latitude,
        Longitude,
        `Is EU`,
        ASN,
        Organization,
        ISP,
        Domain,
        Timezone,
        `Flag Emoji`,
        `Flag Emoji Unicode`,
        `Flag IMG`
    ";

    pub const DB_MARKS__IP: &str = "%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s";

    // __skipped__ because not used in rust
    // COUNTRY_CODES_DICT = ...

    pub fn get_table_name(geo_mode: &str) -> String {
        let MYSQLValue::Str(sep) = MYSQLConfig::TABLE_NAME_SEPARATOR.value() else {
            panic!("Invalid table name separator");
        };
        format!("{SLUG}table{sep}{geo_mode}")
    }

    pub fn get_logs_parsed_dir() -> String {
        ensure_env_loaded();
        let logs_parsed_dir = env::var("LOGS_PARSED_DIR").unwrap_or_default();
        format!("{logs_parsed_dir}/{SLUG}")
    }

    pub fn get_select_statement(geo_mode: &str) -> String {
        format!("SELECT * FROM {}", get_table_name(geo_mode))
    }
}
