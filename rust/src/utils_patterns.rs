use regex::Regex;
use lazy_static::lazy_static;

// We’ll define the regexes using lazy_static!
// so they are compiled once and reused efficiently.
// for comments and line sample, refer to base/utils_patterns.py
lazy_static! {
    pub static ref INIT_REG: Regex = Regex::new(r"^(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2}:\d{2})\s+(\S+)").unwrap();
    pub static ref EVENT_REG: Regex = Regex::new(r"\s+(\([^)]+\))").unwrap();
    pub static ref ALERT_REG: Regex = Regex::new(r"\s+(\[[^\]]+\])").unwrap();

    pub static ref DAEMON_PATTERN: Regex = Regex::new(
        &format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())
    ).unwrap();

    pub static ref FILTERLOG_PATTERN: Regex = Regex::new(
        &format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())
    ).unwrap();

    pub static ref SNORT_PATTERN: Regex = Regex::new(
        &format!(
            r"{}{}{}\
            \s+\[(\d+:\d+:\d+)\]\
            \s+(.+?)\
            \s+\[Classification: (.+?)\]\
            \s+\[Priority: (\d+)\]\
            \s+\{{(\w+)\}}\
            \s+([\d\.]+)(?::(\d+))?\
            \s+->\
            \s+([\d\.]+)(?::(\d+))?",
            INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str()
        )
    ).unwrap();

    pub static ref SQUID_PATTERN: Regex = Regex::new(
        &format!(
            r"{}{}{}\
            \s+(.+?)\
            \s+(.+?)\
            \s+(.+?)\
            \s+(.+?)\
            /(.+?)\
            \s+(.+?)\
            \s+(.+?)\
            \s+(.+?)\
            \s+(.+?)\
            \s+(.+?)\
            /(.+?)\
            \s+(.+)",
            INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str()
        )
    ).unwrap();

    pub static ref USERAUDIT_PATTERN: Regex = Regex::new(
        &format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())
    ).unwrap();

    pub static ref USERNOTICE_PATTERN: Regex = Regex::new(
        &format!(
            r"{}{}{}\
            \s+openvpn\s+server\s+'(.+?)'\
            \s+user\s+'(.+?)'\
            \s+address\s+'([\d\.]+):(\d+)'\
            \s+.+?\
            \s+(.+)",
            INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str()
        )
    ).unwrap();

    pub static ref USERWARNING_PATTERN: Regex = Regex::new(
        &format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())
    ).unwrap();

    pub static ref ROUTER_PATTERN: Regex = Regex::new(
        &format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())
    ).unwrap();

    pub static ref ROUTERBOARD_PATTERN: Regex = Regex::new(
        &format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())
    ).unwrap();

    pub static ref SWITCH_PATTERN: Regex = Regex::new(
        &format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())
    ).unwrap();

    pub static ref VMWARE_PATTERN: Regex = Regex::new(
        &format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())
    ).unwrap();

    pub static ref WINDOWSSERVER_PATTERN: Regex = Regex::new(
        &format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())
    ).unwrap();

    pub static ref WS_AN_AD_PATTERN: Regex = Regex::new(
        r".+Account Name:\s+(.+?)\s+Account Domain:\s+(.+?)\s+Logon ID:.+"
    ).unwrap();

    pub static ref WS_SW_PATTERN: Regex = Regex::new(
        r".+Source Workstation:\s+([^\s]+?)\s+Error Code:.+"
    ).unwrap();

    pub static ref VPNSERVER_PATTERN: Regex = Regex::new(
        &format!(
            r"{}{}{}\
            .*?The user\s+([^\\]+)\
            \\(.+)\s+\
            connected on port\s+(\S+)\s+.*?\
            The user was active for\s+([\w\s]+?)\.\
            \s+(\d+)\s+bytes were sent and\
            \s+(\d+)\s+bytes were received",
            INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str()
        )
    ).unwrap();

    pub static ref DHCP_PATTERN: Regex = Regex::new(
        &format!(
            r"{}{}\s+\[MSWinEventLog.+?\]\s+.+\s+{}\
            \s+(.*)\tN/A",
            INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str()
        )
    ).unwrap();

    pub static ref DNS_PATTERN: Regex = Regex::new(
        &format!(
            r"{}{}\s+\[MSWinEventLog.+?\]\s+.+\s+{}\
            \s+(.*)\tN/A",
            INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str()
        )
    ).unwrap();

    pub static ref DNS_REST_PATTERN: Regex = Regex::new(
        r"\s*(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(R?)\
        \s+([Q\?]?)\
        \s+\[([^\s]+)\
        \s+([A-Za-z ]*?)\
        \s+([^\s]+)\]\
        \s+(.+?)\
        \s+(.+)"
    ).unwrap();
}
