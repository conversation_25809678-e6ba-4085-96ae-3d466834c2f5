use std::collections::HashMap;

#[derive(Debug)]
pub struct ClassificationInfo {
    pub classtype: &'static str,
    pub priority: Priority,
    pub index: u8,
}

#[derive(Debug, PartialEq, Eq)]
pub enum Priority {
    High,
    Medium,
    Low,
    VeryLow,
}

impl Priority {
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "high" => Some(Priority::High),
            "medium" => Some(Priority::Medium),
            "low" => Some(Priority::Low),
            "very low" => Some(Priority::VeryLow),
            _ => None,
        }
    }
}

lazy_static::lazy_static! {
    pub static ref CLASSIFICATIONS: HashMap<&'static str, ClassificationInfo> = {
        let mut m = HashMap::new();

        macro_rules! insert {
            ($label:expr, $classtype:expr, $priority:expr, $index:expr) => {
                m.insert($label, ClassificationInfo {
                    classtype: $classtype,
                    priority: Priority::from_str($priority).unwrap(),
                    index: $index,
                });
            };
        }

        insert!("Attempted Administrator Privilege Gain", "attempted-admin", "high", 1);
        insert!("Attempted User Privilege Gain", "attempted-user", "high", 1);
        insert!("Inappropriate Content was Detected", "inappropriate-content", "high", 1);
        insert!("Potential Corporate Privacy Violation", "policy-violation", "high", 1);
        insert!("Executable code was detected", "shellcode-detect", "high", 1);
        insert!("Successful Administrator Privilege Gain", "successful-admin", "high", 1);
        insert!("Successful User Privilege Gain", "successful-user", "high", 1);
        insert!("A Network Trojan was detected", "trojan-activity", "high", 1);
        insert!("Unsuccessful User Privilege Gain", "unsuccessful-user", "high", 1);
        insert!("Web Application Attack", "web-application-attack", "high", 1);

        insert!("Attempted Denial of Service", "attempted-dos", "medium", 2);
        insert!("Attempted Information Leak", "attempted-recon", "medium", 2);
        insert!("Potentially Bad Traffic", "bad-unknown", "medium", 2);
        insert!("Attempt to login by a default username and password", "default-login-attempt", "medium", 2);
        insert!("Detection of a Denial of Service Attack", "denial-of-service", "medium", 2);
        insert!("Misc Attack", "misc-attack", "medium", 2);
        insert!("Detection of a non-standard protocol or event", "non-standard-protocol", "medium", 2);
        insert!("Decode of an RPC Query", "rpc-portmap-decode", "medium", 2);
        insert!("Denial of Service", "successful-dos", "medium", 2);
        insert!("Large Scale Information Leak", "successful-recon-largescale", "medium", 2);
        insert!("Information Leak", "successful-recon-limited", "medium", 2);
        insert!("A suspicious filename was detected", "suspicious-filename-detect", "medium", 2);
        insert!("An attempted login using a suspicious username was detected", "suspicious-login", "medium", 2);
        insert!("A system call was detected", "system-call-detect", "medium", 2);
        insert!("A client was using an unusual port", "unusual-client-port-connection", "medium", 2);
        insert!("Access to a potentially vulnerable web application", "web-application-activity", "medium", 2);

        insert!("Generic ICMP event", "icmp-event", "low", 3);
        insert!("Misc activity", "misc-activity", "low", 3);
        insert!("Detection of a Network Scan", "network-scan", "low", 3);
        insert!("Not Suspicious Traffic", "not-suspicious", "low", 3);
        insert!("Generic Protocol Command Decode", "protocol-command-decode", "low", 3);
        insert!("A suspicious string was detected", "string-detect", "low", 3);
        insert!("Unknown Traffic", "unknown", "low", 3);

        insert!("A TCP connection was detected", "tcp-connection", "very low", 4);

        m
    };

    pub static ref CLASSIFICATIONS__CRITICALS: Vec<&'static str> =
        CLASSIFICATIONS.iter().filter(|(_, v)| v.index == 1).map(|(k, _)| *k).collect();

    pub static ref CLASSIFICATIONS__WARNINGS: Vec<&'static str> =
        CLASSIFICATIONS.iter().filter(|(_, v)| v.index == 2).map(|(k, _)| *k).collect();

    pub static ref CLASSIFICATIONS__LOWS: Vec<&'static str> =
        CLASSIFICATIONS.iter().filter(|(_, v)| v.index == 3).map(|(k, _)| *k).collect();

    pub static ref CLASSIFICATIONS__VERY_LOWS: Vec<&'static str> =
        CLASSIFICATIONS.iter().filter(|(_, v)| v.index == 4).map(|(k, _)| *k).collect();
}
