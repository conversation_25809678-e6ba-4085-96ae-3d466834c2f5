fn main() {
    println!("Hello");
}


// use dotenv::from_path;
// use std::env;
// use std::path::Path;
// fn main() {
//     // Load from a specific path
//     let env_path = Path::new("../.env");
//     from_path(env_path).expect("Failed to load .env");

//     let host = env::var("MYSQL_DATADIR").unwrap();
//     println!("MYSQL_DATADIR = {}", host);
// }
//
// output:
// MYSQL_DATADIR = /var/lib/mysql

// mod utils_patterns;
// fn main() {
//     let line = "2023-05-13 09:51:58 Sensor-1 (auth/info) [(squid-1)]";

//     if let Some(caps) = utils_patterns::INIT_REG.captures(line) {
//         println!("Date: {}", &caps[1]);
//         println!("Time: {}", &caps[2]);
//         println!("Sensor: {}", &caps[3]);
//     }

//     if let Some(caps) = utils_patterns::EVENT_REG.captures(line) {
//         println!("Event: {}", &caps[1]);
//     }

//     if let Some(caps) = utils_patterns::ALERT_REG.captures(line) {
//         println!("Alert: {}", &caps[1]);
//     }
// }
//
// output:
// Date: 2023-05-13
// Time: 09:51:58
// Sensor: Sensor-1
// Event: (auth/info)
// Alert: [(squid-1)]

// ---------------

// mod utils_classes;
// use utils_classes::{MYSQLConfig, MYSQLValue};
// fn main() {
//     match MYSQLConfig::DEFAULT_DATA_TYPE.value() {
//         MYSQLValue::Str(val) => println!("Default type: {}", val),
//         _ => (),
//     }

//     match MYSQLConfig::BUILTIN_DATABASES.value() {
//         MYSQLValue::List(dbs) => println!("Builtin DBs: {:?}", dbs),
//         _ => (),
//     }

//    if let MYSQLValue::Str(val) = MYSQLConfig::MYSQL_HOST.value() {
//        println!("MYSQL_HOST: {}", val);
//    }

//    let in_st = MYSQLConfig::get_infile_statement();
//    println!("Infile Statement: {}", in_st);
// }
//
// output:
// Default type: MEDIUMTEXT
// Builtin DBs: ["information_schema", "mysql", "performance_schema", "sys"]
// MYSQL_HOST: localhost
// Infile Statement: LOAD DATA LOCAL INFILE


// mod utils_classes;
// use utils_classes::GeoLocationConfig;
// fn main() {
//     let headers = GeoLocationConfig::DB_HEADERS__DOMAIN;
//     println!("headers:");
//     for header in headers {
//         println!("{header}");
//     }

//     let tn = GeoLocationConfig::get_table_name("ip");
//     println!("get_table_name: {tn}");

//     let lpd = GeoLocationConfig::get_logs_parsed_dir();
//     println!("get_logs_parsed_dir: {lpd}");

//     let ss = GeoLocationConfig::get_select_statement("domain");
//     println!("get_select_statement: {ss}");
// }
//
// output:
// headers:
// ID
// Domain
// Country
// ...
// get_table_name: geolocationtable__ip
// get_logs_parsed_dir: /home/<USER>/logs-parsed/geolocation
// get_select_statement: SELECT * FROM geolocationtable__domain
