use std::collections::HashMap;

#[derive(Debug)]
pub struct AuditEvent {
    pub category: &'static str,
    pub criticality: &'static str,
    pub summary: &'static str,
}

lazy_static::lazy_static! {
    pub static ref WINDOWS_SERVER_AUDIT_EVENTS: HashMap<&'static str, AuditEvent> = {
        let mut m = HashMap::new();
    "00" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The log was started" },
    "01" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The log was stopped" },
    "02" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The log was temporarily paused due to low disk space" },
    "10" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A new IP address was leased to a client" },
    "11" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A lease was renewed by a client" },
    "12" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A lease was released by a client" },
    "13" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "An IP address was found to be in use on the network" },
    "14" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A lease request could not be satisfied because the scope's address pool was exhausted" },
    "15" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A lease was denied" },
    "16" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A lease was deleted" },
    "17" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A lease was expired and DNS records for an expired leases have not been deleted" },
    "18" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A lease was expired and DNS records were deleted" },
    "20" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A BOOTP address was leased to a client" },
    "21" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A dynamic BOOTP address was leased to a client" },
    "22" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A BOOTP request could not be satisfied because the scope's address pool for BOOTP was exhausted" },
    "23" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A BOOTP IP address was deleted after checking to see it was not in use" },
    "24" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "IP address cleanup operation has began" },
    "25" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "IP address cleanup statistics" },
    "29" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The time provider NtpClient is configured to acquire time from one or more time sources; however none of the sources are currently accessible" },
    "30" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DNS update request to the named DNS server" },
    "31" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DNS update failed" },
    "32" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DNS update successful" },
    "33" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Packet dropped due to NAP policy" },
    "34" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DNS update request failed.as the DNS update request queue limit exceeded" },
    "35" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DNS update request failed" },
    "36" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Packet dropped because the server is in failover standby role or the hash of the client ID does not match" },
    "38" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The time provider NtpClient cannot reach or is currently receiving invalid time data" },
    "41" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The system has rebooted without cleanly shutting down first" },
    "47" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Time Provider NtpClient: No valid response received" },
    "50" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Unreachable domain" },
    "51" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Authorization succeeded" },
    "52" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Upgraded to a Windows Server 2003 operating system" },
    "53" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Cached Authorization" },
    "54" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Authorization failed" },
    "55" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Authorization (servicing)" },
    "56" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Authorization failure, stopped servicing" },
    "57" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Server found in domain" },
    "58" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Server could not find domain" },
    "59" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Network failure" },
    "60" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "No DC is DS Enabled" },
    "61" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Server found that belongs to DS domain" },
    "62" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Another server found" },
    "63" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Restarting rogue detection" },
    "64" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "No DHCP enabled interfaces" },
    "512" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Windows NT is starting up" },
    "513" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Windows is shutting down" },
    "514" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "An authentication package has been loaded by the Local Security Authority" },
    "515" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A trusted logon process has registered with the Local Security Authority" },
    "516" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Internal resources allocated for the queuing of audit messages have been exhausted, leading to the loss of some audits" },
    "517" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The audit log was cleared" },
    "518" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A notification package has been loaded by the Security Account Manager" },
    "519" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A process is using an invalid local procedure call (LPC) port" },
    "520" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The system time was changed" },
    "521" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Unable to log events to security log" },
    "528" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Successful Logon" },
    "529" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Logon Failure - Unknown user name or bad password" },
    "530" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Logon Failure - Account logon time restriction violation" },
    "531" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Logon Failure - Account currently disabled" },
    "532" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Logon Failure - The specified user account has expired" },
    "533" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Logon Failure - User not allowed to logon at this computer" },
    "534" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Logon Failure - The user has not been granted the requested logon type at this machine" },
    "535" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Logon Failure - The specified account's password has expired" },
    "536" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Logon Failure - The NetLogon component is not active" },
    "537" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Logon failure - The logon attempt failed for other reasons" },
    "538" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "User Logoff" },
    "539" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Logon Failure - Account locked out" },
    "540" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Successful Network Logon" },
    "551" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "User initiated logoff" },
    "552" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Logon attempt using explicit credentials" },
    "560" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Object Open" },
    "561" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Handle Allocated" },
    "562" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Handle Closed" },
    "563" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Object Open for Delete" },
    "564" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Object Deleted" },
    "565" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Object Open (Active Directory)" },
    "566" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Object Operation (W3 Active Directory)" },
    "567" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Object Access Attempt" },
    "576" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Special privileges assigned to new logon" },
    "577" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Privileged Service Called" },
    "578" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Privileged object operation" },
    "592" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A new process has been created" },
    "593" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A process has exited" },
    "594" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A handle to an object has been duplicated" },
    "595" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Indirect access to an object has been obtained" },
    "596" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Backup of data protection master key" },
    "600" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A process was assigned a primary token" },
    "601" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Attempt to install service" },
    "602" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Scheduled Task created" },
    "608" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "User Right Assigned" },
    "609" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "User Right Removed" },
    "610" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "New Trusted Domain" },
    "611" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Removing Trusted Domain" },
    "612" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Audit Policy Change" },
    "613" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "IPSec policy agent started" },
    "614" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "IPSec policy agent disabled" },
    "615" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "IPSEC PolicyAgent Service" },
    "616" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "IPSec policy agent encountered a potentially serious failure" },
    "617" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Kerberos Policy Changed" },
    "618" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Encrypted Data Recovery Policy Changed" },
    "619" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Quality of Service Policy Changed" },
    "620" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Trusted Domain Information Modified" },
    "621" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "System Security Access Granted" },
    "622" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "System Security Access Removed" },
    "623" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Per User Audit Policy was refreshed" },
    "624" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "User Account Created" },
    "625" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "User Account Type Changed" },
    "626" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "User Account Enabled" },
    "627" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Change Password Attempt" },
    "628" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "User Account password set" },
    "629" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "User Account Disabled" },
    "630" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "User Account Deleted" },
    "631" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Enabled Global Group Created" },
    "632" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Enabled Global Group Member Added" },
    "633" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Enabled Global Group Member Removed" },
    "634" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Enabled Global Group Deleted" },
    "635" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Enabled Local Group Created" },
    "636" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Enabled Local Group Member Added" },
    "637" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Enabled Local Group Member Removed" },
    "638" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Enabled Local Group Deleted" },
    "639" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Enabled Local Group Changed" },
    "640" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "General Account Database Change" },
    "641" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Enabled Global Group Changed" },
    "642" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "User Account Changed" },
    "643" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Domain Policy Changed" },
    "644" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "User Account Locked Out" },
    "645" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Computer Account Created" },
    "646" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Computer Account Changed" },
    "647" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Computer Account Deleted" },
    "648" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Disabled Local Group Created" },
    "649" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Disabled Local Group Changed" },
    "650" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Disabled Local Group Member Added" },
    "651" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Disabled Local Group Member Removed" },
    "652" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Disabled Local Group Deleted" },
    "653" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Disabled Global Group Created" },
    "654" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Disabled Global Group Changed" },
    "655" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Disabled Global Group Member Added" },
    "656" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Disabled Global Group Member Removed" },
    "657" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Disabled Global Group Deleted" },
    "658" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Enabled Universal Group Created" },
    "659" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Enabled Universal Group Changed" },
    "660" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Enabled Universal Group Member Added" },
    "661" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Enabled Universal Group Member Removed" },
    "662" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Enabled Universal Group Deleted" },
    "663" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Disabled Universal Group Created" },
    "664" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Disabled Universal Group Changed" },
    "665" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Disabled Universal Group Member Added" },
    "666" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Disabled Universal Group Member Removed" },
    "667" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Security Disabled Universal Group Deleted" },
    "668" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Group Type Changed" },
    "669" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Add SID History" },
    "670" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Add SID History" },
    "671" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "User Account Unlocked" },
    "672" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Authentication Ticket Granted" },
    "673" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Service Ticket Granted" },
    "674" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Ticket Granted Renewed" },
    "675" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Pre-authentication failed" },
    "676" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Authentication Ticket Request Failed" },
    "677" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Service Ticket Request Failed" },
    "678" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Account Mapped for Logon by" },
    "679" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The name: %2 could not be mapped for logon by: %1" },
    "680" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Account Used for Logon by" },
    "681" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The logon to account: %2 by: %1 from workstation: %3 failed" },
    "682" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Session reconnected to winstation" },
    "683" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Session disconnected from winstation" },
    "684" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Set ACLs of members in administrators groups" },
    "685" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Account Name Changed" },
    "686" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Password of the following user accessed" },
    "687" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Basic Application Group Created" },
    "688" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Basic Application Group Changed" },
    "689" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Basic Application Group Member Added" },
    "690" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Basic Application Group Member Removed" },
    "691" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Basic Application Group Non-Member Added" },
    "692" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Basic Application Group Non-Member Removed" },
    "693" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Basic Application Group Deleted" },
    "694" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "LDAP Query Group Created" },
    "695" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "LDAP Query Group Changed" },
    "696" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "LDAP Query Group Deleted" },
    "697" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Password Policy Checking API is called" },
    "806" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Per User Audit Policy was refreshed" },
    "807" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Per user auditing policy set for user" },
    "808" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A security event source has attempted to register" },
    "809" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A security event source has attempted to unregister" },
    "848" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The following policy was active when the Windows Firewall started" },
    "849" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "An application was listed as an exception when the Windows Firewall started" },
    "850" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A port was listed as an exception when the Windows Firewall started" },
    "851" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A change has been made to the Windows Firewall application exception list" },
    "852" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A change has been made to the Windows Firewall port exception list" },
    "853" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The Windows Firewall operational mode has changed" },
    "854" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The Windows Firewall logging settings have changed" },
    "855" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A Windows Firewall ICMP setting has changed" },
    "856" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The Windows Firewall setting to allow unicast responses to multicast/broadcast traffic has changed" },
    "857" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The Windows Firewall setting to allow remote administration, allowing port TCP 135 and DCOM/RPC, has changed" },
    "858" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Windows Firewall group policy settings have been applied" },
    "859" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The Windows Firewall group policy settings have been removed" },
    "860" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The Windows Firewall has switched the active policy profile" },
    "861" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The Windows Firewall has detected an application listening for incoming traffic" },
    "1030" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "the Group Policy settings cannot be read, the Group Policy object (GPO) is corrupted, or the computer is unable to access the domain controller" },
    "1058" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "the computer is unable to access the Sysvol share, which stores the Group Policy templates and scripts" },
    "1074" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The system has been shutdown properly by a user or process. The process X has initiated the restart / shutdown of computer on behalf of user Y for the following reason: Z. Indicates that an application or a user initiated a restart or shutdown" },
    "1076" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The reason supplied by user X for the last unexpected shutdown of this computer is: Y. Records when the first user with shutdown privileges logs on to the computer after an unexpected restart or shutdown and supplies a reason for the occurrence.Follows after Event ID 6008 and means that the first user with shutdown privileges logged on to the server after an unexpected restart or shutdown and specified the cause" },
    "1100" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The event logging service has shut down" },
    "1101" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Audit events have been dropped by the transport" },
    "1102" => AuditEvent { category: "Miscellaneous", criticality: "Medium to High", summary: "The audit log was cleared" },
    "1104" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The security Log is now full" },
    "1105" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Event log automatic backup" },
    "1108" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The event logging service encountered an error" },
    "1704" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "the GPO was successfully applied to the client computer" },
    "4190" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCP server failed to assign an address because there are no more available in the scope" },
    "4191" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCP server encountered an error while processing a DHCP request" },
    "4198" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCP lease has expired" },
    "4608" => AuditEvent { category: "System", criticality: "Low", summary: "Windows is starting up" },
    "4609" => AuditEvent { category: "System", criticality: "Low", summary: "Windows is shutting down" },
    "4610" => AuditEvent { category: "System", criticality: "Low", summary: "An authentication package has been loaded by the Local Security Authority" },
    "4611" => AuditEvent { category: "System", criticality: "Low", summary: "A trusted logon process has been registered with the Local Security Authority" },
    "4612" => AuditEvent { category: "System", criticality: "Low", summary: "Internal resources allocated for the queuing of audit messages have been exhausted, leading to the loss of some audits" },
    "4614" => AuditEvent { category: "System", criticality: "Low", summary: "A notification package has been loaded by the Security Account Manager" },
    "4615" => AuditEvent { category: "System", criticality: "Low", summary: "Invalid use of LPC port" },
    "4616" => AuditEvent { category: "System", criticality: "Low", summary: "The system time was changed" },
    "4618" => AuditEvent { category: "System", criticality: "High", summary: "A monitored security event pattern has occurred" },
    "4621" => AuditEvent { category: "System", criticality: "Medium", summary: "Administrator recovered system from CrashOnAuditFail. Users who are not administrators will now be allowed to log on. Some auditable activity might not have been recorded" },
    "4622" => AuditEvent { category: "System", criticality: "Low", summary: "A security package has been loaded by the Local Security Authority" },
    "4624" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "An account was successfully logged on" },
    "4625" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "An account failed to log on" },
    "4626" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "User/Device claims information" },
    "4627" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "Group membership information" },
    "4634" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "An account was logged off" },
    "4646" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "IKE DoS-prevention mode started" },
    "4647" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "User initiated logoff" },
    "4648" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "A logon was attempted using explicit credentials" },
    "4649" => AuditEvent { category: "Logon/Logoff", criticality: "High", summary: "A replay attack was detected. May be a harmless false positive due to misconfiguration error" },
    "4650" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "An IPsec Main Mode security association was established. Extended Mode was not enabled. Certificate authentication was not used" },
    "4651" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "An IPsec Main Mode security association was established. Extended Mode was not enabled. A certificate was used for authentication" },
    "4652" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "An IPsec Main Mode negotiation failed" },
    "4653" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "An IPsec Main Mode negotiation failed" },
    "4654" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "An IPsec Quick Mode negotiation failed" },
    "4655" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "An IPsec Main Mode security association ended" },
    "4656" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A handle to an object was requested" },
    "4657" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A registry value was modified" },
    "4658" => AuditEvent { category: "Object Access", criticality: "Low", summary: "The handle to an object was closed" },
    "4659" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A handle to an object was requested with intent to delete" },
    "4660" => AuditEvent { category: "Object Access", criticality: "Low", summary: "An object was deleted" },
    "4661" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A handle to an object was requested" },
    "4662" => AuditEvent { category: "DS Access", criticality: "Low", summary: "An operation was performed on an object" },
    "4663" => AuditEvent { category: "Object Access", criticality: "Low", summary: "An attempt was made to access an object" },
    "4664" => AuditEvent { category: "Object Access", criticality: "Low", summary: "An attempt was made to create a hard link" },
    "4665" => AuditEvent { category: "Object Access", criticality: "Low", summary: "An attempt was made to create an application client context" },
    "4666" => AuditEvent { category: "Object Access", criticality: "Low", summary: "An application attempted an operation" },
    "4667" => AuditEvent { category: "Object Access", criticality: "Low", summary: "An application client context was deleted" },
    "4668" => AuditEvent { category: "Object Access", criticality: "Low", summary: "An application was initialized" },
    "4670" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "Permissions on an object were changed" },
    "4671" => AuditEvent { category: "Object Access", criticality: "Low", summary: "An application attempted to access a blocked ordinal through the TBS" },
    "4672" => AuditEvent { category: "Privilege Use", criticality: "Low", summary: "Special privileges assigned to new logon" },
    "4673" => AuditEvent { category: "Privilege Use", criticality: "Low", summary: "A privileged service was called" },
    "4674" => AuditEvent { category: "Privilege Use", criticality: "Low", summary: "An operation was attempted on a privileged object" },
    "4675" => AuditEvent { category: "Logon/Logoff", criticality: "Medium", summary: "SIDs were filtered" },
    "4688" => AuditEvent { category: "Detailed Tracking", criticality: "Low", summary: "A new process has been created" },
    "4689" => AuditEvent { category: "Detailed Tracking", criticality: "Low", summary: "A process has exited" },
    "4690" => AuditEvent { category: "Object Access", criticality: "Low", summary: "An attempt was made to duplicate a handle to an object" },
    "4691" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Indirect access to an object was requested" },
    "4692" => AuditEvent { category: "Detailed Tracking", criticality: "Medium", summary: "Backup of data protection master key was attempted" },
    "4693" => AuditEvent { category: "Detailed Tracking", criticality: "Medium", summary: "Recovery of data protection master key was attempted" },
    "4694" => AuditEvent { category: "Detailed Tracking", criticality: "Low", summary: "Protection of auditable protected data was attempted" },
    "4695" => AuditEvent { category: "Detailed Tracking", criticality: "Low", summary: "Unprotection of auditable protected data was attempted" },
    "4696" => AuditEvent { category: "Detailed Tracking", criticality: "Low", summary: "A primary token was assigned to process" },
    "4697" => AuditEvent { category: "System", criticality: "Low", summary: "A service was installed in the system" },
    "4698" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A scheduled task was created" },
    "4699" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A scheduled task was deleted" },
    "4700" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A scheduled task was enabled" },
    "4701" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A scheduled task was disabled" },
    "4702" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A scheduled task was updated" },
    "4703" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A user right was adjusted" },
    "4704" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A user right was assigned" },
    "4705" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A user right was removed" },
    "4706" => AuditEvent { category: "Policy Change", criticality: "Medium", summary: "A new trust was created to a domain" },
    "4707" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A trust to a domain was removed" },
    "4709" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "IPsec Services was started" },
    "4710" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "IPsec Services was disabled" },
    "4711" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "May contain any one of the following: PAStore Engine applied locally cached copy of Active Directory storage IPsec policy on the computer. PAStore Engine applied Active Directory storage IPsec policy on the computer. PAStore Engine applied local registry storage IPsec policy on the computer. PAStore Engine failed to apply locally cached copy of Active Directory storage IPsec policy on the computer. PAStore Engine failed to apply Active Directory storage IPsec policy on the computer. PAStore Engine failed to apply local registry storage IPsec policy on the computer. PAStore Engine failed to apply some rules of the active IPsec policy on the computer. PAStore Engine failed to load directory storage IPsec policy on the computer. PAStore Engine loaded directory storage IPsec policy on the computer. PAStore Engine failed to load local storage IPsec policy on the computer. PAStore Engine loaded local storage IPsec policy on the computer. PAStore Engine polled for changes to the active IPsec policy and detected no changes" },
    "4712" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "IPsec Services encountered a potentially serious failure" },
    "4713" => AuditEvent { category: "Policy Change", criticality: "Medium", summary: "Kerberos policy was changed" },
    "4714" => AuditEvent { category: "Policy Change", criticality: "Medium", summary: "Encrypted data recovery policy was changed" },
    "4715" => AuditEvent { category: "Policy Change", criticality: "Medium", summary: "The audit policy (SACL) on an object was changed" },
    "4716" => AuditEvent { category: "Policy Change", criticality: "Medium", summary: "Trusted domain information was modified" },
    "4717" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "System security access was granted to an account" },
    "4718" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "System security access was removed from an account" },
    "4719" => AuditEvent { category: "Policy Change", criticality: "High", summary: "System audit policy was changed" },
    "4720" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A user account was created" },
    "4722" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A user account was enabled" },
    "4723" => AuditEvent { category: "Account Management", criticality: "Low", summary: "An attempt was made to change an account's password" },
    "4724" => AuditEvent { category: "Account Management", criticality: "Medium", summary: "An attempt was made to reset an account's password" },
    "4725" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A user account was disabled" },
    "4726" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A user account was deleted" },
    "4727" => AuditEvent { category: "Account Management", criticality: "Medium", summary: "A security-enabled global group was created" },
    "4728" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A member was added to a security-enabled global group" },
    "4729" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A member was removed from a security-enabled global group" },
    "4730" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A security-enabled global group was deleted" },
    "4731" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A security-enabled local group was created" },
    "4732" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A member was added to a security-enabled local group" },
    "4733" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A member was removed from a security-enabled local group" },
    "4734" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A security-enabled local group was deleted" },
    "4735" => AuditEvent { category: "Account Management", criticality: "Medium", summary: "A security-enabled local group was changed" },
    "4737" => AuditEvent { category: "Account Management", criticality: "Medium", summary: "A security-enabled global group was changed" },
    "4738" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A user account was changed" },
    "4739" => AuditEvent { category: "Policy Change", criticality: "Medium", summary: "Domain Policy was changed" },
    "4740" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A user account was locked out" },
    "4741" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A computer account was created" },
    "4742" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A computer account was changed" },
    "4743" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A computer account was deleted" },
    "4744" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A security-disabled local group was created" },
    "4745" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A security-disabled local group was changed" },
    "4746" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A member was added to a security-disabled local group" },
    "4747" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A member was removed from a security-disabled local group" },
    "4748" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A security-disabled local group was deleted" },
    "4749" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A security-disabled global group was created" },
    "4750" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A security-disabled global group was changed" },
    "4751" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A member was added to a security-disabled global group" },
    "4752" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A member was removed from a security-disabled global group" },
    "4753" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A security-disabled global group was deleted" },
    "4754" => AuditEvent { category: "Account Management", criticality: "Medium", summary: "A security-enabled universal group was created" },
    "4755" => AuditEvent { category: "Account Management", criticality: "Medium", summary: "A security-enabled universal group was changed" },
    "4756" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A member was added to a security-enabled universal group" },
    "4757" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A member was removed from a security-enabled universal group" },
    "4758" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A security-enabled universal group was deleted" },
    "4759" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A security-disabled universal group was created" },
    "4760" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A security-disabled universal group was changed" },
    "4761" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A member was added to a security-disabled universal group" },
    "4762" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A member was removed from a security-disabled universal group" },
    "4763" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A security-disabled universal group was deleted" },
    "4764" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A group’s type was changed" },
    "4765" => AuditEvent { category: "Account Management", criticality: "High", summary: "SID History was added to an account" },
    "4766" => AuditEvent { category: "Account Management", criticality: "High", summary: "An attempt to add SID History to an account failed" },
    "4767" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A user account was unlocked" },
    "4768" => AuditEvent { category: "Account Logon", criticality: "Low", summary: "A Kerberos authentication ticket (TGT) was requested" },
    "4769" => AuditEvent { category: "Account Logon", criticality: "Low", summary: "A Kerberos service ticket was requested" },
    "4770" => AuditEvent { category: "Account Logon", criticality: "Low", summary: "A Kerberos service ticket was renewed" },
    "4771" => AuditEvent { category: "Account Logon", criticality: "Low", summary: "Kerberos pre-authentication failed" },
    "4772" => AuditEvent { category: "Account Logon", criticality: "Low", summary: "A Kerberos authentication ticket request failed" },
    "4773" => AuditEvent { category: "Account Logon", criticality: "Low", summary: "A Kerberos service ticket request failed" },
    "4774" => AuditEvent { category: "Account Logon", criticality: "Low", summary: "An account was mapped for logon" },
    "4775" => AuditEvent { category: "Account Logon", criticality: "Low", summary: "An account could not be mapped for logon" },
    "4776" => AuditEvent { category: "Account Logon", criticality: "Low", summary: "The domain controller attempted to validate the credentials for an account" },
    "4777" => AuditEvent { category: "Account Logon", criticality: "Low", summary: "The domain controller failed to validate the credentials for an account" },
    "4778" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "A session was reconnected to a Window Station" },
    "4779" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "A session was disconnected from a Window Station" },
    "4780" => AuditEvent { category: "Account Management", criticality: "Medium", summary: "The ACL was set on accounts which are members of administrators groups" },
    "4781" => AuditEvent { category: "Account Management", criticality: "Low", summary: "The name of an account was changed" },
    "4782" => AuditEvent { category: "Account Management", criticality: "Low", summary: "The password hash an account was accessed" },
    "4783" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A basic application group was created" },
    "4784" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A basic application group was changed" },
    "4785" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A member was added to a basic application group" },
    "4786" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A member was removed from a basic application group" },
    "4787" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A non-member was added to a basic application group" },
    "4788" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A non-member was removed from a basic application group" },
    "4789" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A basic application group was deleted" },
    "4790" => AuditEvent { category: "Account Management", criticality: "Low", summary: "An LDAP query group was created" },
    "4791" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A basic application group was changed" },
    "4792" => AuditEvent { category: "Account Management", criticality: "Low", summary: "An LDAP query group was deleted" },
    "4793" => AuditEvent { category: "Account Management", criticality: "Low", summary: "The Password Policy Checking API was called" },
    "4794" => AuditEvent { category: "Account Management", criticality: "High", summary: "An attempt was made to set the Directory Services Restore Mode" },
    "4797" => AuditEvent { category: "Account Management", criticality: "Low", summary: "An attempt was made to query the existence of a blank password for an account" },
    "4798" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A user's local group membership was enumerated" },
    "4799" => AuditEvent { category: "Account Management", criticality: "Low", summary: "A security-enabled local group membership was enumerated" },
    "4800" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "The workstation was locked" },
    "4801" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "The workstation was unlocked" },
    "4802" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "The screen saver was invoked" },
    "4803" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "The screen saver was dismissed" },
    "4816" => AuditEvent { category: "System", criticality: "Medium", summary: "RPC detected an integrity violation while decrypting an incoming message" },
    "4817" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "Auditing settings on an object were changed" },
    "4818" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Proposed Central Access Policy does not grant the same access permissions as the current Central Access Policy" },
    "4819" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "Central Access Policies on the machine have been changed" },
    "4820" => AuditEvent { category: "Account Logon", criticality: "Low", summary: "A Kerberos Ticket-granting-ticket (TGT) was denied because the device does not meet the access control restrictions" },
    "4821" => AuditEvent { category: "Account Logon", criticality: "Low", summary: "A Kerberos service ticket was denied because the user, device, or both does not meet the access control restrictions" },
    "4822" => AuditEvent { category: "Account Logon", criticality: "Low", summary: "NTLM authentication failed because the account was a member of the Protected User group" },
    "4823" => AuditEvent { category: "Account Logon", criticality: "Low", summary: "NTLM authentication failed because access control restrictions are required" },
    "4824" => AuditEvent { category: "Account Logon", criticality: "Low", summary: "Kerberos preauthentication by using DES or RC4 failed because the account was a member of the Protected User group" },
    "4825" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "A user was denied the access to Remote Desktop" },
    "4826" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "Boot Configuration Data loaded" },
    "4830" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "SID History was removed from an account" },
    "4864" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A namespace collision was detected" },
    "4865" => AuditEvent { category: "Policy Change", criticality: "Medium", summary: "A trusted forest information entry was added" },
    "4866" => AuditEvent { category: "Policy Change", criticality: "Medium", summary: "A trusted forest information entry was removed" },
    "4867" => AuditEvent { category: "Policy Change", criticality: "Medium", summary: "A trusted forest information entry was modified" },
    "4868" => AuditEvent { category: "Object Access", criticality: "Medium", summary: "The certificate manager denied a pending certificate request" },
    "4869" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services received a resubmitted certificate request" },
    "4870" => AuditEvent { category: "Object Access", criticality: "Medium", summary: "Certificate Services revoked a certificate" },
    "4871" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services received a request to publish the certificate revocation list (CRL)" },
    "4872" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services published the certificate revocation list (CRL)" },
    "4873" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A certificate request extension changed" },
    "4874" => AuditEvent { category: "Object Access", criticality: "Low", summary: "One or more certificate request attributes changed" },
    "4875" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services received a request to shut down" },
    "4876" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services backup started" },
    "4877" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services backup completed" },
    "4878" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services restore started" },
    "4879" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services restore completed" },
    "4880" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services started" },
    "4881" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services stopped" },
    "4882" => AuditEvent { category: "Object Access", criticality: "Medium", summary: "The security permissions for Certificate Services changed" },
    "4883" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services retrieved an archived key" },
    "4884" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services imported a certificate into its database" },
    "4885" => AuditEvent { category: "Object Access", criticality: "Medium", summary: "The audit filter for Certificate Services changed" },
    "4886" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services received a certificate request" },
    "4887" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services approved a certificate request and issued a certificate" },
    "4888" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services denied a certificate request" },
    "4889" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services set the status of a certificate request to pending" },
    "4890" => AuditEvent { category: "Object Access", criticality: "Medium", summary: "The certificate manager settings for Certificate Services changed" },
    "4891" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A configuration entry changed in Certificate Services" },
    "4892" => AuditEvent { category: "Object Access", criticality: "Medium", summary: "A property of Certificate Services changed" },
    "4893" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services archived a key" },
    "4894" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services imported and archived a key" },
    "4895" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services published the CA certificate to Active Directory Domain Services" },
    "4896" => AuditEvent { category: "Object Access", criticality: "Medium", summary: "One or more rows have been deleted from the certificate database" },
    "4897" => AuditEvent { category: "Object Access", criticality: "High", summary: "Role separation enabled" },
    "4898" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services loaded a template" },
    "4899" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A Certificate Services template was updated" },
    "4900" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Certificate Services template security was updated" },
    "4902" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "The Per-user audit policy table was created" },
    "4904" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "An attempt was made to register a security event source" },
    "4905" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "An attempt was made to unregister a security event source" },
    "4906" => AuditEvent { category: "Policy Change", criticality: "Medium", summary: "The CrashOnAuditFail value has changed" },
    "4907" => AuditEvent { category: "Policy Change", criticality: "Medium", summary: "Auditing settings on object were changed" },
    "4908" => AuditEvent { category: "Policy Change", criticality: "Medium", summary: "Special Groups Logon table modified" },
    "4909" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "The local policy settings for the TBS were changed" },
    "4910" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "The group policy settings for the TBS were changed" },
    "4911" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "Resource attributes of the object were changed" },
    "4912" => AuditEvent { category: "Policy Change", criticality: "Medium", summary: "Per User Audit Policy was changed" },
    "4913" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "Central Access Policy on the object was changed" },
    "4928" => AuditEvent { category: "DS Access", criticality: "Low", summary: "An Active Directory replica source naming context was established" },
    "4929" => AuditEvent { category: "DS Access", criticality: "Low", summary: "An Active Directory replica source naming context was removed" },
    "4930" => AuditEvent { category: "DS Access", criticality: "Low", summary: "An Active Directory replica source naming context was modified" },
    "4931" => AuditEvent { category: "DS Access", criticality: "Low", summary: "An Active Directory replica destination naming context was modified" },
    "4932" => AuditEvent { category: "DS Access", criticality: "Low", summary: "Synchronization of a replica of an Active Directory naming context has begun" },
    "4933" => AuditEvent { category: "DS Access", criticality: "Low", summary: "Synchronization of a replica of an Active Directory naming context has ended" },
    "4934" => AuditEvent { category: "DS Access", criticality: "Low", summary: "Attributes of an Active Directory object were replicated" },
    "4935" => AuditEvent { category: "DS Access", criticality: "Low", summary: "Replication failure begins" },
    "4936" => AuditEvent { category: "DS Access", criticality: "Low", summary: "Replication failure ends" },
    "4937" => AuditEvent { category: "DS Access", criticality: "Low", summary: "A lingering object was removed from a replica" },
    "4944" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "The following policy was active when the Windows Firewall started" },
    "4945" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A rule was listed when the Windows Firewall started" },
    "4946" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A change has been made to Windows Firewall exception list. A rule was added" },
    "4947" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A change has been made to Windows Firewall exception list. A rule was modified" },
    "4948" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A change has been made to Windows Firewall exception list. A rule was deleted" },
    "4949" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "Windows Firewall settings were restored to the default values" },
    "4950" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A Windows Firewall setting has changed" },
    "4951" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A rule has been ignored because its major version number was not recognized by Windows Firewall" },
    "4952" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "Parts of a rule have been ignored because its minor version number was not recognized by Windows Firewall. The other parts of the rule will be enforced" },
    "4953" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A rule has been ignored by Windows Firewall because it could not parse the rule" },
    "4954" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "Windows Firewall Group Policy settings have changed. The new settings have been applied" },
    "4956" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "Windows Firewall has changed the active profile" },
    "4957" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "Windows Firewall did not apply the following rule" },
    "4958" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "Windows Firewall did not apply the following rule because the rule referred to items not configured on this computer" },
    "4960" => AuditEvent { category: "System", criticality: "Medium", summary: "IPsec dropped an inbound packet that failed an integrity check. If this problem persists, it could indicate a network issue or that packets are being modified in transit to this computer. Verify that the packets sent from the remote computer are the same as those received by this computer. This error might also indicate interoperability problems with other IPsec implementations" },
    "4961" => AuditEvent { category: "System", criticality: "Medium", summary: "IPsec dropped an inbound packet that failed a replay check. If this problem persists, it could indicate a replay attack against this computer" },
    "4962" => AuditEvent { category: "System", criticality: "Medium", summary: "IPsec dropped an inbound packet that failed a replay check. The inbound packet had too low a sequence number to ensure it was not a replay" },
    "4963" => AuditEvent { category: "System", criticality: "Medium", summary: "IPsec dropped an inbound clear text packet that should have been secured. This is usually due to the remote computer changing its IPsec policy without informing this computer. This could also be a spoofing attack attempt" },
    "4964" => AuditEvent { category: "Logon/Logoff", criticality: "High", summary: "Special groups have been assigned to a new logon" },
    "4965" => AuditEvent { category: "System", criticality: "Medium", summary: "IPsec received a packet from a remote computer with an incorrect Security Parameter Index (SPI). This is usually caused by malfunctioning hardware that is corrupting packets. If these errors persist, verify that the packets sent from the remote computer are the same as those received by this computer. This error may also indicate interoperability problems with other IPsec implementations. In that case, if connectivity is not impeded, then these events can be ignored" },
    "4976" => AuditEvent { category: "Logon/Logoff", criticality: "Medium", summary: "During Main Mode negotiation, IPsec received an invalid negotiation packet. If this problem persists, it could indicate a network issue or an attempt to modify or replay this negotiation" },
    "4977" => AuditEvent { category: "Logon/Logoff", criticality: "Medium", summary: "During Quick Mode negotiation, IPsec received an invalid negotiation packet. If this problem persists, it could indicate a network issue or an attempt to modify or replay this negotiation" },
    "4978" => AuditEvent { category: "Logon/Logoff", criticality: "Medium", summary: "During Extended Mode negotiation, IPsec received an invalid negotiation packet. If this problem persists, it could indicate a network issue or an attempt to modify or replay this negotiation" },
    "4979" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "IPsec Main Mode and Extended Mode security associations were established" },
    "4980" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "IPsec Main Mode and Extended Mode security associations were established" },
    "4981" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "IPsec Main Mode and Extended Mode security associations were established" },
    "4982" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "IPsec Main Mode and Extended Mode security associations were established" },
    "4983" => AuditEvent { category: "Logon/Logoff", criticality: "Medium", summary: "An IPsec Extended Mode negotiation failed. The corresponding Main Mode security association has been deleted" },
    "4984" => AuditEvent { category: "Logon/Logoff", criticality: "Medium", summary: "An IPsec Extended Mode negotiation failed. The corresponding Main Mode security association has been deleted" },
    "4985" => AuditEvent { category: "Object Access", criticality: "Low", summary: "The state of a transaction has changed" },
    "5024" => AuditEvent { category: "System", criticality: "Low", summary: "The Windows Firewall Service has started successfully" },
    "5025" => AuditEvent { category: "System", criticality: "Low", summary: "The Windows Firewall Service has been stopped" },
    "5027" => AuditEvent { category: "System", criticality: "Medium", summary: "The Windows Firewall Service was unable to retrieve the security policy from the local storage. The service will continue enforcing the current policy" },
    "5028" => AuditEvent { category: "System", criticality: "Medium", summary: "The Windows Firewall Service was unable to parse the new security policy. The service will continue with currently enforced policy" },
    "5029" => AuditEvent { category: "System", criticality: "Medium", summary: "The Windows Firewall Service failed to initialize the driver. The service will continue to enforce the current policy" },
    "5030" => AuditEvent { category: "System", criticality: "Medium", summary: "The Windows Firewall Service failed to start" },
    "5031" => AuditEvent { category: "Object Access", criticality: "Low", summary: "The Windows Firewall Service blocked an application from accepting incoming connections on the network" },
    "5032" => AuditEvent { category: "System", criticality: "Low", summary: "Windows Firewall was unable to notify the user that it blocked an application from accepting incoming connections on the network" },
    "5033" => AuditEvent { category: "System", criticality: "Low", summary: "The Windows Firewall Driver has started successfully" },
    "5034" => AuditEvent { category: "System", criticality: "Low", summary: "The Windows Firewall Driver has been stopped" },
    "5035" => AuditEvent { category: "System", criticality: "Medium", summary: "The Windows Firewall Driver failed to start" },
    "5037" => AuditEvent { category: "System", criticality: "Medium", summary: "The Windows Firewall Driver detected critical runtime error. Terminating" },
    "5038" => AuditEvent { category: "System", criticality: "Medium", summary: "Code integrity determined that the image hash of a file is not valid. The file could be corrupt due to unauthorized modification or the invalid hash could indicate a potential disk device error" },
    "5039" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A registry key was virtualized" },
    "5040" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A change has been made to IPsec settings. An Authentication Set was added" },
    "5041" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A change has been made to IPsec settings. An Authentication Set was modified" },
    "5042" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A change has been made to IPsec settings. An Authentication Set was deleted" },
    "5043" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A change has been made to IPsec settings. A Connection Security Rule was added" },
    "5044" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A change has been made to IPsec settings. A Connection Security Rule was modified" },
    "5045" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A change has been made to IPsec settings. A Connection Security Rule was deleted" },
    "5046" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A change has been made to IPsec settings. A Crypto Set was added" },
    "5047" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A change has been made to IPsec settings. A Crypto Set was modified" },
    "5048" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A change has been made to IPsec settings. A Crypto Set was deleted" },
    "5049" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "An IPsec Security Association was deleted" },
    "5050" => AuditEvent { category: "System", criticality: "Low", summary: "An attempt to programmatically disable the Windows Firewall was rejected because this API is not supported on Windows Vista" },
    "5051" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A file was virtualized" },
    "5056" => AuditEvent { category: "System", criticality: "Low", summary: "A cryptographic self test was performed" },
    "5057" => AuditEvent { category: "System", criticality: "Low", summary: "A cryptographic primitive operation failed" },
    "5058" => AuditEvent { category: "System", criticality: "Low", summary: "Key file operation" },
    "5059" => AuditEvent { category: "System", criticality: "Low", summary: "Key migration operation" },
    "5060" => AuditEvent { category: "System", criticality: "Low", summary: "Verification operation failed" },
    "5061" => AuditEvent { category: "System", criticality: "Low", summary: "Cryptographic operation" },
    "5062" => AuditEvent { category: "System", criticality: "Low", summary: "A kernel-mode cryptographic self test was performed" },
    "5063" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A cryptographic provider operation was attempted" },
    "5064" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A cryptographic context operation was attempted" },
    "5065" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A cryptographic context modification was attempted" },
    "5066" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A cryptographic function operation was attempted" },
    "5067" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A cryptographic function modification was attempted" },
    "5068" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A cryptographic function provider operation was attempted" },
    "5069" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A cryptographic function property operation was attempted" },
    "5070" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A cryptographic function property modification was attempted" },
    "5071" => AuditEvent { category: "System", criticality: "Low", summary: "Key access denied by Microsoft key distribution service" },
    "5120" => AuditEvent { category: "Object Access", criticality: "Medium", summary: "OCSP Responder Service Started" },
    "5121" => AuditEvent { category: "Object Access", criticality: "Medium", summary: "OCSP Responder Service Stopped" },
    "5122" => AuditEvent { category: "Object Access", criticality: "Medium", summary: "A Configuration entry changed in the OCSP Responder Service" },
    "5123" => AuditEvent { category: "Object Access", criticality: "Medium", summary: "A configuration entry changed in the OCSP Responder Service" },
    "5124" => AuditEvent { category: "Object Access", criticality: "High", summary: "A security setting was updated on the OCSP Responder Service" },
    "5125" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A request was submitted to the OCSP Responder Service" },
    "5126" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Signing Certificate was automatically updated by the OCSP Responder Service" },
    "5127" => AuditEvent { category: "Object Access", criticality: "Low", summary: "The OCSP Revocation Provider successfully updated the revocation information" },
    "5136" => AuditEvent { category: "DS Access", criticality: "Low", summary: "A directory service object was modified" },
    "5137" => AuditEvent { category: "DS Access", criticality: "Low", summary: "A directory service object was created" },
    "5138" => AuditEvent { category: "DS Access", criticality: "Low", summary: "A directory service object was undeleted" },
    "5139" => AuditEvent { category: "DS Access", criticality: "Low", summary: "A directory service object was moved" },
    "5140" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A network share object was accessed" },
    "5141" => AuditEvent { category: "DS Access", criticality: "Low", summary: "A directory service object was deleted" },
    "5142" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A network share object was added" },
    "5143" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A network share object was modified" },
    "5144" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A network share object was deleted" },
    "5145" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A network share object was checked to see whether the client can be granted desired access" },
    "5146" => AuditEvent { category: "Object Access", criticality: "Low", summary: "The Windows Filtering Platform has blocked a packet" },
    "5147" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A more restrictive Windows Filtering Platform filter has blocked a packet" },
    "5148" => AuditEvent { category: "Object Access", criticality: "Low", summary: "The Windows Filtering Platform has detected a DoS attack and entered a defensive mode; packets associated with this attack will be discarded" },
    "5149" => AuditEvent { category: "Object Access", criticality: "Low", summary: "The DoS attack has subsided and normal processing is being resumed" },
    "5150" => AuditEvent { category: "Object Access", criticality: "Low", summary: "The Windows Filtering Platform has blocked a packet" },
    "5151" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A more restrictive Windows Filtering Platform filter has blocked a packet" },
    "5152" => AuditEvent { category: "Object Access", criticality: "Low", summary: "The Windows Filtering Platform blocked a packet" },
    "5153" => AuditEvent { category: "Object Access", criticality: "Low", summary: "A more restrictive Windows Filtering Platform filter has blocked a packet" },
    "5154" => AuditEvent { category: "Object Access", criticality: "Low", summary: "The Windows Filtering Platform has permitted an application or service to listen on a port for incoming connections" },
    "5155" => AuditEvent { category: "Object Access", criticality: "Low", summary: "The Windows Filtering Platform has blocked an application or service from listening on a port for incoming connections" },
    "5156" => AuditEvent { category: "Object Access", criticality: "Low", summary: "The Windows Filtering Platform has allowed a connection" },
    "5157" => AuditEvent { category: "Object Access", criticality: "Low", summary: "The Windows Filtering Platform has blocked a connection" },
    "5158" => AuditEvent { category: "Object Access", criticality: "Low", summary: "The Windows Filtering Platform has permitted a bind to a local port" },
    "5159" => AuditEvent { category: "Object Access", criticality: "Low", summary: "The Windows Filtering Platform has blocked a bind to a local port" },
    "5168" => AuditEvent { category: "Object Access", criticality: "Low", summary: "Spn check for SMB/SMB2 failed" },
    "5169" => AuditEvent { category: "DS Access", criticality: "Low", summary: "A directory service object was modified" },
    "5170" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "A directory service object was modified during a background cleanup task" },
    "5376" => AuditEvent { category: "Account Management", criticality: "Medium", summary: "Credential Manager credentials were backed up" },
    "5377" => AuditEvent { category: "Account Management", criticality: "Medium", summary: "Credential Manager credentials were restored from a backup" },
    "5378" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "The requested credentials delegation was disallowed by policy" },
    "5379" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Credential Manager credentials were read" },
    "5380" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Vault Find Credential" },
    "5381" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Vault credentials were read" },
    "5382" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Vault credentials were read" },
    "5440" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "The following callout was present when the Windows Filtering Platform Base Filtering Engine started" },
    "5441" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "The following filter was present when the Windows Filtering Platform Base Filtering Engine started" },
    "5442" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "The following provider was present when the Windows Filtering Platform Base Filtering Engine started" },
    "5443" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "The following provider context was present when the Windows Filtering Platform Base Filtering Engine started" },
    "5444" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "The following sub-layer was present when the Windows Filtering Platform Base Filtering Engine started" },
    "5446" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A Windows Filtering Platform callout has been changed" },
    "5447" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A Windows Filtering Platform filter has been changed" },
    "5448" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A Windows Filtering Platform provider has been changed" },
    "5449" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A Windows Filtering Platform provider context has been changed" },
    "5450" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "A Windows Filtering Platform sub-layer has been changed" },
    "5451" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "An IPsec Quick Mode security association was established" },
    "5452" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "An IPsec Quick Mode security association ended" },
    "5453" => AuditEvent { category: "Logon/Logoff", criticality: "Medium", summary: "An IPsec negotiation with a remote computer failed because the IKE and AuthIP IPsec Keying Modules (IKEEXT) service is not started" },
    "5456" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine applied Active Directory storage IPsec policy on the computer" },
    "5457" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine failed to apply Active Directory storage IPsec policy on the computer" },
    "5458" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine applied locally cached copy of Active Directory storage IPsec policy on the computer" },
    "5459" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine failed to apply locally cached copy of Active Directory storage IPsec policy on the computer" },
    "5460" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine applied local registry storage IPsec policy on the computer" },
    "5461" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine failed to apply local registry storage IPsec policy on the computer" },
    "5462" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine failed to apply some rules of the active IPsec policy on the computer. Use the IP Security Monitor snap-in to diagnose the problem" },
    "5463" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine polled for changes to the active IPsec policy and detected no changes" },
    "5464" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine polled for changes to the active IPsec policy, detected changes, and applied them to IPsec Services" },
    "5465" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine received a control for forced reloading of IPsec policy and processed the control successfully" },
    "5466" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine polled for changes to the Active Directory IPsec policy, determined that Active Directory cannot be reached, and will use the cached copy of the Active Directory IPsec policy instead. Any changes made to the Active Directory IPsec policy since the last poll could not be applied" },
    "5467" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine polled for changes to the Active Directory IPsec policy, determined that Active Directory can be reached, and found no changes to the policy. The cached copy of the Active Directory IPsec policy is no longer being used" },
    "5468" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine polled for changes to the Active Directory IPsec policy, determined that Active Directory can be reached, found changes to the policy, and applied those changes. The cached copy of the Active Directory IPsec policy is no longer being used" },
    "5471" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine loaded local storage IPsec policy on the computer" },
    "5472" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine failed to load local storage IPsec policy on the computer" },
    "5473" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine loaded directory storage IPsec policy on the computer" },
    "5474" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine failed to load directory storage IPsec policy on the computer" },
    "5477" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "PAStore Engine failed to add quick mode filter" },
    "5478" => AuditEvent { category: "System", criticality: "Low", summary: "IPsec Services has started successfully" },
    "5479" => AuditEvent { category: "System", criticality: "Low", summary: "IPsec Services has been shut down successfully. The shutdown of IPsec Services can put the computer at greater risk of network attack or expose the computer to potential security risks" },
    "5480" => AuditEvent { category: "System", criticality: "Medium", summary: "IPsec Services failed to get the complete list of network interfaces on the computer. This poses a potential security risk because some of the network interfaces may not get the protection provided by the applied IPsec filters. Use the IP Security Monitor snap-in to diagnose the problem" },
    "5483" => AuditEvent { category: "System", criticality: "Medium", summary: "IPsec Services failed to initialize RPC server. IPsec Services could not be started" },
    "5484" => AuditEvent { category: "System", criticality: "Medium", summary: "IPsec Services has experienced a critical failure and has been shut down. The shutdown of IPsec Services can put the computer at greater risk of network attack or expose the computer to potential security risks" },
    "5485" => AuditEvent { category: "System", criticality: "Medium", summary: "IPsec Services failed to process some IPsec filters on a plug-and-play event for network interfaces. This poses a potential security risk because some of the network interfaces may not get the protection provided by the applied IPsec filters. Use the IP Security Monitor snap-in to diagnose the problem" },
    "5632" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "A request was made to authenticate to a wireless network" },
    "5633" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "A request was made to authenticate to a wired network" },
    "5712" => AuditEvent { category: "Detailed Tracking", criticality: "Low", summary: "A Remote Procedure Call (RPC) was attempted" },
    "5827" => AuditEvent { category: "Miscellaneous", criticality: "Medium", summary: "The Netlogon service denied a vulnerable Netlogon secure channel connection from a machine account" },
    "5828" => AuditEvent { category: "Miscellaneous", criticality: "Medium", summary: "The Netlogon service denied a vulnerable Netlogon secure channel connection using a trust account" },
    "5888" => AuditEvent { category: "Object Access", criticality: "Low", summary: "An object in the COM+ Catalog was modified" },
    "5889" => AuditEvent { category: "Object Access", criticality: "Low", summary: "An object was deleted from the COM+ Catalog" },
    "5890" => AuditEvent { category: "Object Access", criticality: "Low", summary: "An object was added to the COM+ Catalog" },
    "6005" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The event log service was started. Indicates the system startup" },
    "6006" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The event log service was stopped. Indicates the proper system shutdown" },
    "6008" => AuditEvent { category: "Miscellaneous", criticality: "Low", summary: "The previous system shutdown was unexpected" },
    "6009" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Indicates the Windows product name, version, build number, service pack number, and operating system type detected at boot time" },
    "6013" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "The system uptime in seconds" },
    "6144" => AuditEvent { category: "Policy Change", criticality: "Low", summary: "Security policy in the group policy objects has been applied successfully" },
    "6145" => AuditEvent { category: "Policy Change", criticality: "Medium", summary: "One or more errors occurred while processing security policy in the group policy objects" },
    "6272" => AuditEvent { category: "Logon/Logoff", criticality: "Low", summary: "Network Policy Server granted access to a user" },
    "6273" => AuditEvent { category: "Logon/Logoff", criticality: "Medium", summary: "Network Policy Server denied access to a user" },
    "6274" => AuditEvent { category: "Logon/Logoff", criticality: "Medium", summary: "Network Policy Server discarded the request for a user" },
    "6275" => AuditEvent { category: "Logon/Logoff", criticality: "Medium", summary: "Network Policy Server discarded the accounting request for a user" },
    "6276" => AuditEvent { category: "Logon/Logoff", criticality: "Medium", summary: "Network Policy Server quarantined a user" },
    "6277" => AuditEvent { category: "Logon/Logoff", criticality: "Medium", summary: "Network Policy Server granted access to a user but put it on probation because the host did not meet the defined health policy" },
    "6278" => AuditEvent { category: "Logon/Logoff", criticality: "Medium", summary: "Network Policy Server granted full access to a user because the host met the defined health policy" },
    "6279" => AuditEvent { category: "Logon/Logoff", criticality: "Medium", summary: "Network Policy Server locked the user account due to repeated failed authentication attempts" },
    "6280" => AuditEvent { category: "Logon/Logoff", criticality: "Medium", summary: "Network Policy Server unlocked the user account" },
    "6281" => AuditEvent { category: "System", criticality: "Low", summary: "Code Integrity determined that the page hashes of an image file are not valid. The file could be improperly signed without page hashes or corrupt due to unauthorized modification. The invalid hashes could indicate a potential disk device error" },
    "6400" => AuditEvent { category: "System", criticality: "Low", summary: "BranchCache: Received an incorrectly formatted response while discovering availability of content" },
    "6401" => AuditEvent { category: "System", criticality: "Low", summary: "BranchCache: Received invalid data from a peer. Data discarded" },
    "6402" => AuditEvent { category: "System", criticality: "Low", summary: "BranchCache: The message to the hosted cache offering it data is incorrectly formatted" },
    "6403" => AuditEvent { category: "System", criticality: "Low", summary: "BranchCache: The hosted cache sent an incorrectly formatted response to the client's message to offer it data" },
    "6404" => AuditEvent { category: "System", criticality: "Low", summary: "BranchCache: Hosted cache could not be authenticated using the provisioned SSL certificate" },
    "6405" => AuditEvent { category: "System", criticality: "Low", summary: "BranchCache: %2 instance(s) of event id %1 occurred" },
    "6406" => AuditEvent { category: "System", criticality: "Low", summary: "%1 registered to Windows Firewall to control filtering for the following: %2" },
    "6407" => AuditEvent { category: "System", criticality: "Low", summary: "%1" },
    "6408" => AuditEvent { category: "System", criticality: "Low", summary: "Registered product %1 failed and Windows Firewall is now controlling the filtering for %2" },
    "6409" => AuditEvent { category: "System", criticality: "Low", summary: "BranchCache: A service connection point object could not be parsed" },
    "6410" => AuditEvent { category: "System", criticality: "Low", summary: "Code integrity determined that a file does not meet the security requirements to load into a process. This could be due to the use of shared sections or other issues" },
    "6416" => AuditEvent { category: "System", criticality: "Low", summary: "A new external device was recognized by the system" },
    "6417" => AuditEvent { category: "System", criticality: "Low", summary: "The FIPS mode crypto selftests succeeded" },
    "6418" => AuditEvent { category: "System", criticality: "Low", summary: "The FIPS mode crypto selftests failed" },
    "6419" => AuditEvent { category: "System", criticality: "Low", summary: "A request was made to disable a device" },
    "6420" => AuditEvent { category: "System", criticality: "Low", summary: "A device was disabled" },
    "6421" => AuditEvent { category: "System", criticality: "Low", summary: "A request was made to enable a device" },
    "6422" => AuditEvent { category: "System", criticality: "Low", summary: "A device was enabled" },
    "6423" => AuditEvent { category: "System", criticality: "Low", summary: "The installation of this device is forbidden by system policy" },
    "6424" => AuditEvent { category: "System", criticality: "Low", summary: "The installation of this device was allowed, after having previously been forbidden by policy" },
    "7009" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "a service timeout has occurred" },
    "7011" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "a service did not respond within the specified time" },
    "7023" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "the service terminated with the following error: The service terminated with the following service-specific error: Incorrect function" },
    "7024" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "the service terminated with service-specific error" },
    "7031" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "a service has stopped unexpectedly" },
    "7035" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "a service was successfully sent a start/Stop control" },
    "7036" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "a service has entered the running or stopped state" },
    "8191" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "Highest System-Defined Audit Message Value" },
    "11000" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Solicit" },
    "11001" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Advertise" },
    "11002" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Request" },
    "11003" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Confirm" },
    "11004" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Renew" },
    "11005" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Rebind" },
    "11006" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Decline" },
    "11007" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Release" },
    "11008" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Information Request" },
    "11009" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Scope Full" },
    "11010" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Started" },
    "11011" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Stopped" },
    "11012" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Audit log paused" },
    "11013" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Log File" },
    "11014" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Bad Address" },
    "11015" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Address is already in use" },
    "11016" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Client deleted" },
    "11017" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 DNS record not deleted" },
    "11018" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Expired" },
    "11019" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Leases Expired and Leases Deleted" },
    "11020" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Database cleanup begin" },
    "11021" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 Database cleanup end" },
    "11022" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DNS IPv6 Update Request" },
    "11023" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DNS IPv6 Update Failed" },
    "11024" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DNS IPv6 Update Successful" },
    "11028" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DNS IPv6 update request failed as the DNS update request queue limit exceeded" },
    "11029" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DNS IPv6 update request failed" },
    "11030" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 stateless client records purged" },
    "11031" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPv6 stateless client record is purged as the purge interval has expired for this client record" },
    "11032" => AuditEvent { category: "Miscellaneous", criticality: "Miscellaneous", summary: "DHCPV6 Information Request from IPV6 Stateless Client" },
    "24577" => AuditEvent { category: "Miscellaneous", criticality: "Low", summary: "Encryption of volume started" },
    "24578" => AuditEvent { category: "Miscellaneous", criticality: "Low", summary: "Encryption of volume stopped" },
    "24579" => AuditEvent { category: "Miscellaneous", criticality: "Low", summary: "Encryption of volume completed" },
    "24580" => AuditEvent { category: "Miscellaneous", criticality: "Low", summary: "Decryption of volume started" },
    "24581" => AuditEvent { category: "Miscellaneous", criticality: "Low", summary: "Decryption of volume stopped" },
    "24582" => AuditEvent { category: "Miscellaneous", criticality: "Low", summary: "Decryption of volume completed" },
    "24583" => AuditEvent { category: "Miscellaneous", criticality: "Low", summary: "Conversion worker thread for volume started" },
    "24584" => AuditEvent { category: "Miscellaneous", criticality: "Low", summary: "Conversion worker thread for volume temporarily stopped" },
    "24586" => AuditEvent { category: "Miscellaneous", criticality: "Medium", summary: "An error was encountered converting volume" },
    "24588" => AuditEvent { category: "Miscellaneous", criticality: "Low", summary: "The conversion operation on volume %2 encountered a bad sector error. Please validate the data on this volume" },
    "24592" => AuditEvent { category: "Miscellaneous", criticality: "Medium", summary: "An attempt to automatically restart conversion on volume %2 failed" },
    "24593" => AuditEvent { category: "Miscellaneous", criticality: "Medium", summary: "Metadata write: Volume %2 returning errors while trying to modify metadata. If failures continue, decrypt volume" },
    "24594" => AuditEvent { category: "Miscellaneous", criticality: "Medium", summary: "Metadata rebuild: An attempt to write a copy of metadata on volume %2 failed and may appear as disk corruption. If failures continue, decrypt volume" },
    "24595" => AuditEvent { category: "Miscellaneous", criticality: "Low", summary: "Volume %2 contains bad clusters. These clusters will be skipped during conversion" },
    "24621" => AuditEvent { category: "Miscellaneous", criticality: "Low", summary: "Initial state check: Rolling volume conversion transaction on %2" },
        m
    };
}
