{% load humanize %}
{% load tags-filters %}


<div class="row g-3" id="dropdown_hx_destination">
  {% create_id_for_htmx_indicator "visitors-of-domain" as id_for_htmx_indicator %}
  <div class="col-12">
    <div class="card custom-card shadow">
      <div class="card-header justify-content-between">
        {# left #}
        <div class="card-title">
          {% include '00-header-title.html' with mode="no-date" ttl=card_header_title %}
        </div>
        {# right #}
        {# <div></div> #}
      </div>
      <div class="card-body">
        <div class="full_height">
          <table class="table table-striped table-sm sortable">
            <thead class="{{class_for_thead}}">
              <tr>
                <th scope="col"></th>
                <th scope="col">IP</th>
                <th scope="col">Computer Name</th>
                <th scope="col">Real Name</th>
                <th scope="col">MAC Address</th>
                <th scope="col">Count</th></th>
              </tr>
            </thead>
            <tbody
              hx-include="#dropdown_form"
              hx-get="{% url "dns-visitors-of-domain-url" domain|quote_domain %}"
              hx-vals='{"date": "{{date_to_show}}"}'
              hx-trigger="intersect once"
              hx-swap="innerHTML"
              hx-indicator="#{{id_for_htmx_indicator}}"
            >
            </tbody>
          </table>

          {% include '00-htmx-indicator.html' with id_for_htmx_indicator=id_for_htmx_indicator %}
        </div>
      </div>
    </div>
  </div>
</div>


{% if from_dropdown %}
  {% include '00-set-cookies.html' %}
  {% comment %}
  {% include '00-set-dropdowns.html' %}
  {% endcomment %}
{% endif %}
