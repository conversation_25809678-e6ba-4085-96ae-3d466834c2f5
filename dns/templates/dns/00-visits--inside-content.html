{% load tags-filters %}
{% load humanize %}


<div class="row g-3" id="dropdown_hx_destination">
  {% create_id_for_htmx_indicator "visits" as id_for_htmx_indicator %}
  <div class="col-12">
    <div class="card custom-card shadow">
      <div class="card-header justify-content-between">
        {# left #}
        <div class="card-title">
          {% include '00-header-title.html' with mode="multi-date" ttl="" daysago=date_to_show|days_ago %}
        </div>
        {# right #}
        {% if page_has_scrollable_tables %}
          {% include '00-scrollable-table-icon.html' %}
        {% endif %}
      </div>
      <div class="card-body">
        <div class="full_height">
          <table class="table table-striped table-sm sortable visits_table_for_{{mode}}">
            <thead class="{{class_for_thead}}">
              <tr>
                {% for d_h in db_headers %}
                  <th scope="col">{{d_h}}</th>
                {% endfor %}
              </tr>
            </thead>
            <tbody>
              {% include 'dns/00-visits--rows.html' with id_for_htmx_indicator=id_for_htmx_indicator %}
            </tbody>
          </table>

          {% include '00-htmx-indicator.html' with id_for_htmx_indicator=id_for_htmx_indicator %}
        </div>
      </div>
    </div>
  </div>
</div>


{% if from_dropdown %}
  {% include '00-set-cookies.html' %}
  {% include '00-set-dropdowns.html' %}
{% endif %}
