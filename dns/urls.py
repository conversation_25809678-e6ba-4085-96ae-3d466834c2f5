from django.urls import path

from . import views


urlpatterns = [
    path('combined-overview/',                   views.combined_overview,  name='dns-combined-overview-url'),
    path('detailed-activity/',                   views.detailed_activity,  name='dns-detailed-activity-url'),
    path('detailed-report/',                     views.detailed_report,    name='dns-detailed-report-url'),
    path('detailed-trend/<str:table_slug>/',     views.detailed_trend,     name='dns-detailed-trend-url'),
    path('graphical-category/<str:chart_slug>/', views.graphical_category, name='dns-graphical-category-url'),
    path('graphical-overview/<str:mode>/',       views.graphical_overview, name='dns-graphical-overview-url'),
    path('overall-activity/',                    views.overall_activity,   name='dns-overall-activity-url'),
    path('overall-trend/',                       views.overall_trend,      name='dns-overall-trend-url'),
    path('search/',                              views.search,             name='dns-search-url'),
    path('tabular-category/<str:table_slug>/',   views.tabular_category,   name='dns-tabular-category-url'),
    path('tabular-overview/',                    views.tabular_overview,   name='dns-tabular-overview-url'),
    path('visitors-of-domain/<str:domain>/',     views.visitors_of_domain, name='dns-visitors-of-domain-url'),
    path('visits/<str:mode>/',                   views.visits,             name='dns-visits-url'),
]
