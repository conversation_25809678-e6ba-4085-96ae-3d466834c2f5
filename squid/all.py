all_tables = [
    {
        'column_name': 'Time',
        'slug': 'time',
        'table_id': 'time',
    },
    # {
    #     'column_name': 'Duration',
    #     'slug': 'duration',
    #     'table_id': 'duration',
    # },
    {
        'column_name': 'Source IP',
        'slug': 'source-ip',
        'table_id': 'source-ip',
    },
    {
        'column_name': 'Request Status',
        'slug': 'request-status',
        'table_id': 'request-status',
    },
    {
        'column_name': 'Status Code',
        'slug': 'status-code',
        'table_id': 'status-code',
    },
    # {
    #     'column_name': 'Transfer',
    #     'slug': 'transfer',
    #     'table_id': 'transfer',
    # },
    {
        'column_name': 'HTTP Method',
        'slug': 'http-method',
        'table_id': 'http-method',
    },
    {
        'column_name': 'URL',
        'slug': 'url',
        'table_id': 'url',
    },
    {
        'column_name': 'Client Identity',
        'slug': 'client-identity',
        'table_id': 'client-identity',
    },
    {
        'column_name': 'Peer Code',
        'slug': 'peer-code',
        'table_id': 'peer-code',
    },
    {
        'column_name': 'Destination IP',
        'slug': 'destination-ip',
        'table_id': 'destination-ip',
    },
    {
        'column_name': 'Content Type',
        'slug': 'content-type',
        'table_id': 'content-type',
    },
    {
        'column_name': 'Transfer Report',
        'slug': 'transfer-report',
        'table_id': 'transfer-report',
    },
    {
        'column_name': 'Duration Report',
        'slug': 'duration-report',
        'table_id': 'duration-report',
    },
]

all_charts = [
    {
        'chart_title': 'Time',
        'chart_slug': 'time',
    },
    # {
    #     'chart_title': 'Duration',
    #     'chart_slug': 'duration',
    # },
    {
        'chart_title': 'Source IP',
        'chart_slug': 'source-ip',
    },
    {
        'chart_title': 'Request Status',
        'chart_slug': 'request-status',
    },
    {
        'chart_title': 'Status Code',
        'chart_slug': 'status-code',
    },
    # {
    #     'chart_title': 'Transfer',
    #     'chart_slug': 'transfer',
    # },
    {
        'chart_title': 'HTTP Method',
        'chart_slug': 'http-method',
    },
    {
        'chart_title': 'URL',
        'chart_slug': 'url',
    },
    {
        'chart_title': 'Client Identity',
        'chart_slug': 'client-identity',
    },
    {
        'chart_title': 'Peer Code',
        'chart_slug': 'peer-code',
    },
    {
        'chart_title': 'Destination IP',
        'chart_slug': 'destination-ip',
    },
    {
        'chart_title': 'Content Type',
        'chart_slug': 'content-type',
    },
    {
        'chart_title': 'Transfer Report',
        'chart_slug': 'transfer-report',
    },
    {
        'chart_title': 'Duration Report',
        'chart_slug': 'duration-report',
    },
]
