from django.urls import path

from . import views


urlpatterns = [
    path('combined-overview/',                   views.combined_overview,  name='squid-combined-overview-url'),
    path('detailed-activity/',                   views.detailed_activity,  name='squid-detailed-activity-url'),
    path('detailed-report/',                     views.detailed_report,    name='squid-detailed-report-url'),
    path('detailed-trend/<str:table_slug>/',     views.detailed_trend,     name='squid-detailed-trend-url'),
    path('graphical-category/<str:chart_slug>/', views.graphical_category, name='squid-graphical-category-url'),
    path('graphical-overview/<str:mode>/',       views.graphical_overview, name='squid-graphical-overview-url'),
    path('overall-activity/',                    views.overall_activity,   name='squid-overall-activity-url'),
    path('overall-trend/',                       views.overall_trend,      name='squid-overall-trend-url'),
    path('search/',                              views.search,             name='squid-search-url'),
    path('tabular-category/<str:table_slug>/',   views.tabular_category,   name='squid-tabular-category-url'),
    path('tabular-overview/',                    views.tabular_overview,   name='squid-tabular-overview-url'),
    path('visitors-of-domain/<str:domain>/',     views.visitors_of_domain, name='squid-visitors-of-domain-url'),
    path('visits/<str:mode>/',                   views.visits,             name='squid-visits-url'),
]
