{% load tags-filters %}
{% load humanize %}

{% for item, count in right_col.items %}
  <tr
    id="htmx_fade_in"

    {% if forloop.last %}
      hx-include="#dropdown_form"
      hx-get="{% url "squid-visits-url" mode %}"
      {% if mode == "ip" %}
        hx-vals='{"page": "{{page_number|add:"1"}}", "ip": "{{ip}}"}'
      {% elif mode == "domain" %}
        hx-vals='{"page": "{{page_number|add:"1"}}", "domain": "{{domain}}"}'
      {% endif %}
      hx-trigger="intersect once"
      hx-swap="afterend"
    {% endif %}
  >
    <td>{% calculate_id_of_visits_table limit_to_show page_number forloop.counter True %}</td>
    <td>{{item}}</td>

    {% if mode == "domain" %}
      {# computer name #}
      <td
        class="text-nowrap"
        hx-get="{% url "base-get-computer-name-url" item %}"
        hx-trigger="intersect once"
        hx-swap="innerHTML"
      ></td>
      {# real name #}
      <td
        class="text-nowrap"
        hx-get="{% url "base-get-real-name-url" item %}"
        hx-trigger="intersect once"
        hx-swap="innerHTML"
      ></td>
    {% endif %}

    <td>{{count|intcomma}}</td>
  </tr>

  {% if forloop.last and right_col|length < limit_to_show %}
    {% include '00-no-results-found.html' with page_number=page_number|add:"1" %}
  {% endif %}
{% empty %}
  {% include '00-no-results-found.html' %}
{% endfor %}
