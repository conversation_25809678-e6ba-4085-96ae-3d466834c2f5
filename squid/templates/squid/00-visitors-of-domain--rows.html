{% load humanize %}


{% for ip, count in ips_and_counts.items %}
  <tr>
    <td sorttable_customkey="{{ forloop.counter }}">{{ forloop.counter|intcomma }}</td>

    {# ip #}
    <td>
      <div class="d-flex justify-content-between align-items-center">
        <div>{{ ip }}</div>
        {% if ip %}
          <div class="ms-2 action_menu">
            <a href="javascript:void(0);" data-bs-toggle="dropdown" class="btn btn-icon btn-sm btn-link text-muted text-decoration-none">
              <i class="ti ti-dots-vertical"></i>
            </a>
            <ul class="dropdown-menu">
              <li><a target="_blank" class="dropdown-item" href="{% url "dhcp-detailed-report-url" %}?Source IP={{ip}}&date={{date_to_show}}">DHCP Detailed Report</a></li>
              <li><a target="_blank" class="dropdown-item" href="{% url "dns-detailed-report-url" %}?Source IP={{ip}}&date={{date_to_show}}">DNS Detailed Report</a></li>
              <li><a target="_blank" class="dropdown-item" href="{% url "snort-detailed-report-url" %}?Source IP={{ip}}&date={{date_to_show}}">Snort Detailed Report</a></li>
              <li><a target="_blank" class="dropdown-item" href="{% url "filterlog-detailed-report-url" %}?Source IP={{ip}}&date={{date_to_show}}">FilterLog Detailed Report</a></li>
              <li><a target="_blank" class="dropdown-item" href="{% url "squid-detailed-report-url" %}?Source IP={{ip}}&date={{date_to_show}}">Squid Detailed Report</a></li>
            </ul>
          </div>
        {% endif %}
      </div>
    </td>

    {# computer name #}
    <td
      hx-get="{% url "base-get-computer-name-url" ip %}"
      hx-trigger="intersect once"
      hx-swap="innerHTML"
    ></td>
    {# real name #}
    <td
      hx-get="{% url "base-get-real-name-url" ip %}"
      hx-trigger="intersect once"
      hx-swap="innerHTML"
    ></td>
    {# mac address #}
    <td
      hx-get="{% url "base-get-mac-address-url" ip date_to_show %}"
      hx-trigger="intersect once"
      hx-swap="innerHTML"
    ></td>

    <td>{{ count|intcomma }}</td>
  </tr>
{% empty %}
  {% include '00-no-results-found.html' %}
{% endfor %}
