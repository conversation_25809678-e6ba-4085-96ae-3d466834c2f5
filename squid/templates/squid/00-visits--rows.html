{% load tags-filters %}

{% for item, count in item_counts_dict.items %}
  {% with dashified=item|dashify %}
    <tr
      id="htmx_fade_in"

      {% if forloop.last %}
        hx-include="#dropdown_form"
        hx-get="{% url "squid-visits-url" mode %}"
        hx-vals='{"page": "{{page_number|add:"1"}}"}'
        hx-trigger="intersect once"
        hx-swap="afterend"
        hx-indicator="#{{id_for_htmx_indicator}}"
      {% endif %}
    >
      <td class="align-top">{% calculate_id_of_visits_table limit_to_show page_number forloop.counter True %}</td>
      <td class="align-top">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            {{ item }}
            {# commented because this number does not match (i.e. is larger than) #}
            {# the actual number of unrepeated domains #}
            {# ({{count|intcomma}}) #}
          </div>
          {% if item %}
            {% if mode == "domain" %}
              <div class="d-flex align-items-center">
                {% with item|quote_domain as quoted_domain %}
                  <div
                    class="text-end small ms-3 d-block d-md-inline computer_name_or_flag"
                    hx-get="{% url "base-get-country-of-domain-url" quoted_domain %}"
                    hx-trigger="intersect once"
                    hx-swap="innerHTML"
                  ></div>
                {% endwith %}

                <div class="ms-2 action_menu">
                  <a href="javascript:void(0);" data-bs-toggle="dropdown" class="btn btn-icon btn-sm btn-link text-muted text-decoration-none">
                    <i class="ti ti-dots-vertical"></i>
                  </a>
                  <ul class="dropdown-menu">
                    <li><a target="_blank" class="dropdown-item" href="{% url "general-lookup-url" %}?look-up={{item}}">Lookup</a></li>
                    <li><a target="_blank" class="dropdown-item" href="{% url "malicious-detailed-report-url" "domain" %}?Domain={{item}}">Malicious - Domain</a></li>
                    <li><a target="_blank" class="dropdown-item" href="{% url "geolocation-detailed-report-url" "domain" %}?Domain={{item}}">GeoLocation - Domain</a></li>
                  </ul>
                </div>
              </div>
            {% endif %}
          {% endif %}
        </div>
      </td>

      {% if mode == "ip" %}
        {# computer name #}
        <td
          class="text-nowrap align-top"
          hx-get="{% url "base-get-computer-name-url" item %}"
          hx-trigger="intersect once"
          hx-swap="innerHTML"
        ></td>
        {# real name #}
        <td
          class="text-nowrap align-top"
          hx-get="{% url "base-get-real-name-url" item %}"
          hx-trigger="intersect once"
          hx-swap="innerHTML"
        ></td>
      {% endif %}

      {# Action #}
      <td class="align-top">
        {# from navbar.html #}
        <button
          type="button"
          class="btn btn-sm btn-light btn-wave w-100"
          data-bs-toggle="collapse"
          data-bs-target="#domainsorips-and-counts-{{dashified}}"
          aria-controls="domainsorips-and-counts-{{dashified}}"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <i class="bi bi-chevron-right"></i>
        </button>
      </td>

      {# Domains/IPs #}
      <td class="align-top">
        <div
          class="collapse"
          id="domainsorips-and-counts-{{dashified}}"

          hx-include="#dropdown_form"
          hx-get="{% url "squid-visits-url" mode %}"
          {% if mode == "ip" %}
            hx-vals='{"page": "1", "ip": "{{item}}"}'
          {% elif mode == "domain" %}
            hx-vals='{"page": "1", "domain": "{{item}}"}'
          {% endif %}
          hx-trigger="intersect once"
          hx-swap="innerHTML"
        >
        </div>
      </td>
    </tr>
  {% endwith %}
{% empty %}
  {% comment %}first time page loads{% endcomment %}

  {% comment %}
    to make sure this block fires only on first hx request.
    without this, when rows is empty (e.g. because the date by dates section has not been parsed yet)
    it keeps generating this block and, in turn, sending hx requests infinitely
  {% endcomment %}
  {% if page_number == 0 %}

    {% comment %}
      this is a tr with empty cells and its purpose is to send the initial hx query
      so py-0 and d-none are added to make it not visible
    {% endcomment %}
    <tr
      id="htmx_fade_in"
      class="py-0"

      hx-include="#dropdown_form"
      hx-get="{% url "squid-visits-url" mode %}"
      hx-vals='{"page": "{{page_number|add:"1"}}"}'
      hx-trigger="intersect once"
      hx-swap="afterend"
      hx-indicator="#{{id_for_htmx_indicator}}"

      data-to_be_deleted_tr="true"
    >
      <td class="d-none" colspan="{{db_headers|length}}"></td>
    </tr>

  {% else %}
    {% include '00-no-results-found.html' %}
  {% endif %}

{% endfor %}
