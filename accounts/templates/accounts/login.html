{% extends 'dashboard.html' %}
{% load static %}

{% block login %}
  <div class="row authentication mx-0">

      <div class="col-xxl-5 col-xl-5 col-lg-12">
          <div class="row justify-content-center align-items-center h-100">
              <div class="col-xxl-6 col-xl-9 col-lg-6 col-md-6 col-sm-8 col-12">
                  <div class="card custom-card shadow my-4">
                      <div class="card-body p-4">
                          <div class="mb-3 d-flex justify-content-center">
                              <a href="{{request.path}}">
                                  <img src="{% static "files/project-logo/logo.png" %}" alt="logo" class="authentication-brand desktop-logo">
                                  <img src="{% static "files/project-logo/logo.png" %}" alt="logo" class="authentication-brand desktop-dark">
                              </a>
                          </div>
                          <p class="h5 mb-2 text-center">Welcome to {{project_title}}</p>
                          <p class="mb-5 text-muted op-7 fw-normal text-center"     style="direction:ltr;">{% now "d M, Y" %}</p>

                          {% include 'accounts/00-form-errors--ul.html' %}

                          {% comment %}
                            login_form needed in custom.css
                            to set proper direction
                            for input and placeholder in rtl
                          {% endcomment %}
                          <form method="post" class="login_form">
                            {% csrf_token %}
                            <div class="row gy-3">
                              {% for fld in form %}
                                <div class="col-xl-12 login_input_wrapper">
                                  {{fld}}
                                </div>
                              {% endfor %}
                              <div class="d-grid mt-4">
                                <input type="submit" class="btn btn-lg btn-primary" type="submit" name="from_login_form" value="{{main_title}}">
                              </div>
                            </div>
                          </form>

                          <div class="text-center text-muted mt-5 mb-0     small" style="direction:ltr;">
                            {% include '00-copyright-and-credit.html' %}
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
      <div class="col-xxl-7 col-xl-7 col-lg-7 d-xl-block d-none px-0">
          <div class="authentication-cover bg-primary">
              <div>
                  <div class="authentication-cover-image">
                      <img src="{% static "udon/dist/assets/images/media/media-67.png" %}" alt="">
                  </div>
                  <div class="text-center mb-4">
                      <h1 class="text-fixed-white">{{main_title}}</h1>
                  </div>
              </div>
          </div>
      </div>

  </div>

  {# JUMP_1 #}
  <div
    id="usecaptcha"
    class="d-none"
    data-usecaptcha="{{usecaptcha}}"
  >
  </div>
{% endblock login %}

{% block scripts %}
  <script src="{% static "js/jquery-3.7.1.min.js" %}"></script>
  <script src="{% static "js/make-captcha-fields-flex.js" %}"></script>

  {# JUMP_1 #}
  <script src="{% static "js/use-captcha.js" %}"></script>
{% endblock scripts %}
