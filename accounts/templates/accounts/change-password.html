{% extends 'dashboard.html' %}
{% load static %}

{% block change_password %}
  <div class="row authentication mx-0">

      <div class="col-xxl-5 col-xl-5 col-lg-12">
          <div class="row justify-content-center align-items-center h-100">
              <div class="col-xxl-6 col-xl-9 col-lg-6 col-md-6 col-sm-8 col-12">
                  <div class="card custom-card shadow my-4">
                      <div class="card-body p-4">
                          <div class="mb-3 d-flex justify-content-center">
                              <a href="{{request.path}}">
                                  <img src="{% static "files/project-logo/logo.png" %}" alt="logo" class="authentication-brand desktop-logo">
                                  <img src="{% static "files/project-logo/logo.png" %}" alt="logo" class="authentication-brand desktop-dark">
                              </a>
                          </div>
                          <p class="h5 mb-2 text-center">Username: {{request.user.username}}</p>
                          <p class="mb-5 text-muted op-7 fw-normal text-center"     style="direction:ltr;">{% now "d M, Y" %}</p>

                          {% include 'accounts/00-form-errors--ul.html' %}

                          {% comment %}
                            login_form needed in custom.css
                            to set proper direction
                            for input and placeholder in rtl
                          {% endcomment %}
                          <form method="post" class="change_password_form">
                            {% csrf_token %}
                            <div class="row gy-3">
                              {% for fld in change_password_form %}
                                <div class="col-xl-12 login_input_wrapper">
                                  {{fld}}
                                </div>
                              {% endfor %}
                              <div class="d-grid mt-4">
                                <input type="submit" class="btn btn-lg btn-primary" type="submit" name="from_change_password_form" value="{{main_title}}">
                              </div>
                            </div>
                          </form>

                          <div class="text-center text-muted mt-5 mb-0     small" style="direction:ltr;">
                            {% include '00-copyright-and-credit.html' %}
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
      <div class="col-xxl-7 col-xl-7 col-lg-7 d-xl-block d-none px-0">
          <div class="authentication-cover bg-primary">
              <div>
                  <div class="authentication-cover-image">
                      <img src="{% static "udon/dist/assets/images/media/media-67.png" %}" alt="">
                  </div>
                  <div class="text-center mb-4">
                      <h1 class="text-fixed-white">{{main_title}}</h1>
                  </div>
                  <div class="btn-list text-center">
                      <a href="{% url "general-homepage-url" %}" class="btn btn-icon authentication-cover-icon btn-wave     w-100">
                          {# <i class="ri-home-line fw-bold"></i> #}
                          Home
                      </a>
                  </div>
              </div>
          </div>
      </div>

  </div>

{% endblock change_password %}

{% block scripts %}
{% endblock scripts %}
