from django.contrib import messages
from django.contrib.auth import (
    authenticate,
    login as login_,
    logout as logout_,
    update_session_auth_hash,
)
from django.contrib.auth.decorators import login_required
from django.shortcuts import (
    redirect,
    render,
)

from rahavard import (
    clear_messages,
    convert_string_True_False_None_0,
)

from .forms import (
    CustomAuthenticationForm__CAPTCHA,
    CustomAuthenticationForm__NO_CAPTCHA,
    CustomPasswordChangeForm,
)

from base.handlers import (
    bad_request,
)

from django.contrib.auth import get_user_model
User = get_user_model()


APP_TITLE = 'Accounts'
APP_SLUG  = 'accounts'


def login(request):
    if request.user.is_authenticated:
        return bad_request(request, error_msg='Log out First to Log in as a New User')

    usecaptcha = request.COOKIES.get('usecaptcha', False)
    usecaptcha = convert_string_True_False_None_0(usecaptcha)  ## because it may be 'False'

    form_errors = None

    if request.method == 'POST':

        ## NOTE JUMP_1 in this application,
        ##             we exceptionally use this if statement for post requests
        ##             because there are post requests sent by selects on sidebar
        ##             with the ids of {user,category,priority}-id-select in main.html
        ##             these post requests get problematic when there are already other forms on page
        ##             e.g. for creating/replying to ticket, changing password, etc.
        ##             so in this app, we add this if statement to each post request
        ##             to make sure the request is actually coming from the form on the page
        ##             and not from the selects on the sidebar
        if 'from_login_form' in request.POST:
            if usecaptcha:
                form = CustomAuthenticationForm__CAPTCHA(data=request.POST)  ## NOTE do NOT remove 'data='
            else:
                form = CustomAuthenticationForm__NO_CAPTCHA(data=request.POST)  ## NOTE do NOT remove 'data='

            if form.is_valid():
                username = form.cleaned_data.get('username').lower()
                password = form.cleaned_data.get('password')

                ## authenticate and login
                user = authenticate(request, username=username, password=password)
                if user is not None and user.is_active:
                    login_(request, user)
                    clear_messages(request)
                    return redirect('general-homepage-url')
                else:
                    clear_messages(request)
                    messages.error(request, 'Incorrect username and/or password')

                    ## JUMP_1 instead of this: return redirect('accounts-login-url')
                    ##        we set usecaptcha to True and let the rest of the function decide
                    usecaptcha = True
            else:
                ## {'new_password2': [{'message': 'The two password fields didn’t match.', 'code': 'password_mismatch'}]}
                form_errors = form.errors.get_json_data()

                ## JUMP_1
                usecaptcha = True

    if usecaptcha:
        form = CustomAuthenticationForm__CAPTCHA()
    else:
        form = CustomAuthenticationForm__NO_CAPTCHA()

    return render(
        request,
        f'{APP_SLUG}/login.html',
        context={
            'main_title': 'Log in',
            'form': form,
            'form_errors': form_errors,
            'usecaptcha': usecaptcha,
        }
    )

@login_required
def logout(request):
    logout_(request)
    return redirect('accounts-login-url')

@login_required
def change_password(request):
    form_errors = None

    ## https://simpleisbetterthancomplex.com/tips/2016/08/04/django-tip-9-password-change-form.html
    if request.method == 'POST':
        if 'from_change_password_form' in request.POST:
            form = CustomPasswordChangeForm(request.user, request.POST)
            if form.is_valid():
                user = form.save()
                update_session_auth_hash(request, user)  ## Important!
                clear_messages(request)
                messages.success(request, 'Password changed successfully')
                return redirect('general-homepage-url')
            else:
                ## {'new_password2': [{'message': 'The two password fields didn’t match.', 'code': 'password_mismatch'}]}
                form_errors = form.errors.get_json_data()

    return render(
        request,
        f'{APP_SLUG}/change-password.html',
        context={
            'main_title': 'Change Password',
            'change_password_form': CustomPasswordChangeForm(request.user),
            'form_errors': form_errors,
            'is_changing_password': True,
        }
    )
