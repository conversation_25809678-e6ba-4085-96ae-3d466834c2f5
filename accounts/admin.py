from django.contrib import admin
from django.contrib.auth.admin import UserAdmin

from rahavard import (
    ADMIN_PY__LIST_DISPLAY_LINKS,
    ADMIN_PY__USER_LIST_FILTER,
    ADMIN_PY__USER_READONLY_FIELDS,

    make_active,
    make_inactive,
)

from unfold.admin import ModelAdmin as unfoldModelAdmin

from django.contrib.auth import get_user_model
User = get_user_model()


@admin.register(User)
class CustomUserAdmin(UserAdmin, unfoldModelAdmin):
    ## so that custom fields are visible and can be populated in admin panel
    ## (https://stackoverflow.com/questions/48011275/custom-user-model-fields-abstractuser-not-showing-in-django-admin)
    fieldsets = [
        *UserAdmin.fieldsets,  ## original form fieldsets, expanded
                               ## new fieldset added on to the bottom
        (
            'Other',  ## tab title
            {
                'fields': (
                    'company',
                    'position',
                    'description',
                    'gender',
                    'is_limited_admin',
                    # 'short_uuid',  ## commented because it is non-editable and therefore not allowed here
                ),
            },
        ),
    ]
    list_display = [
        'id', 'short_uuid',
        'username', 'first_name', 'last_name', 'email', 'is_superuser', 'is_staff', 'is_active',
        'company', 'position', 'description', 'gender', 'is_limited_admin',
    ]
    search_fields      = list_display
    list_display_links = ADMIN_PY__LIST_DISPLAY_LINKS
    readonly_fields    = ADMIN_PY__USER_READONLY_FIELDS
    list_filter        = ADMIN_PY__USER_LIST_FILTER
    # list_editable    = []
    actions            = [make_active, make_inactive]
