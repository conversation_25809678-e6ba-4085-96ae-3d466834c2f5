# from django import forms
from django.contrib.auth.forms import (
    AuthenticationForm,
    PasswordChangeForm,
)

from captcha.fields import Captcha<PERSON>ield

from django.contrib.auth import get_user_model
User = get_user_model()


class CustomAuthenticationForm__CAPTCHA(AuthenticationForm):
    ## equivalents to classes (e.g. form-control form-control-lg, etc.) are manually set in custom.css
    captcha = CaptchaField()

    class Meta:
        model = User
        fields = [
            'username',
            'password',
        ]

    def __init__(self, *args, **kwargs):
        super(CustomAuthenticationForm__CAPTCHA, self).__init__(*args, **kwargs)  ## NOTE do NOT pass self for __init__
        self.fields['username'].widget.attrs.update({'class': 'form-control form-control-lg', 'placeholder': 'username'})
        self.fields['password'].widget.attrs.update({'class': 'form-control form-control-lg', 'placeholder': 'password'})


class CustomAuthenticationForm__NO_CAPTCHA(AuthenticationForm):
    class Meta:
        model = User
        fields = [
            'username',
            'password',
        ]

    def __init__(self, *args, **kwargs):
        super(CustomAuthenticationForm__NO_CAPTCHA, self).__init__(*args, **kwargs)  ## NOTE do NOT pass self for __init__
        self.fields['username'].widget.attrs.update({'class': 'form-control form-control-lg', 'placeholder': 'username'})
        self.fields['password'].widget.attrs.update({'class': 'form-control form-control-lg', 'placeholder': 'password'})


class CustomPasswordChangeForm(PasswordChangeForm):
    class Meta:
        model = User
        fields = [
            'old_password',
            'new_password1',
            'new_password2',
        ]

    def __init__(self, *args, **kwargs):
        super(CustomPasswordChangeForm, self).__init__(*args, **kwargs)  ## NOTE do NOT pass self for __init__
        self.fields['old_password'].widget.attrs.update({'class': 'form-control form-control-lg', 'placeholder': 'old password'})
        self.fields['new_password1'].widget.attrs.update({'class': 'form-control form-control-lg', 'placeholder': 'new password'})
        self.fields['new_password2'].widget.attrs.update({'class': 'form-control form-control-lg', 'placeholder': 'confirm new password'})
