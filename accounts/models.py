from django.contrib.auth.models import (
    AbstractUser,
    UserManager,
)
from django.db import models

from rahavard import create_short_uuid

from base.models import GetActiveObjects


class User(AbstractUser):
    class Meta:
        ordering = ['id']  ## oldest on top
        db_table = 'auth_user'  ## as suggested by https://www.youtube.com/watch?v=oGGjoerX_2A

    class GenderOptions(models.TextChoices):
        MALE   = 'M', 'Male'
        FEMALE = 'F', 'Female'

    company     = models.CharField(max_length=200, null=True, blank=True)
    position    = models.CharField(max_length=200, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    gender      = models.CharField(max_length=1, choices=GenderOptions.choices, default=GenderOptions.MALE)

    is_limited_admin = models.BooleanField(default=False, verbose_name='Is Limited Admin')

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')

    objects        = UserManager()
    active_objects = GetActiveObjects()
