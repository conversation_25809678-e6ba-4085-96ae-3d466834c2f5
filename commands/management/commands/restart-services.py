from django.conf import settings
from django.core.management.base import BaseCommand

from signal import SIGIN<PERSON>, signal
from subprocess import run
from time import sleep, perf_counter

from rahavard import (
    abort,
    convert_second,
    get_command,
    get_command_log_file,
    keyboard_interrupt_handler,
    save_log,
)

from base.utils_constants import (
    MAX_TRIES,
)

from base.utils import (
    command_instance_is_running,
)


signal(SIGINT, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = 'Restart Services'

    def handle(self, *args, **kwargs):
        command = get_command(full_path=__file__, drop_extention=True)
        log_file = get_command_log_file(command)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        start = perf_counter()

        for service_name in [
            ## maybe better keep order
            'syslog-ng',
            'mysql-server',
            'live_parse',
        ]:
            restart_try = 0
            successful = False

            while not successful and restart_try < MAX_TRIES.restart:
                restart_try += 1
                try:
                    cmd = run(
                        f'sudo service {service_name} restart',
                        shell=True,

                        ## commented to see messages
                        # universal_newlines=True,
                        # capture_output=True,  ## JUMP_1
                    )

                    if cmd.returncode == 0:  ## returncode 0 means everything is fine
                        log_msg = f'{service_name} restart successful (try {restart_try})'
                        successful = True
                    else:
                        ## NOTE: can't use cmd.stderr.strip() as message because
                        ##       we've commented capture_output in JUMP_1
                        raise Exception()

                except Exception as exc:
                    log_msg = f'{service_name} restart failed (try {restart_try}): {exc!r}'

                save_log(self, command, settings.HOST_NAME, log_file, log_msg)

                sleep(1)

        end = perf_counter()
        duration = int(end - start)

        save_log(self, command, settings.HOST_NAME, log_file, f'done in {duration:,} seconds ({convert_second(seconds=duration, verbose=False)})')
