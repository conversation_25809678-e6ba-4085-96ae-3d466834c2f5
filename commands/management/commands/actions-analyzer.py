from django.conf import settings
from django.core.management import call_command
from django.core.management.base import BaseCommand

from datetime import datetime
from json import dumps
from os import path
from signal import SIGINT, signal
from subprocess import run

from natsort import natsorted
from rahavard import (
    abort,
    add_yearmonthday_force,
    get_command,
    get_command_log_file,
    is_allowed,
    keyboard_interrupt_handler,
    save_log,
)

from base.utils_classes import (
    DaemonConfig,
    DHCPConfig,
    DNSConfig,
    FilterLogConfig,
    RouterBoardConfig,
    RouterConfig,
    SnortConfig,
    SquidConfig,
    SwitchConfig,
    UserAuditConfig,
    UserNoticeConfig,
    UserWarningConfig,
    VMwareConfig,
    VPNServerConfig,
    WindowsServerConfig,
)

from base.utils_extra import (
    get_vpnserver_object,
)

from base.utils_database import (
    get_databases_and_sizes,
)

from base.utils import (
    get_list_of_files,
    get_parsed_dirs,
    get_size_of_source_log,
)

from base.models import (
    Router,
    RouterBoard,
    Sensor,
    Switch,
    VMware,
    WindowsServer,
)


ACTION_OPTIONS = [
    'statistics',
    'parse',
    'hourly-parse',
]


signal(SIGINT, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = 'Actions Analyzer'

    def add_arguments(self, parser):
        add_yearmonthday_force(parser, for_mysql=False)

        parser.add_argument(
            '-a',
            '--action',
            default=None,
            type=str,
            help=f'action (options: {",".join(ACTION_OPTIONS)})',
        )

        parser.add_argument(
            '-o',
            '--only',
            default=[],
            nargs='+',
            type=str,
            help='only',
        )

        parser.add_argument(
            '-e',
            '--exclude',
            default=[],
            nargs='+',
            type=str,
            help='exclude. Note: it overrides -o|--only args',
        )

        parser.add_argument(
            '-p',
            '--proxy',
            default=False,
            action='store_true',
            help='Use proxy',
        )

        parser.add_argument(
            '-r',
            '--restart-services',
            default=False,
            action='store_true',
            help='Restart Services',
        )

    def handle(self, *args, **kwargs):
        year_months     = kwargs.get('year_months')
        year_month_days = kwargs.get('year_month_days')

        start_year_month     = kwargs.get('start_year_month')
        start_year_month_day = kwargs.get('start_year_month_day')

        end_year_month     = kwargs.get('end_year_month')
        end_year_month_day = kwargs.get('end_year_month_day')

        force = kwargs.get('force')

        if year_months:     year_months     = natsorted(set(year_months))
        if year_month_days: year_month_days = natsorted(set(year_month_days))

        if start_year_month and end_year_month:
            ## make sure start_year_month precedes end_year_month in time
            if start_year_month >= end_year_month:
                end_year_month = None

        if start_year_month_day and end_year_month_day:
            ## make sure start_year_month_day precedes end_year_month_day in time
            if start_year_month_day >= end_year_month_day:
                end_year_month_day = None

        ## to be used in JUMP_1
        ymd__force = {
            'year_months':          year_months,
            'year_month_days':      year_month_days,
            'start_year_month':     start_year_month,
            'start_year_month_day': start_year_month_day,
            'end_year_month':       end_year_month,
            'end_year_month_day':   end_year_month_day,
            'force':                force,
        }

        action           = kwargs.get('action')
        only             = kwargs.get('only')
        exclude          = kwargs.get('exclude')
        proxy            = kwargs.get('proxy')
        restart_services = kwargs.get('restart_services')

        #############################################################

        if not action:
            return abort(self, 'no action specified')

        if action not in ACTION_OPTIONS:
            return abort(self, 'invalid action')

        command  = get_command(full_path=__file__, drop_extention=True)
        log_file = get_command_log_file(f'{command}--{action}')
        ## .../actions--renew.log

        if action == 'statistics':
            aymdhms = datetime.now().strftime('%a %Y-%m-%d %H:%M:%S')

            ## 1/4: logs

            dic = {'aymdhms': aymdhms}

            try:
                source_logs = get_list_of_files(directory=settings.LOGS_DIR, extension='log')

                for source_log in source_logs:
                    root, base = path.split(source_log)
                    dic[base] = get_size_of_source_log(source_log)
            except Exception as exc:
                save_log(self, command, settings.HOST_NAME, log_file, f'command_name={command}: {exc!r}')


            with open(settings.LOGS_STATISTICS_FILE, 'w') as opened:
                dumped = dumps(dic, indent=2)
                opened.write(dumped + '\n')


            ## 2/4: databases

            dic = {'aymdhms': aymdhms}

            try:
                databases_and_sizes = get_databases_and_sizes(
                    include_builtins=False,
                    sort=True,
                    based_on='key',
                    reverse=False,
                    convert=False,
                )

                for database, size in databases_and_sizes.items():
                    dic[database] = size
            except Exception as exc:
                save_log(self, command, settings.HOST_NAME, log_file, f'command_name={command}: {exc!r}')


            with open(settings.DATABASES_STATISTICS_FILE, 'w') as opened:
                dumped = dumps(dic, indent=2)
                opened.write(dumped + '\n')


            ## 3/4: parsed dates

            dic = {'aymdhms': aymdhms}

            try:
                router_objects        = Router.active_objects.all()
                routerboard_objects   = RouterBoard.active_objects.all()
                sensor_objects        = Sensor.active_objects.all()
                switch_objects        = Switch.active_objects.all()
                vmware_objects        = VMware.active_objects.all()
                windowsserver_objects = WindowsServer.active_objects.all()

                for cls, objects in [
                    ## cls                objects
                    (DaemonConfig,        sensor_objects),
                    (FilterLogConfig,     sensor_objects),
                    (SnortConfig,         sensor_objects),
                    (SquidConfig,         sensor_objects),
                    (UserAuditConfig,     sensor_objects),
                    (UserNoticeConfig,    sensor_objects),
                    (UserWarningConfig,   sensor_objects),
                    (RouterBoardConfig,   routerboard_objects),
                    (RouterConfig,        router_objects),
                    (SwitchConfig,        switch_objects),
                    (VMwareConfig,        vmware_objects),
                    (WindowsServerConfig, windowsserver_objects),

                    (VPNServerConfig,     None),
                    (DHCPConfig,          None),
                    (DNSConfig,           None),
                ]:
                    title = cls.TITLE.value

                    if all([
                        title == VPNServerConfig.TITLE.value,
                        not get_vpnserver_object(),
                    ]):
                        continue

                    if title in [DHCPConfig.TITLE.value, DNSConfig.TITLE.value, VPNServerConfig.TITLE.value]:
                        src_dir = cls.get_logs_parsed_dir()
                        dic[title] = get_parsed_dirs(src_dir, reverse=False)
                    else:
                        for obj in objects:
                            src_dir = f'{cls.get_logs_parsed_dir()}/{obj.name}'
                            dic[f'{title} - {obj.name}'] = get_parsed_dirs(src_dir, reverse=False)
            except Exception as exc:
                save_log(self, command, settings.HOST_NAME, log_file, f'command_name={command}: {exc!r}')


            with open(settings.PARSED_DATES_STATISTICS_FILE, 'w') as opened:
                dumped = dumps(dic, indent=2)
                opened.write(dumped + '\n')


            ## 4/4: disk usage

            dic = {'aymdhms': aymdhms}

            total_space = 0
            used_space  = 0

            try:
                cmd = run(
                    ## https://forums.freebsd.org/threads/zfs-get-sizes-in-megabytes.89927/
                    'zpool get -p -H size',
                    shell=True, universal_newlines=True, capture_output=True,
                )
                ## '--> zroot   size    586263035904    -

                if cmd.returncode == 0:  ## returncode 0 means everything is fine
                    total_space = int(cmd.stdout.strip().split('\t')[2])  ## 586263035904
            except Exception as exc:
                total_space = 0
                save_log(self, command, settings.HOST_NAME, log_file, f'command_name={command}: {exc!r}')

            try:
                ## didn't add -h because:
                ##   a. we need size in bytes
                ##   b. it makes size completely wrong
                du_switches = '-B 1 -s -x'  ## -h
                ##
                if not settings.DEBUG:
                    du_switches += ' -A'

                cmd = run(
                    f'sudo du {du_switches} /',
                    shell=True, universal_newlines=True, capture_output=True,
                )
                ## '--> 1609107968    /

                if cmd.returncode == 0:  ## returncode 0 means everything is fine
                    used_space = int(cmd.stdout.strip().split('\t')[0])

                    ## although we have added '-B 1' to switches
                    ## to get used_space in bytes,
                    ## the output on production server
                    ## is still generated in kilobytes
                    ## so we need to manually turn it to bytes
                    if not settings.DEBUG:
                        used_space *= 1024
            except Exception as exc:
                used_space = 0
                save_log(self, command, settings.HOST_NAME, log_file, f'command_name={command}: {exc!r}')


            dic['Total'] = total_space
            dic['Used']  = used_space
            dic['Free']  = total_space - used_space

            with open(settings.DISK_USAGE_STATISTICS_FILE, 'w') as opened:
                dumped = dumps(dic, indent=2)
                opened.write(dumped + '\n')

        ## -----------------------------------
        elif action == 'parse':
            ## JUMP_1
            for command_name, switches in  [
                ## run before parse-*
                ('remove-corrupt-logs', {}),

                ('parse-daemon',        ymd__force),
                ('parse-dhcp',          ymd__force),
                ('parse-dns',           ymd__force),
                ('parse-filterlog',     ymd__force),
                ('parse-router',        ymd__force),
                ('parse-routerboard',   ymd__force),
                ('parse-snort',         ymd__force),
                ('parse-squid',         ymd__force),
                ('parse-switch',        ymd__force),
                ('parse-useraudit',     ymd__force),
                ('parse-usernotice',    ymd__force),
                ('parse-userwarning',   ymd__force),
                ('parse-vmware',        ymd__force),
                ('parse-vpnserver',     ymd__force),
                ('parse-windowsserver', ymd__force),

                ('fetch-malicious',          {}),
                ('fetch-geolocation-domain', {'proxy': proxy}),
                ('fetch-geolocation-ip',     {'proxy': proxy}),

                ('remove-old-logs-and-databases', {}),

                ## will be run only if restart_services is True
                ('restart-services', {}),
            ]:
                if command_name == 'restart-services' and not restart_services:
                    continue

                if not is_allowed(command_name, only, exclude):
                    continue

                try:
                    call_command(command_name, **switches)
                except Exception as exc:
                    msg = f'command_name={command_name}: {exc!r}'
                    save_log(self, command, settings.HOST_NAME, log_file, msg)

        ## -----------------------------------
        elif action == 'hourly-parse':
            for command_name, switches in [
                ('hourly-parse-daemon',        {}),
                ('hourly-parse-dhcp',          {}),
                ('hourly-parse-dns',           {}),
                ('hourly-parse-filterlog',     {}),
                ('hourly-parse-router',        {}),
                ('hourly-parse-routerboard',   {}),
                ('hourly-parse-snort',         {}),
                ('hourly-parse-squid',         {}),
                ('hourly-parse-switch',        {}),
                ('hourly-parse-useraudit',     {}),
                ('hourly-parse-usernotice',    {}),
                ('hourly-parse-userwarning',   {}),
                ('hourly-parse-vmware',        {}),
                ('hourly-parse-vpnserver',     {}),
                ('hourly-parse-windowsserver', {}),
            ]:
                if not is_allowed(command_name, only, exclude):
                    continue

                try:
                    call_command(command_name, **switches)
                except Exception as exc:
                    msg = f'command_name={command_name}: {exc!r}'
                    save_log(self, command, settings.HOST_NAME, log_file, msg)
