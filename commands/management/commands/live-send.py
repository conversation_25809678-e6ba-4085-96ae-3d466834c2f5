'''
  asyncio protocol docs:
  https://docs.python.org/3.12/library/asyncio-protocol.html#udp-echo-client

  socketserver docs:
  https://docs.python.org/3.12/library/socketserver.html

  https://stackoverflow.com/a/52546291/
'''

from django.conf import settings
from django.core.management.base import BaseCommand

from datetime import datetime
from os import path
from random import choice
from signal import SIGINT, signal
from time import sleep

import asyncio

from rahavard import (
    abort,
    get_command,
    get_command_log_file,
    get_list_of_files,
    keyboard_interrupt_handler,
    save_log,
    to_tilda,
)

from base.utils_constants import (
    ACTION_ON_ERROR,
)

from base.utils import (
    command_instance_is_running,
    get_terminal_width,
    replace_ymdhms_at_beginning_of_line,
)


signal(SIGINT, keyboard_interrupt_handler)


ARROW = '>'
ELLIPSIS = '...'
INTERVAL_DEFAULT = 1.0


class UDPSender:
    def __init__(self, rows, quiet, interval, on_con_lost):
        self.rows = rows
        self.quiet = quiet
        self.interval = interval
        self.on_con_lost = on_con_lost
        self.transport = None

    def _send_logs(self):
        while True:
            random_line = choice(self.rows)

            ## to make it look like it is generated now
            ymdhms = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            random_line = replace_ymdhms_at_beginning_of_line(random_line, ymdhms)

            self.transport.sendto(random_line.encode())

            full_message = f'{ARROW} SENT: {random_line.replace('\t', ' ')}'

            ## truncate random_line based on screen width
            ## before printing if necessary
            width = get_terminal_width()
            if len(full_message) > width:
                full_message = f'{full_message[:width - len(ELLIPSIS)]}{ELLIPSIS}'

            if not self.quiet:
                print(full_message)

            sleep(self.interval)

    def connection_made(self, transport):
        self.transport = transport
        self._send_logs()

    def connection_lost(self, exc):
        print('connection lost')
        self.on_con_lost.set_result(True)

    def error_received(self, error_msg):
        print(f'\nerror received: {error_msg}\n')


class Command(BaseCommand):
    help = 'Live Send'

    def add_arguments(self, parser):
        parser.add_argument(
            '-i',
            '--interval',
            default=INTERVAL_DEFAULT,
            type=float,
            help=f'interval in sec between sending logs, e.g. .5, 2, etc. (default: {int(INTERVAL_DEFAULT)})',
        )

        parser.add_argument(
            '-q',
            '--quiet',
            default=False,
            action='store_true',
            help='suppress printing sent logs',
        )

    def handle(self, *args, **kwargs):
        interval = kwargs.get('interval')
        quiet = kwargs.get('quiet')

        command = get_command(full_path=__file__, drop_extention=True)
        log_file = get_command_log_file(command)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        if not settings.DEBUG:
            return abort(self, f'{command} is only allowed on development server.')

        rows = []
        src_dir = settings.LOGS_LIVE_SEND_DIR

        if not path.exists(src_dir):
            return abort(self, f'{to_tilda(src_dir)} does not exist.')

        source_logs = get_list_of_files(directory=src_dir, extension='log')

        if not source_logs:
            return abort(self, 'no logs.')

        source_logs_len = f'{len(source_logs):,}'

        save_log(self, command, settings.HOST_NAME, log_file, f'reading {source_logs_len} source logs')

        for source_log_index, source_log in enumerate(source_logs, start=1):
            save_log(self, command, settings.HOST_NAME, log_file, f'  {source_log_index}/{source_logs_len} {to_tilda(source_log)}')
            try:
                with open(source_log, errors=ACTION_ON_ERROR) as f:
                    lines = f.read().splitlines()
                    rows.extend(lines)
            except Exception as exc:
                save_log(self, command, settings.HOST_NAME, log_file, f'  ERROR: {exc!r}')

        if not rows:
            return abort(self, 'no rows.')

        save_log(self, command, settings.HOST_NAME, log_file, f'read {len(rows):,} lines')

        ####################################

        async def run_udp_server():
            loop = asyncio.get_running_loop()
            on_con_lost = loop.create_future()

            transport, protocol = await loop.create_datagram_endpoint(
                lambda: UDPSender(rows, quiet, interval, on_con_lost),
                remote_addr=(
                    settings.LIVE_SEND_HOST,
                    settings.LIVE_SEND_PORT,
                ),
            )

            success_msg = f'UDP server sending to {settings.LIVE_SEND_HOST}:{settings.LIVE_SEND_PORT} ...'
            save_log(self, command, settings.HOST_NAME, log_file, success_msg)

            try:
                await on_con_lost
            except Exception as exc:
                error_msg = f'ERROR on {settings.LIVE_SEND_HOST}:{settings.LIVE_SEND_PORT} :: {exc!r}'
                save_log(self, command, settings.HOST_NAME, log_file, error_msg)
            finally:
                save_log(self, command, settings.HOST_NAME, log_file, 'closing...')
                transport.close()
                save_log(self, command, settings.HOST_NAME, log_file, 'closed')

        asyncio.run(run_udp_server())
