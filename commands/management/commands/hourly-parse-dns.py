from django.conf import settings
from django.core.management.base import BaseCommand

from collections import Counter
from datetime import datetime
from json import dumps
from operator import itemgetter
from os import path, makedirs
from signal import SIGINT, signal
from time import perf_counter, mktime

from MySQLdb import connect
from rahavard import (
    abort,
    colorize,
    convert_second,
    get_command,
    keyboard_interrupt_handler,
    save_log,
    sort_dict,
    to_tilda,
)

from base.utils_classes import (
    DNSConfig,
    DNSParser,
    MYSQLConfig,
)

from base.utils_database import (
    get_size_of_database,
    get_tables_and_sizes,
)

from base.utils import (
    all_values_are_0,
    command_instance_is_running,
    create_name_of_database,
    end_of_command_msg,
    get_today_ymd,
    hms_to_hourkey,
    separator,
)


signal(SIGINT, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = f'Hourly Parse {DNSConfig.TITLE.value}'

    def handle(self, *args, **kwargs):
        command = get_command(full_path=__file__, drop_extention=True)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        start = perf_counter()

        today_ymd = get_today_ymd()

        dest_dir          = f'{DNSConfig.get_logs_parsed_dir()}/{today_ymd}'
        # accomplished_file = f'{dest_dir}/{today_ymd}-accomplished.log'
        log_file          = f'{dest_dir}/{today_ymd}.log'

        database_name = create_name_of_database(DNSConfig.SLUG.value, today_ymd)
        instance      = DNSParser(slug=DNSConfig.SLUG.value, ymd=today_ymd)

        ## get instance.rows
        ## (throws exception if database does not exist)
        try:
            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    cur.execute(f'{DNSConfig.get_select_statement()};')
                    instance.rows = cur.fetchall()
        except:
            pass

        ## instance.rows = [
        ##     (1, '2024-08-29', '09:47:29', ...),
        ##     (2, '2024-08-29', '09:47:30', ...),
        ##     ...
        ## ]

        ## instance.rows can be empty
        ## even if reading database was successful
        if not instance.rows:
            return abort(self, f'empty rows from database {database_name}')


        ## needed for creating log_file
        if not path.exists(dest_dir):
            print(colorize(self, 'creating', f'creating {to_tilda(dest_dir)}'))
            makedirs(dest_dir)



        ## __INDEXES_ONE_OFF__

        save_log(self, command, settings.HOST_NAME, log_file, 'preparing *_and_counts')

        for hms, count in Counter(map(itemgetter(1+1), instance.rows)).items():
            ## {'00:49:51': 12, '02:59:55': 1182, ...}
            ## ->
            ## {'00:00 - 00:59': 416787, '01:00 - 01:59': 416167, ...}
            instance.times_and_counts[hms_to_hourkey(hms)] += count

            ## -----

            ## today_ymd hms -> millisecond
            ## (2023-05-12 00:00:26 -> 1624973400000)
            try:
                ## a. today_ymd hms -> timestamp
                ##    (2023-05-12 00:00:26 -> 1624973400)
                timestamped = int(mktime(datetime.strptime(f'{today_ymd} {hms}', '%Y-%m-%d %H:%M:%S').timetuple()))

                ## b. timestamp -> millisecond
                ##    (1624973400 -> 1624973400000)
                timestamped *= 1000

                instance.milliseconds_and_counts[str(timestamped)] = count
            except Exception:
                pass

        instance.udp_tcp_indicators_and_counts      = Counter(map(itemgetter(5+1),  instance.rows))
        instance.send_receive_indicators_and_counts = Counter(map(itemgetter(6+1),  instance.rows))
        instance.source_ips_and_counts              = Counter(map(itemgetter(7+1),  instance.rows))
        instance.responsecodes_and_counts           = Counter(map(itemgetter(13+1), instance.rows))
        instance.question_types_and_counts          = Counter(map(itemgetter(14+1), instance.rows))
        instance.question_names_and_counts          = Counter(map(itemgetter(15+1), instance.rows))

        ################################################
        ## *toptable

        with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:
                for dictionary, table_name, key in [
                    ## dictionary                                  table_name                      column/key
                    (instance.times_and_counts,                   'timetoptable',                 'Time'),
                    (instance.udp_tcp_indicators_and_counts,      'udptcpindicatortoptable',      '`UDP/TCP Indicator`'),
                    (instance.send_receive_indicators_and_counts, 'sendreceiveindicatortoptable', '`Send/Receive Indicator`'),
                    (instance.source_ips_and_counts,              'sourceiptoptable',             '`Source IP`'),
                    (instance.responsecodes_and_counts,           'responsecodetoptable',         'ResponseCode'),
                    (instance.question_types_and_counts,          'questiontypetoptable',         '`Question Type`'),
                    (instance.question_names_and_counts,          'questionnametoptable',         '`Question Name`'),

                    (instance.milliseconds_and_counts,            'millisecondtoptable',          'Millisecond'),
                ]:
                    if key in ['Time', 'Millisecond'] and all_values_are_0(dictionary):
                        dictionary = {}

                    if not dictionary:
                        continue

                    if key in ['Time', 'Millisecond']:
                        sorted_dict = sort_dict(dictionary, based_on='key', reverse=False)
                    else:
                        sorted_dict = sort_dict(dictionary, based_on='value', reverse=True)

                    table_columns = f'''
                        ID    {MYSQLConfig.ID_DATA_TYPE.value},
                        {key} {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                        Count {MYSQLConfig.COUNT_DATA_TYPE.value}'''
                    table_keys = f'{key},Count'
                    table_marks = '%s,%s'

                    ## DROP table
                    save_log(self, command, settings.HOST_NAME, log_file, f'dropping table {table_name}')
                    cur.execute(f'DROP TABLE IF EXISTS {table_name};')

                    save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                    cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                    save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(sorted_dict):,} rows into {table_name}')
                    cur.execute('START TRANSACTION;')
                    cur.executemany(
                        f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                        tuple(sorted_dict.items())
                    )

                conn.commit()

        ################################################
        ## __INDEXES_ONE_OFF__

        temp_list_1 = map(itemgetter(7+1, 15+1), instance.rows)
        ## temp_list_1 = [
        ##     ('**************', 'https://some.ir/d/2020/06/06/3/6043917.jpg'),
        ##     ('***************', 'error:transaction-end-before-headers'),
        ##     ('**************', 'http://download.windowsupdate.com/d/msdownload/update/others/2020/06/xx.cab'),
        ##     ...
        ## ]
        ##
        for ip_, domain_ in temp_list_1:
            instance.ips_domains[ip_].append(domain_)
        ## instance.ips_domains = {
        ##     '**************': ['*******:443', 'https://sentry.divar.cloud/api/5/envelope/?', ...],
        ##     '**************': ['http://download.windowsupdate.com/d/msdownload', 'https:443', 'error:transaction-end-before-headers', ...],
        ##     ...
        ## }


        temp_list_2 = map(itemgetter(15+1, 7+1), instance.rows)
        ## temp_list_2 = [
        ##     ('https://some.ir/d/2020/06/06/3/6043917.jpg', '**************'),
        ##     ('login.live.com:443', '*************'),
        ##     ('https://push.services.mozilla.com/', '**************'),
        ##     ...
        ## ]
        ##
        for domain_, ip_ in temp_list_2:
            instance.domains_ips[domain_].append(ip_)
        ## instance.domains_ips = {
        ##     'https://some.ir/d/2020/06/06/3/6043917.jpg': ['**************', '**************', '**************', ...],
        ##     'http://download.windowsupdate.com/d/msdownload': ['**************', '***************', '**************', ...],
        ##     ...
        ## }



        ## get domains counts for ips:
        for ip, domains_ in instance.ips_domains.items():
            ip__domains_and_counts = sort_dict(Counter(domains_), based_on='value', reverse=True)
            ## {'(6)portal(2)fb(3)com(0)': 10, '(3)api(8)facebook(3)com(0)': 1}

            instance.ips_domains_counts[ip] = ip__domains_and_counts


        ## get ips counts for domains:
        for domain, ips_ in instance.domains_ips.items():
            domain__ips_and_counts = sort_dict(Counter(ips_), based_on='value', reverse=True)
            ## {'*************': 16, '*******': 8, '*************': 4}

            instance.domains_ips_counts[domain] = domain__ips_and_counts

        ################################################

        table_name = 'ipdomainscountstable'
        table_columns = f'''
            ID              {MYSQLConfig.ID_DATA_TYPE.value},
            IP              {MYSQLConfig.DEFAULT_DATA_TYPE.value},
            DomainsCounts   {MYSQLConfig.DEFAULT_DATA_TYPE.value},
            `No of Domains` {MYSQLConfig.COUNT_DATA_TYPE.value}'''
        table_keys = 'IP,DomainsCounts,`No of Domains`'
        table_marks = '%s,%s,%s'
        with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:
                ## DROP table
                save_log(self, command, settings.HOST_NAME, log_file, f'dropping table {table_name}')
                cur.execute(f'DROP TABLE IF EXISTS {table_name};')

                save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(instance.ips_domains_counts):,} rows into {table_name}')
                cur.execute('START TRANSACTION;')
                for k, v in instance.ips_domains_counts.items():
                    cur.execute(
                        f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                        (k, dumps(v), len(v))
                        ## ^^ dumps(v) to turn dictionary of domains into string (__USING_DUMPS_LOADS__)
                    )
                conn.commit()



        table_name = 'domainipscountstable'
        table_columns = f'''
            ID          {MYSQLConfig.ID_DATA_TYPE.value},
            Domain      {MYSQLConfig.DEFAULT_DATA_TYPE.value},
            IPsCounts   {MYSQLConfig.DEFAULT_DATA_TYPE.value},
            `No of IPs` {MYSQLConfig.COUNT_DATA_TYPE.value}'''
        table_keys = 'Domain,IPsCounts,`No of IPs`'
        table_marks = '%s,%s,%s'
        with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:
                ## DROP table
                save_log(self, command, settings.HOST_NAME, log_file, f'dropping table {table_name}')
                cur.execute(f'DROP TABLE IF EXISTS {table_name};')

                save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(instance.domains_ips_counts):,} rows into {table_name}')
                cur.execute('START TRANSACTION;')
                for k, v in instance.domains_ips_counts.items():
                    cur.execute(
                        f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                        (k, dumps(v), len(v))
                        ## ^^ dumps(v) to turn dictionary of IPs into string (__USING_DUMPS_LOADS__)
                    )
                conn.commit()

        ################################################
        instance.truncate_all()
        ################################################

        save_log(self, command, settings.HOST_NAME, log_file, f'database: {database_name}, {get_size_of_database(database_name, convert=True)}')
        save_log(self, command, settings.HOST_NAME, log_file, f'tables: {str(get_tables_and_sizes(database_name, convert=True, reverse=True))}')

        ################################################

        ## create accomplished_file
        # save_log(self, command, settings.HOST_NAME, accomplished_file, 'accomplished', echo=False)

        end = perf_counter()
        duration = int(end - start)

        save_log(self, command, settings.HOST_NAME, log_file, f'accomplished in {duration:,} seconds ({convert_second(seconds=duration, verbose=False)})')
        print(separator())

        print(end_of_command_msg(self, command))
