from django.core.management.base import BaseCommand

from rahavard import (
    abort,
    get_command,
)

from base.utils_classes import (
    RouterBoardConfig,
)

from base.utils import (
    command_instance_is_running,
    end_of_command_msg,
)


class Command(BaseCommand):
    help = f'Hourly Parse {RouterBoardConfig.TITLE.value}'

    def handle(self, *args, **kwargs):
        command = get_command(full_path=__file__, drop_extention=True)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        ## NOTE this parser is empty because:
        ##      in parse-routerboard.py, there are no *toptable

        print(end_of_command_msg(self, command))
