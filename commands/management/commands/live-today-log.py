'''
creates log file for today date
and keeps replacing content with shuffled lines.
useful when checking live monitor page on local
'''

from django.conf import settings
from django.core.management.base import BaseCommand

from datetime import datetime
from os import path
from random import shuffle
from signal import SIGINT, signal
from time import sleep

from rahavard import (
    abort,
    get_command,
    get_command_log_file,
    get_list_of_files,
    keyboard_interrupt_handler,
    save_log,
    to_tilda,
)

from base.utils_constants import (
    ACTION_ON_ERROR,
)

from base.utils import (
    command_instance_is_running,
    get_today_ymd,
)


signal(SIGINT, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = 'Live Today Log'

    def handle(self, *args, **kwargs):
        command = get_command(full_path=__file__, drop_extention=True)
        log_file = get_command_log_file(command)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        if not settings.DEBUG:
            return abort(self, f'{command} is only allowed on development server.')

        rows = []
        src_dir = settings.LOGS_LIVE_SEND_DIR

        if not path.exists(src_dir):
            return abort(self, f'{to_tilda(src_dir)} does not exist.')

        source_logs = get_list_of_files(directory=src_dir, extension='log')

        if not source_logs:
            return abort(self, 'no logs.')

        source_logs_len = f'{len(source_logs):,}'

        save_log(self, command, settings.HOST_NAME, log_file, f'reading {source_logs_len} source logs')

        for source_log_index, source_log in enumerate(source_logs, start=1):
            save_log(self, command, settings.HOST_NAME, log_file, f'  {source_log_index}/{source_logs_len} {to_tilda(source_log)}')
            try:
                with open(source_log, errors=ACTION_ON_ERROR) as f:
                    lines = f.read().splitlines()
                    rows.extend(lines)
            except Exception as exc:
                save_log(self, command, settings.HOST_NAME, log_file, f'  ERROR: {exc!r}')

        if not rows:
            return abort(self, 'no rows.')

        save_log(self, command, settings.HOST_NAME, log_file, f'read {len(rows):,} lines')

        ####################################

        loop_count = 1

        today_ymd = get_today_ymd()

        today_weekday_short = datetime.now().strftime('%a')  ## Sat
        today_log = f'{settings.LOGS_DIR}/{today_ymd}--{today_weekday_short}.log'

        while True:
            print(f'Loop: {loop_count:,}')

            ## shuffle rows
            shuffle(rows)

            with open(today_log, 'w') as opened:
                for row in rows:
                    opened.write(row+'\n')

            loop_count += 1

            sleep(3)
