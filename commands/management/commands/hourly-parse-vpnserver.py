from django.conf import settings
from django.core.management.base import BaseCommand

from collections import Counter
from datetime import datetime
from operator import itemgetter
from os import path, makedirs
from signal import SIGINT, signal
from time import perf_counter, mktime

from MySQLdb import connect
from rahavard import (
    abort,
    colorize,
    convert_second,
    get_command,
    keyboard_interrupt_handler,
    save_log,
    sort_dict,
    to_tilda,
)

from base.utils_classes import (
    # FilterLogConfig,
    MYSQLConfig,
    VPNServerConfig,
    VPNServerParser,
)

from base.utils_database import (
    get_size_of_database,
    get_tables_and_sizes,
)

from base.utils_extra import (
    get_vpnserver_object,
)

from base.utils import (
    all_values_are_0,
    command_instance_is_running,
    create_name_of_database,
    end_of_command_msg,
    get_today_ymd,
    hms_to_hourkey,
    separator,
)


signal(SIGINT, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = f'Hourly Parse {VPNServerConfig.TITLE.value}'

    def handle(self, *args, **kwargs):
        if not get_vpnserver_object():
            return abort(self, f'no {VPNServerConfig.SLUG.value} object.')

        command = get_command(full_path=__file__, drop_extention=True)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        start = perf_counter()

        today_ymd = get_today_ymd()

        dest_dir          = f'{VPNServerConfig.get_logs_parsed_dir()}/{today_ymd}'
        # accomplished_file = f'{dest_dir}/{today_ymd}-accomplished.log'
        log_file          = f'{dest_dir}/{today_ymd}.log'

        database_name = create_name_of_database(VPNServerConfig.SLUG.value, today_ymd)
        instance      = VPNServerParser(slug=VPNServerConfig.SLUG.value, ymd=today_ymd)

        ## get instance.rows
        ## (throws exception if database does not exist)
        try:
            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    cur.execute(f'{VPNServerConfig.get_select_statement()};')
                    instance.rows = cur.fetchall()
        except:
            pass

        ## instance.rows = [
        ##     (1, '2024-08-29', '09:47:29', ...),
        ##     (2, '2024-08-29', '09:47:30', ...),
        ##     ...
        ## ]

        ## instance.rows can be empty
        ## even if reading database was successful
        if not instance.rows:
            return abort(self, f'empty rows from database {database_name}')


        ## needed for creating log_file
        if not path.exists(dest_dir):
            print(colorize(self, 'creating', f'creating {to_tilda(dest_dir)}'))
            makedirs(dest_dir)



        ## __INDEXES_ONE_OFF__

        save_log(self, command, settings.HOST_NAME, log_file, 'preparing *_and_counts')

        for hms, count in Counter(map(itemgetter(1+1), instance.rows)).items():
            ## {'00:49:51': 12, '02:59:55': 1182, ...}
            ## ->
            ## {'00:00 - 00:59': 416787, '01:00 - 01:59': 416167, ...}
            instance.times_and_counts[hms_to_hourkey(hms)] += count

            ## -----

            ## today_ymd hms -> millisecond
            ## (2023-05-12 00:00:26 -> 1624973400000)
            try:
                ## a. today_ymd hms -> timestamp
                ##    (2023-05-12 00:00:26 -> 1624973400)
                timestamped = int(mktime(datetime.strptime(f'{today_ymd} {hms}', '%Y-%m-%d %H:%M:%S').timetuple()))

                ## b. timestamp -> millisecond
                ##    (1624973400 -> 1624973400000)
                timestamped *= 1000

                instance.milliseconds_and_counts[str(timestamped)] = count
            except Exception:
                pass

        instance.domains_and_counts    = Counter(map(itemgetter(2+1), instance.rows))
        instance.usernames_and_counts  = Counter(map(itemgetter(3+1), instance.rows))
        instance.ports_and_counts      = Counter(map(itemgetter(4+1), instance.rows))
        instance.source_ips_and_counts = Counter(map(itemgetter(8+1), instance.rows))

        ################################################
        ## *toptable

        with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:
                for dictionary, table_name, key in [
                    ## dictionary                        table_name             column/key
                    (instance.times_and_counts,         'timetoptable',        'Time'),
                    (instance.domains_and_counts,       'domaintoptable',      'Domain'),
                    (instance.usernames_and_counts,     'usernametoptable',    'Username'),
                    (instance.ports_and_counts,         'porttoptable',        'Port'),
                    (instance.source_ips_and_counts,    'sourceiptoptable',    '`Source IP`'),

                    (instance.milliseconds_and_counts,  'millisecondtoptable', 'Millisecond'),
                ]:
                    if key in ['Time', 'Millisecond'] and all_values_are_0(dictionary):
                        dictionary = {}

                    if not dictionary:
                        continue

                    if key in ['Time', 'Millisecond']:
                        sorted_dict = sort_dict(dictionary, based_on='key', reverse=False)
                    else:
                        sorted_dict = sort_dict(dictionary, based_on='value', reverse=True)

                    table_columns = f'''
                        ID    {MYSQLConfig.ID_DATA_TYPE.value},
                        {key} {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                        Count {MYSQLConfig.COUNT_DATA_TYPE.value}'''
                    table_keys = f'{key},Count'
                    table_marks = '%s,%s'

                    ## DROP table
                    save_log(self, command, settings.HOST_NAME, log_file, f'dropping table {table_name}')
                    cur.execute(f'DROP TABLE IF EXISTS {table_name};')

                    save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                    cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                    save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(sorted_dict):,} rows into {table_name}')
                    cur.execute('START TRANSACTION;')
                    cur.executemany(
                        f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                        tuple(sorted_dict.items())
                    )

                conn.commit()

        ################################################
        ## __INDEXES_ONE_OFF__

        instance.username_aggregates = {
            usr_nam_: {
                'active_for': 0,
                'sent': 0,
                'received': 0,
            }
            for usr_nam_, _ in instance.usernames_and_counts.items()
        }

        temp_list_1 = map(itemgetter(3+1, 5+1, 6+1, 7+1), instance.rows)
        ## temp_list_1 = [
        ##     ('n.peterson', '3536', '34983', '36574'),
        ##     ('b.jackson', '1234', '54363738', '267'),
        ##     ...
        ## ]
        ##
        for username_, active_for_, sent_, received_ in temp_list_1:
            instance.username_aggregates[username_]['active_for'] += int(active_for_)
            instance.username_aggregates[username_]['sent']       += int(sent_)
            instance.username_aggregates[username_]['received']   += int(received_)
        ## instance.username_aggregates = {
        ##     'n.peterson': {
        ##         'active_for': 34637847,
        ##         'sent': 373839438,
        ##         'received': 27389273948,
        ##     },
        ##     ...
        ## }

        ################################################
        instance.username_aggregates = sort_dict(instance.username_aggregates, based_on='key', reverse=False)

        table_name = 'useraggregatestable'
        table_columns = f'''
            ID           {MYSQLConfig.ID_DATA_TYPE.value},
            Username     {MYSQLConfig.DEFAULT_DATA_TYPE.value},
            `Active For` {MYSQLConfig.DEFAULT_DATA_TYPE.value},
            Sent         {MYSQLConfig.DEFAULT_DATA_TYPE.value},
            Received     {MYSQLConfig.DEFAULT_DATA_TYPE.value}'''
        table_keys = 'Username,`Active For`,Sent,Received'
        table_marks = '%s,%s,%s,%s'
        with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:
                ## DROP table
                save_log(self, command, settings.HOST_NAME, log_file, f'dropping table {table_name}')
                cur.execute(f'DROP TABLE IF EXISTS {table_name};')

                save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(instance.username_aggregates):,} rows into {table_name}')
                cur.execute('START TRANSACTION;')
                for username_, info_ in instance.username_aggregates.items():
                    cur.execute(
                        f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                        (
                            username_,
                            info_['active_for'],
                            info_['sent'],
                            info_['received'],
                        )
                    )
                conn.commit()

        ################################################
        instance.truncate_all()
        ################################################

        save_log(self, command, settings.HOST_NAME, log_file, f'database: {database_name}, {get_size_of_database(database_name, convert=True)}')
        save_log(self, command, settings.HOST_NAME, log_file, f'tables: {str(get_tables_and_sizes(database_name, convert=True, reverse=True))}')

        ################################################

        ## create accomplished_file
        # save_log(self, command, settings.HOST_NAME, accomplished_file, 'accomplished', echo=False)

        end = perf_counter()
        duration = int(end - start)

        save_log(self, command, settings.HOST_NAME, log_file, f'accomplished in {duration:,} seconds ({convert_second(seconds=duration, verbose=False)})')
        print(separator())

        print(end_of_command_msg(self, command))
