from django.conf import settings
from django.core.management.base import BaseCommand

from os import path, remove
from signal import SIGINT, signal

from rahavard import (
    abort,
    get_command,
    get_command_log_file,
    get_list_of_files,
    keyboard_interrupt_handler,
    save_log,
    to_tilda,
)

from base.utils_constants import (
    MIN_LOG_SIZE,
)

from base.utils import (
    command_instance_is_running,
    get_date_of_source_log,
    get_size_of_source_log,
    get_today_ymd,
)


signal(SIGINT, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = 'Remove Corrupt Logs'

    def handle(self, *args, **kwargs):
        command = get_command(full_path=__file__, drop_extention=True)
        log_file = get_command_log_file(command)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        if settings.DEBUG:
            return abort(self, f'{command} is only allowed on production server.')

        today_ymd = get_today_ymd()

        src_logs_dir = settings.LOGS_DIR

        if not path.exists(src_logs_dir):
            return abort(self, f'{src_logs_dir} does not exist.')

        source_logs = get_list_of_files(directory=src_logs_dir, extension='log')

        if not source_logs:
            return abort(self, 'no logs.')

        for source_log in source_logs:
            should_rm = False
            log_date = get_date_of_source_log(source_log)

            if log_date < today_ymd:
                log_size = get_size_of_source_log(source_log)
                if log_size <= MIN_LOG_SIZE:
                    should_rm = True
            elif log_date > today_ymd:
                should_rm = True

            if not should_rm:
                continue

            save_log(self, command, settings.HOST_NAME, log_file, f'removing {to_tilda(source_log)}')
            remove(source_log)
