from django.core.management.base import BaseCommand

from rahavard import (
    abort,
    get_command,
)

from base.utils_classes import (
    DaemonConfig,
)

from base.utils import (
    command_instance_is_running,
    end_of_command_msg,
)


class Command(BaseCommand):
    help = f'Hourly Parse {DaemonConfig.TITLE.value}'

    def handle(self, *args, **kwargs):
        command = get_command(full_path=__file__, drop_extention=True)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        ## NOTE this parser is empty because:
        ##      a. in parse-daemon.py, there are no *toptable
        ##      b. in parse-daemon.py, the only table other than *toptable is levelcounttable
        ##         which is used to display count for the day before
        ##         making it unnecessary to be parsed for the current day

        print(end_of_command_msg(self, command))
