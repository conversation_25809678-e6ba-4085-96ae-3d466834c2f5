'''
  asyncio protocol docs:
  https://docs.python.org/3.12/library/asyncio-protocol.html#udp-echo-server

  socketserver docs:
  https://docs.python.org/3.12/library/socketserver.html
'''


from django.conf import settings
from django.core.management.base import BaseCommand

from functools import lru_cache
from grp import getgrnam
from json import loads, dumps
from os import chown, getenv, path, makedirs
from re import compile as re_compile
from pwd import getpwnam
# from signal import signal, SIGINT

import asyncio

from MySQLdb import connect
from natsort import natsorted
from rahavard import (
    abort,
    get_command,
    get_command_log_file,
    # keyboard_interrupt_handler,
    save_log,
    # to_tilda,
)

from base.utils_classes import (
    DaemonConfig,
    DHCPConfig,
    DNSConfig,
    FilterLogConfig,
    MYSQLConfig,
    RouterBoardConfig,
    RouterConfig,
    SnortConfig,
    SquidConfig,
    SwitchConfig,
    User<PERSON>uditConfig,
    UserNoticeConfig,
    UserWarningConfig,
    VMwareConfig,
    VPNServerConfig,
    WindowsServerConfig,
)

from base.utils_constants import (
    LRU_CACHE_MAXSIZE,
)

from base.utils_extra import (
    get_vpnserver_object,
)

from base.utils_parsers import (
    parse_ln,
)

from base.utils import (
    command_instance_is_running,
    create_name_of_database,
    get_today_ymd,
    verbose_time_to_millisecond,
)

from base.models import (
    Router,
    RouterBoard,
    Sensor,
    StaticIP,
    Switch,
    VMware,
    WindowsServer,
)


# signal(SIGINT, keyboard_interrupt_handler)


## this command is set to be run by 'root' in live_parse service
## so that it can auto-start on boot.
## this makes the directories created by this command
## have the owner:group of root:<REMOTE_USERNAME>.
## as a result, hourly-parse-* commands,
## which are run by <REMOTE_USERNAME> and not by 'root',
## can't create *log files and crash.
## so we have to set proper owner:group
## for each directory created here
## (https://stackoverflow.com/a/5995007/)
if settings.DEBUG:
    USER_ID  = getpwnam(getenv('USER')).pw_uid
    GROUP_ID = getgrnam(getenv('USER')).gr_gid
else:
    USER_ID  = getpwnam(settings.PROJECT_SLUG).pw_uid
    GROUP_ID = getgrnam(settings.PROJECT_SLUG).gr_gid

## match timestamp like 2024-11-05 04:24:23
REAL_LOG_START_RE = re_compile(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}')

sensor_list_of_names        = Sensor.get_list_of_names()
switch_list_of_names        = Switch.get_list_of_names()
router_list_of_names        = Router.get_list_of_names()
routerboard_list_of_names   = RouterBoard.get_list_of_names()
vmware_list_of_names        = VMware.get_list_of_names()
windowsserver_list_of_names = WindowsServer.get_list_of_names()

sensor_list_of_names_and_addresses        = Sensor.get_list_of_names_and_addresses()
switch_list_of_names_and_addresses        = Switch.get_list_of_names_and_addresses()
router_list_of_names_and_addresses        = Router.get_list_of_names_and_addresses()
routerboard_list_of_names_and_addresses   = RouterBoard.get_list_of_names_and_addresses()
vmware_list_of_names_and_addresses        = VMware.get_list_of_names_and_addresses()
windowsserver_list_of_names_and_addresses = WindowsServer.get_list_of_names_and_addresses()

sensor_dict_of_addresses_and_names        = Sensor.get_dict_of_addresses_and_names()
switch_dict_of_addresses_and_names        = Switch.get_dict_of_addresses_and_names()
router_dict_of_addresses_and_names        = Router.get_dict_of_addresses_and_names()
routerboard_dict_of_addresses_and_names   = RouterBoard.get_dict_of_addresses_and_names()
vmware_dict_of_addresses_and_names        = VMware.get_dict_of_addresses_and_names()
windowsserver_dict_of_addresses_and_names = WindowsServer.get_dict_of_addresses_and_names()

mysqlconfig_db_name_separator_value = MYSQLConfig.DB_NAME_SEPARATOR.value

dhcpconfig_slug_value      = DHCPConfig.SLUG.value
dnsconfig_slug_value       = DNSConfig.SLUG.value
vpnserverconfig_slug_value = VPNServerConfig.SLUG.value

slugs_of_dhcp_and_dns_and_vpnserver = [
    dhcpconfig_slug_value,
    dnsconfig_slug_value,
    vpnserverconfig_slug_value,
]


real_names_and_virtual_addresses = dict(StaticIP.active_objects.values_list('real_name', 'virtual_address'))
## {
##     'n.peterson': '*******',
##     'a.jackson': '*******',
##     ...
## }

created_dbs = set()
def create_database_and_insert(database_name, table_name, db_columns, db_keys, db_marks, today_ymd, row):
    ## step 1: create database
    if database_name not in created_dbs:
        with connect(**MYSQLConfig.MASTER_CREDS.value) as conn:
            with conn.cursor() as cur:
                cur.execute(f'CREATE DATABASE IF NOT EXISTS {database_name};')
        created_dbs.add(database_name)

    ## step 2-1: check if row is a vpnserver row
    if database_name.startswith(f'{vpnserverconfig_slug_value}{mysqlconfig_db_name_separator_value}'):  ## __DATABASE_YMD_PATTERN__
        ## add/convert items for row

        ## find source ip (e.g. ***********)
        ## by username (e.g. n.peterson) which is row[3]
        ## using StaticIP objects
        source_ip_ = real_names_and_virtual_addresses.get(row[3]) or ''  ## ***********

        filterlog_destination_ips_ = set()

        if source_ip_:
            ## get filterlog destination ips from database
            ## (NOTE: unlike parse-vpnserver.py and hourly-parse-vpnserver.py
            ## we dont need to get all rows from database, but only the one row
            ## whose `Source IP` column matches source_ip_)
            for sensor in sensor_list_of_names:
                database_name_ = create_name_of_database(FilterLogConfig.SLUG.value, today_ymd, sensor)
                try:
                    with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name_) as conn:
                        with conn.cursor() as cur:
                            cur.execute('''
                                SELECT `Destination IPs`
                                FROM sourceipsdestinationipstable
                                WHERE (`Source IP` = %s)
                            ;''', (source_ip_,))

                            dest_ips_ = cur.fetchone()[0]
                            ## ["***********", "************"] (which is str)

                            ## dest_ips_ is a string turned (by loads) into a list (__USING_DUMPS_LOADS__)
                            filterlog_destination_ips_.update(loads(dest_ips_))
                except:
                    pass

            ## filterlog_destination_ips_ = {'*******', '*******'}

        row = (
            row[0],  ## date
            row[1],  ## time
            row[2],  ## domain
            row[3],  ## username
            row[4],  ## port
            verbose_time_to_millisecond(row[5]),  ## active for (227 minutes 14 seconds -> 45647465)
            row[6],  ## sent
            row[7],  ## received
            source_ip_,
            dumps(natsorted(filterlog_destination_ips_)),
            ## ^^ dumps() to turn set of destination ips into string (__USING_DUMPS_LOADS__)
        )


    ## step 2-2: insert
    ## *table
    with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
        with conn.cursor() as cur:
            cur.execute(f'CREATE TABLE IF NOT EXISTS {table_name} ({db_columns});')
            cur.execute(
                f'INSERT INTO {table_name} ({db_keys}) VALUES ({db_marks});',
                row
            )
            conn.commit()


@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def _create_dir_path(cls_logs_parsed_dir, cls_slug, object_name, today_ymd):
    if cls_slug in slugs_of_dhcp_and_dns_and_vpnserver:
        return f'{cls_logs_parsed_dir}/{today_ymd}'

    return f'{cls_logs_parsed_dir}/{object_name}/{today_ymd}'


created_paths = set()
def makedirs_and_chown(cls, cls_slug, object_name, today_ymd):
    dir_path = _create_dir_path(cls.get_logs_parsed_dir(), cls_slug, object_name, today_ymd)

    if dir_path in created_paths:
        return

    if path.exists(dir_path):
        created_paths.add(dir_path)
        return

    makedirs(dir_path)
    chown(dir_path, USER_ID, GROUP_ID)


''' NOTE __ORDER_OF_CLASSES__
  - keep order
  - sorted based on average number of logs

  7497000 snort
  1679000 filterlog
  402000  daemon
  ?       vpnserver  ## above JUMP_1
                     ## otherwise it won't get parsed
                     ## because it's actually a windowsserver line
  33000   windowsserver ## JUMP_1
  9195    dns
  1120    dhcp
  317     userwarning
  152     switch
  49      usernotice
  7       useraudit
  0       squid
  0       router
  0       routerboard
  0       vmware
'''
MAIN_LIST = []
##
for cls_ in [
    SnortConfig,
    FilterLogConfig,
    DaemonConfig,
    VPNServerConfig,
    WindowsServerConfig,  ## JUMP_1
    DNSConfig,
    DHCPConfig,
    UserWarningConfig,
    SwitchConfig,
    UserNoticeConfig,
    UserAuditConfig,
    SquidConfig,
    RouterConfig,
    RouterBoardConfig,
    VMwareConfig,
]:
    if all([
        cls_ in [VPNServerConfig],
        not get_vpnserver_object(),
    ]):
        continue

    if cls_ in [SnortConfig, FilterLogConfig, DaemonConfig, UserAuditConfig, UserNoticeConfig, UserWarningConfig, SquidConfig]:
        lona_ = sensor_list_of_names_and_addresses
        doan_ = sensor_dict_of_addresses_and_names
    elif cls_ is SwitchConfig:
        lona_ = switch_list_of_names_and_addresses
        doan_ = switch_dict_of_addresses_and_names
    elif cls_ is RouterConfig:
        lona_ = router_list_of_names_and_addresses
        doan_ = router_dict_of_addresses_and_names
    elif cls_ is RouterBoardConfig:
        lona_ = routerboard_list_of_names_and_addresses
        doan_ = routerboard_dict_of_addresses_and_names
    elif cls_ is VMwareConfig:
        lona_ = vmware_list_of_names_and_addresses
        doan_ = vmware_dict_of_addresses_and_names
    elif cls_ is WindowsServerConfig:
        lona_ = windowsserver_list_of_names_and_addresses
        doan_ = windowsserver_dict_of_addresses_and_names
    elif cls_ in [DHCPConfig, DNSConfig, VPNServerConfig]:
        lona_ = None
        doan_ = None
    else:
        continue

    cls_slug_ = cls_.SLUG.value

    if any([
        bool(lona_),

        ## DHCPConfig, DNSConfig, and VPNServerConfig should be added
        ## in spite of having no objects
        cls_slug_ in slugs_of_dhcp_and_dns_and_vpnserver,
    ]):
        MAIN_LIST.append((cls_, cls_slug_, lona_, doan_))

def strip_syslog_prefix(ln: str) -> str:
    '''
    Removes any leading syslog metadata from a log line and returns only
    the portion starting from the actual log content.

    This function is specifically designed for cases where syslog-forwarded
    log lines contain an initial ISO 8601-style timestamp (e.g., 
    "2024-11-05T22:37:58+03:30") followed by the real log's own timestamp 
    (e.g., "2024-11-05 4:24:23").

    Log line may look like this:
    <134>1 2024-11-05T22:37:58+03:30 Sensor1 filterlog 12345 - - 2024-11-05 4:24:23 Sensor1 ...
    <189>1 2024-11-05T04:24:23+03:30 Switch1 %PROTO-5 - - [meta seqId="83"] 2024-11-05 04:24:23 Switch1 ...

    It searches for all occurrences of timestamps matching the pattern 
    "YYYY-MM-DD HH:MM:SS" (with optional zero-padded or non-padded hour)
    and returns the substring of the log starting from the **last** such match.

    If no match is found, the original line is returned unchanged.

    This function proved to be pretty efficient speed-wise in benchmarks.
    It took 0.67 seconss for 100,000 sample lines.

    Args:
        ln (str): The full raw log line, possibly with a syslog prefix.

    Returns:
        str: The trimmed log line starting from the real log timestamp.
    
    Example:
        >>> strip_syslog_prefix('<134>1 2024-11-05T22:37:58+03:30 Sensor1 filterlog 12345 - - 2024-11-05 4:24:23 Sensor1 ...')
        '2024-11-05 4:24:23 Sensor1 ...'
        >>> strip_syslog_prefix('<189>1 2024-11-05T04:24:23+03:30 Switch1 %PROTO-5 - - [meta seqId="83"] 2024-11-05 04:24:23 Switch1 ...')
        '2024-11-05 04:24:23 Switch1 ...'
    '''
    ## __BY_AI__ generated by chatgpt

    matches = list(REAL_LOG_START_RE.finditer(ln))

    if len(matches) >= 1:
        ## keep from the **last** match onward
        return ln[matches[-1].start():]

    return ln

_PRECOMPUTED_DB_VALUES = {
    cls: {
        'db_columns': cls.DB_COLUMNS.value,
        'db_keys': cls.DB_KEYS.value,
        'db_marks': cls.DB_MARKS.value,
    }
    for cls in (
        FilterLogConfig,
        SnortConfig,
        DaemonConfig,
        VPNServerConfig,
        WindowsServerConfig,
        DNSConfig,
        DHCPConfig,
        UserWarningConfig,
        SwitchConfig,
        UserNoticeConfig,
        UserAuditConfig,
        SquidConfig,
        RouterConfig,
        RouterBoardConfig,
        VMwareConfig,
    )
}

def handle_ln(self, ln):
    today_ymd = get_today_ymd()

    for cls, cls_slug, lona, doan in MAIN_LIST:
        ## STEP 1/3: parse
        object_name, parsed_ln = parse_ln(
            strip_syslog_prefix(ln.strip()),
            cls,
            lona,
            doan,
        )

        if not parsed_ln:
            continue

        ## STEP 2/3: make dir and set proper owner/permission
        makedirs_and_chown(cls, cls_slug, object_name, today_ymd)

        ## STEP 3/3: insert into database
        database_name = create_name_of_database(cls_slug, today_ymd, object_name)
        create_database_and_insert(
            database_name,
            cls.get_table_name(),
            **_PRECOMPUTED_DB_VALUES[cls],  ## db_columns, db_keys, db_marks
            today_ymd=today_ymd,
            row=parsed_ln,
        )
        ##
        if cls is WindowsServerConfig:
            ## categorized
            ## __INDEXES_ONE_OFF__
            event_id = parsed_ln[4]
            if event_id and event_id.isdigit():
                if   event_id in cls.EVENT_IDS__ACCOUNT_LOGON.value:      table_name_ = cls.get_table_name(category='accountlogon')
                elif event_id in cls.EVENT_IDS__ACCOUNT_MANAGEMENT.value: table_name_ = cls.get_table_name(category='accountmanagement')
                elif event_id in cls.EVENT_IDS__DETAILED_TRACKING.value:  table_name_ = cls.get_table_name(category='detailedtracking')
                elif event_id in cls.EVENT_IDS__DS_ACCESS.value:          table_name_ = cls.get_table_name(category='dsaccess')
                elif event_id in cls.EVENT_IDS__LOGONLOGOFF.value:        table_name_ = cls.get_table_name(category='logonlogoff')
                elif event_id in cls.EVENT_IDS__OBJECT_ACCESS.value:      table_name_ = cls.get_table_name(category='objectaccess')
                elif event_id in cls.EVENT_IDS__POLICY_CHANGE.value:      table_name_ = cls.get_table_name(category='policychange')
                elif event_id in cls.EVENT_IDS__PRIVILEGE_USE.value:      table_name_ = cls.get_table_name(category='privilegeuse')
                elif event_id in cls.EVENT_IDS__SYSTEM.value:             table_name_ = cls.get_table_name(category='system')
                elif event_id in cls.EVENT_IDS__MISCELLANEOUS.value:      table_name_ = cls.get_table_name(category='miscellaneous')
                else: table_name_ = None

                if table_name_:
                    create_database_and_insert(
                        database_name,
                        table_name_,
                        **_PRECOMPUTED_DB_VALUES[cls],  ## db_columns, db_keys, db_marks
                        today_ymd=today_ymd,
                        row=parsed_ln,
                    )

        ## if we reach here (i.e. the end of function),
        ## it means ln is successfully parsed
        ## and inserted into database.
        ## so let's return so that
        ## the loop gets terminated as well
        return


class UDPReceiver(asyncio.DatagramProtocol):
    def connection_made(self, transport):
        self.transport = transport

    def datagram_received(self, data, addr):
        # print(f'From: {addr}')
        handle_ln(
            self,
            data.decode('utf-8'),  ## ln
        )

    def error_received(self, error_msg):
        print(f'\nerror received: {error_msg}\n')


class Command(BaseCommand):
    help = 'Live Parse'

    def handle(self, *args, **kwargs):
        command = get_command(full_path=__file__, drop_extention=True)
        log_file = get_command_log_file(command)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        ####################################

        msg_sensor        = '' if sensor_list_of_names        else 'WARNING: '
        msg_switch        = '' if switch_list_of_names        else 'WARNING: '
        msg_router        = '' if router_list_of_names        else 'WARNING: '
        msg_routerboard   = '' if routerboard_list_of_names   else 'WARNING: '
        msg_vmware        = '' if vmware_list_of_names        else 'WARNING: '
        msg_windowsserver = '' if windowsserver_list_of_names else 'WARNING: '

        for log_msg in [
            f'{msg_sensor}{len(sensor_list_of_names)} Sensor objects',
            f'{msg_switch}{len(switch_list_of_names)} Switch objects',
            f'{msg_router}{len(router_list_of_names)} Router objects',
            f'{msg_routerboard}{len(routerboard_list_of_names)} RouterBoard objects',
            f'{msg_vmware}{len(vmware_list_of_names)} VMware objects',
            f'{msg_windowsserver}{len(windowsserver_list_of_names)} WindowsServer objects',
        ]:
            save_log(self, command, settings.HOST_NAME, log_file, log_msg)

        ####################################

        async def run_udp_server():
            loop = asyncio.get_running_loop()

            ## one protocol instance will be created
            ## to serve all client requests
            transport, protocol = await loop.create_datagram_endpoint(
                lambda: UDPReceiver(),
                local_addr=(
                    settings.LIVE_PARSE_HOST,
                    settings.LIVE_PARSE_PORT,
                ),
            )

            success_msg = f'UDP server listening on {settings.LIVE_PARSE_HOST}:{settings.LIVE_PARSE_PORT} ...'
            save_log(self, command, settings.HOST_NAME, log_file, success_msg)

            try:
                ## serve forever (https://stackoverflow.com/a/74526512/)
                await asyncio.Event().wait()

                ## serve for 1 hour
                # await asyncio.sleep(3600)
            except Exception as exc:
                error_msg = f'ERROR on {settings.LIVE_PARSE_HOST}:{settings.LIVE_PARSE_PORT} :: {exc!r}'
                save_log(self, command, settings.HOST_NAME, log_file, error_msg)
            finally:
                save_log(self, command, settings.HOST_NAME, log_file, 'closing...')
                transport.close()
                save_log(self, command, settings.HOST_NAME, log_file, 'closed')

        asyncio.run(run_udp_server())
