from django.conf import settings
from django.core.management.base import BaseCommand

from collections import Counter
from datetime import datetime
from json import dumps
from operator import itemgetter
from os import path, makedirs
from signal import SIGINT, signal
from time import perf_counter, mktime

from MySQLdb import connect
from natsort import natsorted
from rahavard import (
    abort,
    colorize,
    convert_second,
    get_command,
    keyboard_interrupt_handler,
    save_log,
    sort_dict,
    to_tilda,
)

from base.utils_classes import (
    FilterLogConfig,
    FilterLogParser,
    MYSQLConfig,
)

from base.utils_database import (
    get_size_of_database,
    get_tables_and_sizes,
)

from base.utils import (
    all_values_are_0,
    command_instance_is_running,
    create_name_of_database,
    end_of_command_msg,
    get_today_ymd,
    hms_to_hourkey,
    separator,
)

from base.models import Sensor


signal(SIGINT, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = f'Hourly Parse {FilterLogConfig.TITLE.value}'

    def handle(self, *args, **kwargs):
        command = get_command(full_path=__file__, drop_extention=True)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        today_ymd = get_today_ymd()

        sensor_list_of_names = Sensor.get_list_of_names()

        if not sensor_list_of_names:
            return abort(self, 'no sensors.')

        for sensor_name in sensor_list_of_names:
            sensor_start = perf_counter()

            dest_dir          = f'{FilterLogConfig.get_logs_parsed_dir()}/{sensor_name}/{today_ymd}'
            # accomplished_file = f'{dest_dir}/{today_ymd}-accomplished.log'
            log_file          = f'{dest_dir}/{today_ymd}.log'

            database_name = create_name_of_database(FilterLogConfig.SLUG.value, today_ymd, sensor_name)
            instance      = FilterLogParser(slug=FilterLogConfig.SLUG.value, ymd=today_ymd, object_name=sensor_name)

            ## get instance.rows
            ## (throws exception if database does not exist)
            try:
                with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                    with conn.cursor() as cur:
                        cur.execute(f'{FilterLogConfig.get_select_statement()};')
                        instance.rows = cur.fetchall()
            except:
                pass

            ## instance.rows = [
            ##     (1, '2024-08-29', '09:47:29', ...),
            ##     (2, '2024-08-29', '09:47:30', ...),
            ##     ...
            ## ]

            ## instance.rows can be empty
            ## even if reading database was successful
            if not instance.rows:
                ## NOTE do NOT abort(...)
                ##      otherwise other instances will not be parsed.
                ##      use print(...) and continue instead
                print(colorize(self, 'error', f'empty rows from database {database_name}'))
                continue


            ## needed for creating log_file
            if not path.exists(dest_dir):
                print(colorize(self, 'creating', f'creating {to_tilda(dest_dir)}'))
                makedirs(dest_dir)



            ## __INDEXES_ONE_OFF__

            save_log(self, command, settings.HOST_NAME, log_file, 'preparing *_and_counts')

            for hms, count in Counter(map(itemgetter(1+1), instance.rows)).items():
                ## {'00:49:51': 12, '02:59:55': 1182, ...}
                ## ->
                ## {'00:00 - 00:59': 416787, '01:00 - 01:59': 416167, ...}
                instance.times_and_counts[hms_to_hourkey(hms)] += count

                ## -----

                ## today_ymd hms -> millisecond
                ## (2023-05-12 00:00:26 -> 1624973400000)
                try:
                    ## a. today_ymd hms -> timestamp
                    ##    (2023-05-12 00:00:26 -> 1624973400)
                    timestamped = int(mktime(datetime.strptime(f'{today_ymd} {hms}', '%Y-%m-%d %H:%M:%S').timetuple()))

                    ## b. timestamp -> millisecond
                    ##    (1624973400 -> 1624973400000)
                    timestamped *= 1000

                    instance.milliseconds_and_counts[str(timestamped)] = count
                except Exception:
                    pass

            instance.protocol_names_and_counts    = Counter(map(itemgetter(2+1),  instance.rows))
            instance.source_ips_and_counts        = Counter(map(itemgetter(3+1),  instance.rows))
            instance.destination_ips_and_counts   = Counter(map(itemgetter(4+1),  instance.rows))
            instance.source_ports_and_counts      = Counter(map(itemgetter(5+1),  instance.rows))
            instance.destination_ports_and_counts = Counter(map(itemgetter(6+1),  instance.rows))
            instance.tracking_ids_and_counts      = Counter(map(itemgetter(7+1),  instance.rows))
            instance.real_interfaces_and_counts   = Counter(map(itemgetter(8+1),  instance.rows))
            instance.reasons_and_counts           = Counter(map(itemgetter(9+1),  instance.rows))
            instance.actions_and_counts           = Counter(map(itemgetter(10+1), instance.rows))
            instance.directions_and_counts        = Counter(map(itemgetter(11+1), instance.rows))

            ################################################
            ## *toptable

            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    for dictionary, table_name, key in [
                        ## dictionary                            table_name                 column/key
                        (instance.times_and_counts,             'timetoptable',            'Time'),
                        (instance.protocol_names_and_counts,    'protocolnametoptable',    '`Protocol Name`'),
                        (instance.source_ips_and_counts,        'sourceiptoptable',        '`Source IP`'),
                        (instance.destination_ips_and_counts,   'destinationiptoptable',   '`Destination IP`'),
                        (instance.source_ports_and_counts,      'sourceporttoptable',      '`Source Port`'),
                        (instance.destination_ports_and_counts, 'destinationporttoptable', '`Destination Port`'),
                        (instance.tracking_ids_and_counts,      'trackingidtoptable',      '`Tracking ID`'),
                        (instance.real_interfaces_and_counts,   'realinterfacetoptable',   '`Real Interface`'),
                        (instance.reasons_and_counts,           'reasontoptable',          'Reason'),
                        (instance.actions_and_counts,           'actiontoptable',          'Action'),
                        (instance.directions_and_counts,        'directiontoptable',       'Direction'),

                        (instance.milliseconds_and_counts,      'millisecondtoptable',     'Millisecond'),
                    ]:
                        if key in ['Time', 'Millisecond'] and all_values_are_0(dictionary):
                            dictionary = {}

                        if not dictionary:
                            continue

                        if key in ['Time', 'Millisecond']:
                            sorted_dict = sort_dict(dictionary, based_on='key', reverse=False)
                        else:
                            sorted_dict = sort_dict(dictionary, based_on='value', reverse=True)

                        table_columns = f'''
                            ID    {MYSQLConfig.ID_DATA_TYPE.value},
                            {key} {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                            Count {MYSQLConfig.COUNT_DATA_TYPE.value}'''
                        table_keys = f'{key},Count'
                        table_marks = '%s,%s'

                        ## DROP table
                        save_log(self, command, settings.HOST_NAME, log_file, f'dropping table {table_name}')
                        cur.execute(f'DROP TABLE IF EXISTS {table_name};')

                        save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                        cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                        save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(sorted_dict):,} rows into {table_name}')
                        cur.execute('START TRANSACTION;')
                        cur.executemany(
                            f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                            tuple(sorted_dict.items())
                        )

                    conn.commit()

            ################################################
            ## __INDEXES_ONE_OFF__

            temp_list_1 = map(itemgetter(3+1, 4+1), instance.rows)
            for src_ip_, dest_ip_ in temp_list_1:
                instance.src_ips_and_dest_ips[src_ip_].update(dest_ip_)

            ## instance.src_ips_and_dest_ips = {
            ##     '***********': {'*******', '*******', '*******'},
            ##     '***********': {'*******', '*******'},
            ##     ...
            ## }

            table_name = 'sourceipsdestinationipstable'
            table_columns = f'''
                ID                {MYSQLConfig.ID_DATA_TYPE.value},
                `Source IP`       {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                `Destination IPs` {MYSQLConfig.DEFAULT_DATA_TYPE.value}'''
            table_keys = '`Source IP`,`Destination IPs`'
            table_marks = '%s,%s'
            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    ## DROP table
                    save_log(self, command, settings.HOST_NAME, log_file, f'dropping table {table_name}')
                    cur.execute(f'DROP TABLE IF EXISTS {table_name};')

                    save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                    cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                    save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(instance.src_ips_and_dest_ips):,} rows into {table_name}')
                    cur.execute('START TRANSACTION;')
                    for k, v in instance.src_ips_and_dest_ips.items():
                        cur.execute(
                            f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                            (k, dumps(natsorted(v)))
                            ## ^^ dumps(v) to turn list of destination ips into string (__USING_DUMPS_LOADS__)
                        )
                    conn.commit()

            ################################################
            instance.truncate_all()
            ################################################

            save_log(self, command, settings.HOST_NAME, log_file, f'database: {database_name}, {get_size_of_database(database_name, convert=True)}')
            save_log(self, command, settings.HOST_NAME, log_file, f'tables: {str(get_tables_and_sizes(database_name, convert=True, reverse=True))}')

            ################################################

            ## create accomplished_file
            # save_log(self, command, settings.HOST_NAME, accomplished_file, 'accomplished', echo=False)

            sensor_end = perf_counter()
            sensor_duration = int(sensor_end - sensor_start)

            save_log(self, command, settings.HOST_NAME, log_file, f'accomplished in {sensor_duration:,} seconds ({convert_second(seconds=sensor_duration, verbose=False)})')
            print(separator())

        print(end_of_command_msg(self, command))
