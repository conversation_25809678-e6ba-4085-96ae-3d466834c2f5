from django.conf import settings
from django.core.cache import cache
from django.core.management.base import BaseCommand

from os import path, remove, rename
from shutil import rmtree
from signal import SIGINT, signal
from subprocess import run
from tarfile import open as tarfile_open
from time import sleep

from MySQLdb import connect
from natsort import natsorted
from rahavard import (
    abort,
    add_yearmonthday_force,
    colorize,
    get_command,
    keyboard_interrupt_handler,
    save_log,
    to_tilda,
)

from base.utils_classes import (
    MYSQLConfig,
)

from base.utils_constants import (
    PRINT_ENDPOINT,
)

from base.utils_database import (
    get_databases,
    get_databases_and_sizes,
    get_size_of_database,
    get_size_of_table,
    get_tables_and_sizes,
)

from base.utils import (
    filter_databases,
    get_directory_path_from_name_of_database,
    get_today_ymd,
)


signal(SIGINT, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = 'MYSQL'

    def add_arguments(self, parser):
        add_yearmonthday_force(parser, for_mysql=True)

        parser.add_argument(
            '-e',
            '--exclude',
            default=[],
            nargs='+',
            type=str,
            help='exclude. Note: it overrides -o|--only args',
        )

        parser.add_argument(
            '-o',
            '--only',
            default=[],
            nargs='+',
            type=str,
            help='only',
        )

        parser.add_argument(
            '-p',
            '--plus',
            default=[],
            nargs='+',
            type=str,
            help='plus these databases (besides -o|--only/-e|--exclude/--*year-month-day*)',
        )

        parser.add_argument(
            '-a',
            '--all-databases',
            default=[],
            nargs='+',
            type=str,
            help='all databases. Note: it overrides any other filters',
        )

        parser.add_argument(
            '-k',
            '--keep-sql',
            default=False,
            action='store_true',
            help='skip removing the .sql file',
        )

    @staticmethod
    def should_abort(msg):
        confirm = input(msg).lower().strip()
        return not confirm == 'y'

    def handle(self, *args, **kwargs):
        year_months     = kwargs.get('year_months')
        year_month_days = kwargs.get('year_month_days')

        start_year_month     = kwargs.get('start_year_month')
        start_year_month_day = kwargs.get('start_year_month_day')

        end_year_month     = kwargs.get('end_year_month')
        end_year_month_day = kwargs.get('end_year_month_day')

        force = kwargs.get('force')

        if year_months:     year_months     = natsorted(set(year_months))
        if year_month_days: year_month_days = natsorted(set(year_month_days))

        if start_year_month and end_year_month:
            ## make sure start_year_month precedes end_year_month in time
            if start_year_month >= end_year_month:
                end_year_month = None

        if start_year_month_day and end_year_month_day:
            ## make sure start_year_month_day precedes end_year_month_day in time
            if start_year_month_day >= end_year_month_day:
                end_year_month_day = None

        exclude  = kwargs.get('exclude')
        only     = kwargs.get('only')
        plus     = kwargs.get('plus')
        all_dbs  = kwargs.get('all_databases')
        keep_sql = kwargs.get('keep_sql')

        ## ,----------------------------------------------,
        ## | NOTE if you add/modify/remove any arguments, |
        ## |      make sure to do the same in JUMP_4      |
        ## '----------------------------------------------'

        #############################################################

        command = get_command(full_path=__file__, drop_extention=True)

        initializer = '>'
        separator = '  '
        warning_sign = '!'
        MAIN_MENU = [
            'show databases and sizes',
            'show tables and sizes of database',
            'reset binary logs and gtids',
            'drop databases',
            'dump',
            'clear cache',
        ]

        ## menu
        for idx, opt in enumerate(MAIN_MENU, start=1):
            print(f'{idx}{separator}{opt}')
        print(f'q{separator}quit')

        try:
            answer = input(f'{initializer}{separator}').lower().strip()
            if not answer:
                raise Exception

            if answer.isdigit():
                index = int(answer) - 1
                if index < 0:
                    raise Exception
                option = MAIN_MENU[index]
            else:
                if answer == 'q':
                    return
                raise ValueError

        except IndexError:
            return abort(self, 'invalid number')

        ## input is letter other than q
        except ValueError:
            return abort(self, 'numbers only')

        except Exception:
            return abort(self, 'invalid input')

        ## -----------------------------------

        print(f'\n{option}:')

        if option in [
            'show databases and sizes',
            'show tables and sizes of database',
        ]:
            if option == 'show tables and sizes of database':
                ## __DATABASE_YMD_PATTERN__
                database_name = input('database name (e.g. malicious, daemon__Sensor_One__2024_11_30, etc.) ').lower().strip()
                if not database_name:
                    return abort(self, 'invalid database name')
            else:
                database_name = None

            print(f'1{separator}sort by size (ascending)')
            print(f'2{separator}sort by size (descending)')
            print(f'3{separator}sort alphabetically (ascending)')
            print(f'4{separator}sort alphabetically (descending)')
            print(f'5{separator}no sort')
            answer = input(f'{initializer}{separator}').lower().strip()

            if not answer or answer not in ['1', '2', '3', '4', '5']:
                return abort(self, 'invalid input')

            if answer == '1':
                sort = True
                based_on = 'value'
                reverse = False
            elif answer == '2':
                sort = True
                based_on = 'value'
                reverse = True
            elif answer == '3':
                sort = True
                based_on = 'key'
                reverse = False
            elif answer == '4':
                sort = True
                based_on = 'key'
                reverse = True
            elif answer == '5':
                sort = False
                based_on = ''
                reverse = False

            if option == 'show databases and sizes':
                items_and_sizes = get_databases_and_sizes(
                    include_builtins=False,
                    sort=sort,
                    based_on=based_on,
                    reverse=reverse,
                    convert=True,
                )
            elif option == 'show tables and sizes of database':
                items_and_sizes = get_tables_and_sizes(
                    database_name=database_name,
                    sort=sort,
                    based_on=based_on,
                    reverse=reverse,
                    convert=True,
                )

            ## JUMP_2
            if not items_and_sizes:
                return abort(self, 'no item found')

            longest_name = max(map(len, items_and_sizes.keys()))  ## 31
            longest_size = max(map(len, items_and_sizes.values()))  ## 7

            ## +1 to allow for at least one space
            ## between item and item_size
            longest_line = longest_name + longest_size + 1

            for item, item_size in items_and_sizes.items():
                if option == 'show databases and sizes':
                    item_size = get_size_of_database(item, convert=True)
                elif option == 'show tables and sizes of database':
                    item_size = get_size_of_table(database_name, item, convert=True)
                gap_len = longest_line - len(item) - len(item_size)

                print(f'{item}{" ":>{gap_len}}{item_size}')

            print(f'\ntotal no: {len(items_and_sizes):,}')

        elif option == 'reset binary logs and gtids':
            if self.should_abort(msg=colorize(self, 'warning', f'{warning_sign} {option}. continue? (y/n) ')):
                return abort(self, 'canceled')

            try:
                ## https://dev.mysql.com/doc/refman/8.4/en/reset-binary-logs-and-gtids.html
                with connect(**MYSQLConfig.MASTER_CREDS.value) as conn:
                    with conn.cursor() as cur:
                        cur.execute('RESET BINARY LOGS AND GTIDS;')
                print(colorize(self, 'done', 'done'))
            except Exception as exc:
                return abort(self, f'ERROR: {exc!r}')

        elif option == 'drop databases':
            print('a  all')
            print('p  pattern')
            answer = input(f'{initializer}{separator}').lower().strip()

            if answer not in ['a', 'p']:
                return abort(self, 'invalid input')

            databases = get_databases(include_builtins=False)

            ## JUMP_2
            if not databases:
                return abort(self, 'no databases')

            if answer == 'a':
                pass
            elif answer == 'p':
                ## __DATABASE_YMD_PATTERN__
                pattern = input('pattern (e.g. daemon, daemon__Sensor_One, 2024_11_30, etc.) ').lower().strip()
                if not pattern:
                    return abort(self, 'invalid pattern')

                databases = [
                    _ for _ in databases
                    if pattern in _
                ]

                ## JUMP_2
                if not databases:
                    return abort(self, f'no databases matching {pattern}')

            if self.should_abort(msg=colorize(self, 'warning', f'{warning_sign} drop {len(databases):,} databases. continue? (y/n) ')):
                return abort(self, 'canceled')

            dropped = 0
            removed = 0
            colored_ok = colorize(self, 'success', 'OK')

            ## also in commands/remove-old-logs-and-databases.py
            with connect(**MYSQLConfig.MASTER_CREDS.value) as conn:
                with conn.cursor() as cur:
                    for db in databases:
                        ## drop database
                        db_msg = colorize(self, 'dropping', f'dropping {db}')
                        print(db_msg, end=PRINT_ENDPOINT)
                        cur.execute(f'DROP DATABASE IF EXISTS {db};')
                        db_msg = f'{db_msg} {colored_ok}'
                        print(db_msg, end=PRINT_ENDPOINT)
                        dropped += 1

                        ## remove directory
                        dir_name = get_directory_path_from_name_of_database(db)
                        if dir_name:
                            if path.exists(dir_name):
                                dir_msg = colorize(self, 'removing', f'removing {to_tilda(dir_name)}')
                                dir_msg = f'{db_msg} + {dir_msg}'
                                print(dir_msg, end=PRINT_ENDPOINT)
                                rmtree(dir_name)
                                dir_msg = f'{dir_msg} {colored_ok}'
                                print(dir_msg, end=PRINT_ENDPOINT)
                                removed += 1
                            else:
                                dir_msg = colorize(self, 'error', f'{to_tilda(dir_name)} does not exist')
                                dir_msg = f'{db_msg} + {dir_msg}'
                                print(dir_msg, end=PRINT_ENDPOINT)
                        else:
                            dir_msg = colorize(self, 'error', f'directory is {dir_name}')
                            print(f'{db_msg} + {dir_msg}', end=PRINT_ENDPOINT)

                        print()
                        sleep(.1)

            print()

            if all([
                dropped,
                removed,
                dropped == removed,
            ]):
                mode = 'success'
            else:
                mode = 'error'
            print(colorize(self, mode, f'{dropped:,} databases dropped'))
            print(colorize(self, mode, f'{removed:,} directories removed'))

        elif option == 'dump':
            if all_dbs:
                dbs_cmd = '--all-databases'
                len_databases = 'all'
            else:
                databases = get_databases(include_builtins=False)
                ## '--> ['malicious', 'daemon__Rahavard__2024_10_07', ..., 'malicious', ...]
                databases = filter_databases(
                    databases=databases,

                    year_months=year_months,
                    year_month_days=year_month_days,

                    start_year_month=start_year_month,
                    start_year_month_day=start_year_month_day,

                    end_year_month=end_year_month,
                    end_year_month_day=end_year_month_day,
                )

                ## keep 'if exclude:' as first statement (before JUMP_3)
                ## because --exclude is of higher priority
                ## and is supposed to override --only args
                if exclude:
                    databases_ = []
                    for db in databases:
                        should_add = True
                        for exc in exclude:
                            if exc in db:
                                should_add = False
                                break
                        if should_add:
                            databases_.append(db)
                    databases = databases_

                ## JUMP_3
                elif only:
                    databases_ = []
                    for db in databases:
                        should_add = False
                        for on in only:
                            if on in db:
                                should_add = True
                                break
                        if should_add:
                            databases_.append(db)
                    databases = databases_

                if plus:
                    for p_ in plus:
                        if p_ not in databases:
                            databases.append(p_)

                ## JUMP_2
                if not databases:
                    return abort(self, 'no databases')

                len_databases = f'{len(databases):,}'

                databases__space_joined = ' '.join(databases)
                dbs_cmd = f'--databases {databases__space_joined}'

            ## ------------------

            today_ymd = get_today_ymd()

            dest_dir = settings.LOGS_PARSED_DIR

            dest_sql = f'{dest_dir}/{settings.HOST_NAME}--dumped-on-{today_ymd}.sql'
            replaced = dest_sql.replace('.sql', '.sql.replaced')
            dest_tar = dest_sql.replace('.sql', '.tar.gz')
            log_file = dest_sql.replace('.sql', '.log')

            if not path.exists(dest_dir):
                return abort(self, f'{to_tilda(dest_dir)} does not exist')

            ## JUMP_4 log the arguments
            save_log(self, command, settings.HOST_NAME, log_file, 'arguments:')
            if year_months:          save_log(self, command, settings.HOST_NAME, log_file, f'  --year-months {" ".join(year_months)}')
            if year_month_days:      save_log(self, command, settings.HOST_NAME, log_file, f'  --year-month-days {" ".join(year_month_days)}')
            if start_year_month:     save_log(self, command, settings.HOST_NAME, log_file, f'  --start-year-month {start_year_month}')
            if start_year_month_day: save_log(self, command, settings.HOST_NAME, log_file, f'  --start-year-month-day {start_year_month_day}')
            if end_year_month:       save_log(self, command, settings.HOST_NAME, log_file, f'  --end-year-month {end_year_month}')
            if end_year_month_day:   save_log(self, command, settings.HOST_NAME, log_file, f'  --end-year-month-day {end_year_month_day}')
            if force:                save_log(self, command, settings.HOST_NAME, log_file,  '  --force')
            if exclude:              save_log(self, command, settings.HOST_NAME, log_file, f'  --exclude {" ".join(exclude)}')
            if only:                 save_log(self, command, settings.HOST_NAME, log_file, f'  --only {" ".join(only)}')
            if plus:                 save_log(self, command, settings.HOST_NAME, log_file, f'  --plus {" ".join(plus)}')
            if all_dbs:              save_log(self, command, settings.HOST_NAME, log_file,  '  --all-databases')
            if keep_sql:             save_log(self, command, settings.HOST_NAME, log_file,  '  --keep-sql')


            ## check if dest_sql exists
            if path.exists(dest_sql):
                if force:
                    save_log(self, command, settings.HOST_NAME, log_file, f'removing {to_tilda(dest_sql)}')
                    remove(dest_sql)
                else:
                    return abort(self, f'{to_tilda(dest_sql)} already exists.')
            ##
            ## check if dest_tar exists
            if path.exists(dest_tar):
                if force:
                    save_log(self, command, settings.HOST_NAME, log_file, f'removing {to_tilda(dest_tar)}')
                    remove(dest_tar)
                else:
                    return abort(self, f'{to_tilda(dest_tar)} already exists.')


            ## dump (https://stackoverflow.com/a/26408905/)
            save_log(self, command, settings.HOST_NAME, log_file, f'dumping {len_databases} databases to {to_tilda(dest_sql)}')

            ## NOTE do NOT ' -> "
            run(f'''
                mysqldump -u '{MYSQLConfig.MASTER_CREDS.value["user"]}' -p'{MYSQLConfig.MASTER_CREDS.value["password"]}' \\
                --add-drop-database --single-transaction --skip-lock-tables \\
                {dbs_cmd} --result-file='{dest_sql}' \\
                2>/dev/null || exit 1
            ''', shell=True)


            ## replace
            utf_production  = 'utf8mb4_0900_ai_ci'
            utf_development = 'utf8mb4_unicode_ci'
            save_log(self, command, settings.HOST_NAME, log_file, f'replacing {utf_production} with {utf_development}')
            run(f'''
                sed 's/{utf_production}/{utf_development}/g' '{dest_sql}' > '{replaced}' || exit 1
            ''', shell=True)
            ##
            rename(replaced, dest_sql)


            ## compress
            save_log(self, command, settings.HOST_NAME, log_file, f'compressing to {to_tilda(dest_tar)}')
            try:
                ## https://docs.python.org/3/library/tarfile.html
                with tarfile_open(dest_tar, 'w:gz') as tar_opened:
                    tar_opened.add(
                        dest_sql,

                        ## added this so the name of the extracted file
                        ## will be a basename instead of an absolute path in current directory
                        ## (i.e. sth-2000-01-01.sql instead of /home/<USER>/sth-2000-01-01.sql)
                        arcname=path.basename(dest_sql),
                    )
            except Exception as exc:
                return abort(self, f'ERROR compressing: {exc!r}')


            ## remove dest_sql
            if not keep_sql:
                save_log(self, command, settings.HOST_NAME, log_file, f'removing {to_tilda(dest_sql)}')
                remove(dest_sql)


            ## done
            save_log(self, command, settings.HOST_NAME, log_file, 'done')

        elif option == 'clear cache':
            if self.should_abort(msg=colorize(self, 'warning', 'continue? (y/n) ')):
                return abort(self, 'canceled')

            cache.clear()
