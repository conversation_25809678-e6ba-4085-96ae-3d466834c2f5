from django.conf import settings
from django.core.management.base import BaseCommand

from collections import Counter
from json import loads
from operator import itemgetter
from os import path, makedirs
from signal import SIGINT, signal
from time import perf_counter

import asyncio

from MySQLdb import connect
from rahavard import (
    abort,
    colorize,
    convert_second,
    get_command,
    keyboard_interrupt_handler,
    save_log,
    sort_dict,
    to_tilda,
)

import httpx

from base.utils_classes import (
    GeoLocationParser,
    GeoLocationConfig,
    MYSQLConfig,
)

from base.utils_constants import (
    HTTP_HEADERS,
    MAX_TRIES,
    PRINT_ENDPOINT,
    TIMEOUTS,
)

from base.utils_database import (
    get_all_domains,
)

from base.utils_tor import (
    check_is_tor,
    renew_tor_identity,
)

from base.utils import (
    all_values_are_0,
    command_instance_is_running,
    create_name_of_database,
    end_of_command_msg,
    list_of_tuples_to_list,
    service_is_running,
    strip_protocol_and_path_from_url,
)


signal(<PERSON><PERSON>IN<PERSON>, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = f'Fetch {GeoLocationConfig.TITLE.value} - Domain'

    def add_arguments(self, parser):
        parser.add_argument(
            '-p',
            '--proxy',
            default=False,
            action='store_true',
            help='Use proxy',
        )

    def handle(self, *args, **kwargs):
        proxy = kwargs.get('proxy')

        ################################################

        command = get_command(full_path=__file__, drop_extention=True)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        httpx_timeout = httpx.Timeout(TIMEOUTS.fetcher)

        database_name = create_name_of_database(GeoLocationConfig.SLUG.value)

        src_dir = GeoLocationConfig.get_logs_parsed_dir()
        log_file = f'{src_dir}/log--domain'

        instance = GeoLocationParser(slug=GeoLocationConfig.SLUG.value)

        if not path.exists(src_dir):
            print(colorize(self, 'creating', f'creating {to_tilda(src_dir)}'))
            makedirs(src_dir)

        start = perf_counter()

        ################################################

        is_tor = False
        tor_ip = ''

        if proxy:
            save_log(self, command, settings.HOST_NAME, log_file, 'checking tor availability')

            ## check 1
            tor_is_running = service_is_running('tor')
            if not tor_is_running:
                log_msg = 'proxy requested but tor service is not running'
                save_log(self, command, settings.HOST_NAME, log_file, log_msg, echo=False)
                return abort(self, log_msg)

            ## check 2
            is_tor, tor_ip = check_is_tor(self)  ## (True, '*******')
            if not is_tor:
                log_msg = 'proxy requested but tor could not be connected (i.e. check_is_tor() returned False)'
                save_log(self, command, settings.HOST_NAME, log_file, log_msg, echo=False)
                return abort(self, log_msg)

            save_log(self, command, settings.HOST_NAME, log_file, f'proxy set to tor. ip: {tor_ip}')
            proxy = settings.TOR_PROXY
        else:
            proxy = None

        ################################################

        save_log(self, command, settings.HOST_NAME, log_file, 'getting all domains')

        domains = get_all_domains(unique=True)

        ################################################

        save_log(self, command, settings.HOST_NAME, log_file, 'stripping domains')

        domains = list(set(
            strip_protocol_and_path_from_url(_)
            for _ in domains

            ## __DOT_LOCAL__
            if not _.endswith('.local')
        ))

        ## remove empty elements
        domains = [_ for _ in domains if _]

        ################################################

        save_log(self, command, settings.HOST_NAME, log_file, 'getting domains that already have geolocation')
        try:
            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    cur.execute(f'''
                        SELECT Domain
                        FROM {GeoLocationConfig.get_table_name(geo_mode="domain")}
                    ;''')
                    ## using set to have faster difference in JUMP_4
                    domains__already = set(list_of_tuples_to_list(cur.fetchall()))
        except Exception as exc:
            save_log(self, command, settings.HOST_NAME, log_file, f'ERROR: {exc!r}')
            domains__already = set()

        ################################################

        ## JUMP_4 remove items of domains__already from domains
        ## (https://stackoverflow.com/a/54297667/)
        if domains__already:
            save_log(self, command, settings.HOST_NAME, log_file, 'removing domains that already have geolocation from list')

            ## making sure it's a list
            ## because evenly_sized_chunks throws an error
            ## if it is a set
            domains = list(
                set(domains).difference(domains__already)  ## gives a set
            )


        if not domains:
            return abort(self, 'no domains. exiting')

        ################################################

        ## create database
        with connect(**MYSQLConfig.MASTER_CREDS.value) as conn:
            with conn.cursor() as cur:
                save_log(self, command, settings.HOST_NAME, log_file, f'creating database {database_name}')
                cur.execute(f'CREATE DATABASE IF NOT EXISTS {database_name};')

        ################################################

        save_log(self, command, settings.HOST_NAME, log_file, 'fetching ips')

        global should_renew_tor, should_abort, renew_counts
        should_renew_tor = False
        should_abort      = False
        renew_counts     = 1


        def insert_into_db(rows):
            if not rows:
                return

            save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(rows):,} rows into {GeoLocationConfig.get_table_name(geo_mode="domain")}')

            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    cur.execute(f'''
                        CREATE TABLE IF NOT EXISTS {GeoLocationConfig.get_table_name(geo_mode="domain")}
                        ({GeoLocationConfig.DB_COLUMNS__DOMAIN.value})
                    ;''')

                    cur.execute('START TRANSACTION;')
                    cur.executemany(
                        f'INSERT INTO {GeoLocationConfig.get_table_name(geo_mode="domain")} ({GeoLocationConfig.DB_KEYS__DOMAIN.value}) VALUES ({GeoLocationConfig.DB_MARKS__DOMAIN.value});',
                        rows,
                    )
                    conn.commit()

        def evenly_sized_chunks(domains, len_of_each_chunk=20):
            ''' NOTE a similar structure in evenly_sized_batches() in base/utils.py
            https://www.programiz.com/python-programming/examples/list-chunks

            takes list of domains   e.g. ['example1.com', 'example2.com', ..., 'example42.com']
            takes len_of_each_chunk e.g. 10

            returns generator: <generator object ... at ...>
            which looks like this if turned into list:
            [
                ['example1.com',  ..., 'example10.com'],
                ['example11.com', ..., 'example20.com'],
                ['example21.com', ..., 'example30.com'],
                ['example31.com', ..., 'example40.com'],
                ['example41.com', 'example42.com'],
            ]

            '''
            for i in range(0, len(domains), len_of_each_chunk):
                yield domains[i:i + len_of_each_chunk]

        async def fetch_one(client, domain):
            global should_renew_tor, renew_counts, should_abort

            if should_renew_tor:
                if proxy:
                    save_log(self, command, settings.HOST_NAME, log_file, f'renewing tor identity (renew counts: {renew_counts})')
                    renew_tor_identity()
                    await asyncio.sleep(2)

                    save_log(self, command, settings.HOST_NAME, log_file, '  renewed. getting new tor ip...')
                    is_tor, tor_ip = check_is_tor(self)  ## (True, '*******')

                    save_log(self, command, settings.HOST_NAME, log_file, f'  new tor ip: {tor_ip}')

                    renew_counts += 1
                    should_renew_tor = False
                else:
                    should_abort = True
                    return None

            pre_msg = domain
            print(pre_msg, end=PRINT_ENDPOINT)

            dl_try = 0
            dl_successful = False

            while not dl_successful and dl_try < MAX_TRIES.dl:
                dl_try += 1
                is_valid_ip = False
                row = tuple()

                response_text = ''
                success = False
                message = None

                try:
                    ## JUMP_1
                    response = await client.get(
                        ## no trailing /
                        url=f'{settings.GEOLOCATION_URL__DOMAIN}/json/{domain}',

                        headers=HTTP_HEADERS,
                        timeout=httpx_timeout,
                    )

                    response.raise_for_status()

                    response_text = response.text  ## JUMP_2
                    response_dict = loads(response_text)

                    success = response_dict.get('status') == 'success'  ## True/False
                    message = response_dict.get('message')

                    ## JUMP_3
                    row = (
                        domain,
                        response_dict.get('country', ''),  ## The Netherlands
                        response_dict.get('countryCode', ''),
                        response_dict.get('regionName', ''),
                        response_dict.get('city', ''),
                        response_dict.get('lat', ''),
                        response_dict.get('lon', ''),
                        response_dict.get('timezone', ''),
                        response_dict.get('isp', ''),
                        response_dict.get('org', ''),
                        response_dict.get('query', ''),  ## *******
                    )

                    country_ = row[1]
                    ip_      = row[10]

                    ## NOTE __IS_VALID_IP__
                    ## when success is True or message is
                    ## 'invalid query' or 'private range' or 'reserved range',
                    ## we do 2 things:
                    ##   1. set dl_successful to True
                    ##      to stop tries
                    ##   2. set is_valid_ip to True
                    ##      to accept ip_'s value
                    ##      as a valid value whatever it is
                    ##      (i.e. '' or '*******')

                    if success:
                        if ip_:
                            print(f'{pre_msg}: {colorize(self, "success", ip_)} {country_}')
                        else:
                            print(f'{pre_msg}: {colorize(self, "warning", "no ip")}')
                        dl_successful = True
                        is_valid_ip = True  ## __IS_VALID_IP__

                    ## response_dict is:
                    ## {'status': 'fail', 'message': 'invalid query', 'query': 'yahoo.xx'}
                    ## {'status': 'fail', 'message': 'private range', 'query': '*******'}
                    ## {'status': 'fail', 'message': 'reserved range', 'query': '127.0.0.1'}
                    elif message in ['invalid query', 'private range', 'reserved range']:
                        print(f'{pre_msg}: {colorize(self, "error", f"message: {message}")}')
                        dl_successful = True
                        is_valid_ip = True  ## __IS_VALID_IP__

                        ## JUMP_3
                        row = (
                            domain,
                            message,
                            message,
                            message,
                            message,
                            message,
                            message,
                            message,
                            message,
                            message,
                            message,
                        )
                    else:
                        print(f'{pre_msg}: {colorize(self, "error", f"failed. message: {message}")}')
                        await asyncio.sleep(1)

                except httpx.HTTPStatusError as exc:
                    response_status_code = exc.response.status_code
                    print(f'{pre_msg}: {colorize(self, "error", f"status code: {response_status_code}")}')

                    if response_status_code == 404:
                        dl_successful = True

                    elif response_status_code == 429:
                        ## response code 429 indicates the client
                        ## has sent too many requests in a given amount of time
                        await asyncio.sleep(3)

                except Exception as exc:
                    str_exc = str(exc)

                    if 'Invalid non-printable ASCII character in URL' in str_exc:
                        print(f'{pre_msg}: {colorize(self, "error", "Invalid non-printable ASCII character in URL")}')
                        dl_successful = True

                    elif 'Proxy Server could not connect: TTL expired' in str_exc:
                        print(f'{pre_msg}: {colorize(self, "error", "Proxy Server could not connect: TTL expired")}')

                        ## __SHOULD_RENEW_TOR__
                        should_renew_tor = True

                    elif 'Proxy Server could not connect: General SOCKS server failure' in str_exc:
                        print(f'{pre_msg}: {colorize(self, "error", "Proxy Server could not connect: General SOCKS server failure")}')

                        ## __SHOULD_RENEW_TOR__
                        should_renew_tor = True

                    ## elif ...

                    else:
                        print(f'{pre_msg}: {colorize(self, "error", f"{exc!r}")}')

                        await asyncio.sleep(1)


            ## __IS_VALID_IP__
            if is_valid_ip and row:
                return row

            return None

        async def fetch_all(domains):
            async with httpx.AsyncClient(proxy=proxy) as client:
                tasks = [
                    asyncio.create_task(fetch_one(client, domain))
                    for domain in domains
                ]
                return await asyncio.gather(*tasks)

        for chunk_index, chunk in enumerate(evenly_sized_chunks(domains), start=1):
            instance.fetched_all = asyncio.run(fetch_all(domains=chunk))

            ## remove None items
            instance.fetched_all = [
                _ for _ in instance.fetched_all
                if _ is not None
            ]

            if instance.fetched_all:
                insert_into_db(instance.fetched_all)

            if should_abort:
                log_msg = 'should abort'
                save_log(self, command, settings.HOST_NAME, log_file, log_msg, echo=False)
                return abort(self, log_msg)


        ################################################

        ## now that we have updated the database
        ## with the newly-fetched data,
        ## let's get the instance.rows before moving on to *_and_counts

        save_log(self, command, settings.HOST_NAME, log_file, f'getting rows from database {database_name}')
        try:
            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    ## using GeoLocationConfig.DB_KEYS__DOMAIN.value
                    ## instead of GeoLocationConfig.get_select_statement()
                    ## because we do not need the ID column
                    cur.execute(f'SELECT {GeoLocationConfig.DB_KEYS__DOMAIN.value} FROM {GeoLocationConfig.get_table_name(geo_mode="domain")};')
                    instance.rows = cur.fetchall()
        except Exception as exc:
            save_log(self, command, settings.HOST_NAME, log_file, f'ERROR: {exc!r}')
            instance.rows = []

        ################################################
        ## *_and_counts

        ## __INDEXES_ONE_OFF__
        ## the indexes in parse-<APP_SLUG>.py (this script) are lower by 1
        ## compared to its hourly counterpart (i.e. hourly-parse-<APP_SLUG>.py).
        ## in hourly-parse-<APP_SLUG>.py,
        ## instance.rows are directlry read from *table in database
        ## meaning ID column is also included
        ## so we have to increment indexes by 1
        ## to get the right columns

        save_log(self, command, settings.HOST_NAME, log_file, 'preparing *_and_counts')

        instance.countries_and_counts     = Counter(map(itemgetter(1),  instance.rows))
        instance.cities_and_counts        = Counter(map(itemgetter(4),  instance.rows))
        instance.timezones_and_counts     = Counter(map(itemgetter(7),  instance.rows))
        instance.isps_and_counts          = Counter(map(itemgetter(8),  instance.rows))
        instance.organizations_and_counts = Counter(map(itemgetter(9),  instance.rows))
        instance.ips_and_counts           = Counter(map(itemgetter(10), instance.rows))

        ################################################
        ## *toptable

        with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:
                for dictionary, table_name, key in [
                    ## dictionary                        table_name                      column/key
                    (instance.countries_and_counts,     'countrytoptable__domain',      'Country'),
                    (instance.cities_and_counts,        'citytoptable__domain',         'City'),
                    (instance.timezones_and_counts,     'timezonetoptable__domain',     'Timezone'),
                    (instance.isps_and_counts,          'isptoptable__domain',          'ISP'),
                    (instance.organizations_and_counts, 'organizationtoptable__domain', 'Organization'),
                    (instance.ips_and_counts,           'iptoptable__domain',           'IP'),
                ]:
                    if key in ['Time', 'Millisecond'] and all_values_are_0(dictionary):
                        dictionary = {}

                    if not dictionary:
                        continue

                    if key in ['Time', 'Millisecond']:
                        sorted_dict = sort_dict(dictionary, based_on='key', reverse=False)
                    else:
                        sorted_dict = sort_dict(dictionary, based_on='value', reverse=True)

                    table_columns = f'''
                        ID    {MYSQLConfig.ID_DATA_TYPE.value},
                        {key} {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                        Count {MYSQLConfig.COUNT_DATA_TYPE.value}'''
                    table_keys = f'{key},Count'
                    table_marks = '%s,%s'

                    ## DROP table
                    save_log(self, command, settings.HOST_NAME, log_file, f'dropping table {table_name}')
                    cur.execute(f'DROP TABLE IF EXISTS {table_name};')

                    save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                    cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                    save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(sorted_dict):,} rows into {table_name}')
                    cur.execute('START TRANSACTION;')
                    cur.executemany(
                        f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                        tuple(sorted_dict.items())
                    )

                conn.commit()

        ################################################
        instance.truncate_all()
        ################################################

        end = perf_counter()
        duration = int(end - start)

        save_log(self, command, settings.HOST_NAME, log_file, f'fetched in {duration:,} seconds ({convert_second(seconds=duration, verbose=False)})')

        print(end_of_command_msg(self, command))


''' JUMP_1
?
'''

''' JUMP_2
{
    'status': 'success',
    'country': 'The Netherlands',
    'countryCode': 'NL',
    'region': 'NH',
    'regionName': 'North Holland',
    'city': 'Haarlem',
    'zip': '2011',
    'lat': 11,
    'lon': 12,
    'timezone': 'Europe/Amsterdam',
    'isp': 'Akamai Technologies, Inc.',
    'org': 'Akamai Technologies, Inc.',
    'as': 'AS16625 Akamai Technologies, Inc.',
    'query': '*******',
}
'''
