from django.core.management.base import BaseCommand

from signal import SIGINT, signal

from rahavard import (
    abort,
    get_command,
    keyboard_interrupt_handler,
)

from base.utils import command_instance_is_running

from base.models import FireHOL


signal(SIGINT, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = 'Create FireHOLs'

    def handle(self, *args, **kwargs):
        command = get_command(full_path=__file__, drop_extention=True)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        rows = [
            ## Name                                                              URL                                                            Description
            ['danger.rulez.sk',                                                 'https://danger.rulez.sk/projects/bruteforceblocker/blist.php', None],
            ['lists.blocklist.de',                                              'https://lists.blocklist.de/lists/all.txt', None],
            ['pgl.yoyo.org (I)',                                                'https://pgl.yoyo.org/adservers/serverlist.php?hostformat=hosts&showintro=0&startdate%5Bday%5D=&startdate%5Bmonth%5D=&startdate%5Byear%5D=&mimetype=plaintext', None],
            ['pgl.yoyo.org (II)',                                               'https://pgl.yoyo.org/adservers/serverlist.php?mimetype=plaintext&hostformat=plain', None],
            ['github: firehol @ blocklist-ipsets @ firehol_level2.netset',      'https://raw.githubusercontent.com/firehol/blocklist-ipsets/master/firehol_level2.netset', None],
            ['github: firehol @ blocklist-ipsets @ firehol_level3.netset',      'https://raw.githubusercontent.com/firehol/blocklist-ipsets/master/firehol_level3.netset', None],
            ['github: firehol @ blocklist-ipsets @ firehol_level4.netset',      'https://raw.githubusercontent.com/firehol/blocklist-ipsets/master/firehol_level4.netset', None],
            ['github: Loyalsoldier @ v2ray-rules-dat @ reject-list.txt',        'https://raw.githubusercontent.com/Loyalsoldier/v2ray-rules-dat/release/reject-list.txt', None],
            ['github: ookangzheng @ blahdns @ blacklist.txt',                   'https://raw.githubusercontent.com/ookangzheng/blahdns/master/hosts/blacklist.txt', None],
            ['github: romainmarcoux @ malicious-domains @ full-domains-aa.txt', 'https://raw.githubusercontent.com/romainmarcoux/malicious-domains/refs/heads/main/full-domains-aa.txt', None],
            ['github: romainmarcoux @ malicious-domains @ full-domains-ab.txt', 'https://raw.githubusercontent.com/romainmarcoux/malicious-domains/refs/heads/main/full-domains-ab.txt', None],
            ['github: romainmarcoux @ malicious-ip @ full-300k-aa.txt',         'https://raw.githubusercontent.com/romainmarcoux/malicious-ip/refs/heads/main/full-300k-aa.txt', None],
            ['github: romainmarcoux @ malicious-ip @ full-300k-ab.txt',         'https://raw.githubusercontent.com/romainmarcoux/malicious-ip/refs/heads/main/full-300k-ab.txt', None],
            ['github: romainmarcoux @ malicious-ip @ full-300k-ac.txt',         'https://raw.githubusercontent.com/romainmarcoux/malicious-ip/refs/heads/main/full-300k-ac.txt', None],
            ['github: soteria-nou @ domain-list @ ads.txt',                     'https://raw.githubusercontent.com/soteria-nou/domain-list/master/ads.txt', None],
            ['github: soteria-nou @ domain-list @ affiliate.txt',               'https://raw.githubusercontent.com/soteria-nou/domain-list/master/affiliate.txt', None],
            ['github: soteria-nou @ domain-list @ fake.txt',                    'https://raw.githubusercontent.com/soteria-nou/domain-list/master/fake.txt', None],
            ['github: stamparm @ blackbook @ blackbook.txt',                    'https://raw.githubusercontent.com/stamparm/blackbook/master/blackbook.txt', None],
            ['github: stamparm @ ipsum @ 5.txt',                                'https://raw.githubusercontent.com/stamparm/ipsum/refs/heads/master/levels/5.txt', None],
            ['github: stamparm @ maltrail @ magentocore.txt',                   'https://raw.githubusercontent.com/stamparm/maltrail/master/trails/static/malicious/magentocore.txt', None],
            ['github: StevenBlack @ hosts @ hosts (I)',                         'https://raw.githubusercontent.com/StevenBlack/hosts/master/alternates/fakenews-gambling-porn/hosts', None],
            ['github: StevenBlack @ hosts @ hosts (II)',                        'https://raw.githubusercontent.com/StevenBlack/hosts/master/hosts', None],
            ['github: XionKzn @ PiHole-Lists @ Quad9.txt',                      'https://raw.githubusercontent.com/XionKzn/PiHole-Lists/master/PiHole/Archive/Quad9.txt', None],
            ['someonewhocares.org',                                             'https://someonewhocares.org/hosts/hosts', None],
            ['sslbl.abuse.ch',                                                  'https://sslbl.abuse.ch/blacklist/sslipblacklist.txt', None],
            ['v.firebog.net (I)',                                               'https://v.firebog.net/hosts/AdguardDNS.txt', None],
            ['v.firebog.net (II)',                                              'https://v.firebog.net/hosts/Easyprivacy.txt', None],
            ['v.firebog.net (III)',                                             'https://v.firebog.net/hosts/Prigent-Ads.txt', None],
            ['v.firebog.net (IV)',                                              'https://v.firebog.net/hosts/Prigent-Malware.txt', None],
            ['v.firebog.net (V)',                                               'https://v.firebog.net/hosts/static/w3kbl.txt', None],
            ['www.spamhaus.org',                                                'https://www.spamhaus.org/drop/drop.txt', None],

            ## not confirmed how domains in this block get updated
            ['www.dan.me.uk',          'https://www.dan.me.uk/torlist/', None],
            ['www.stopforumspam.com',  'https://www.stopforumspam.com/downloads/toxic_domains_whole.txt', None],
            ['cinsscore.com',          'https://cinsscore.com/list/ci-badguys.txt', None],
            ['cinsarmy.com',           'https://cinsarmy.com/list/ci-badguys.txt', None],
            ['s3.amazonaws.com (I)',   'https://s3.amazonaws.com/lists.disconnect.me/simple_ad.txt', None],
            ['s3.amazonaws.com (II)',  'https://s3.amazonaws.com/lists.disconnect.me/simple_malvertising.txt', None],
            ['s3.amazonaws.com (III)', 'https://s3.amazonaws.com/lists.disconnect.me/simple_tracking.txt', None],
            ['hostfiles.frogeye.fr',   'https://hostfiles.frogeye.fr/firstparty-trackers.txt', None],
            ['paulgb.github.io',       'https://paulgb.github.io/BarbBlock/blacklists/domain-list.txt', None],
        ]

        firehol_objects = FireHOL.active_objects.all()
        if firehol_objects:
            return abort(self, 'firehols already exist.')

        for idx, row in enumerate(rows, start=1):
            name, \
            url, \
            description = row

            print(f'{idx}/{len(rows):,} {url}')

            obj = FireHOL.objects.create(
                name=name,
                url=url,
            )

            if description:
                obj.description = description
                obj.save()
