from django.conf import settings
from django.core.management.base import BaseCommand

from operator import itemgetter
from os import path, makedirs
from re import sub, match
from signal import SIGINT, signal
from statistics import mean
from time import perf_counter

from MySQLdb import connect
from rahavard import (
    abort,
    colorize,
    convert_second,
    get_command,
    keyboard_interrupt_handler,
    save_log,
    sort_dict,
    to_tilda,
)

from base.utils_classes import (
    MYSQLConfig,
    UserWarningParser,
    UserWarningConfig,
)

from base.utils_database import (
    get_size_of_database,
    get_tables_and_sizes,
)

from base.utils import (
    command_instance_is_running,
    create_name_of_database,
    end_of_command_msg,
    get_today_ymd,
    separator,
)

from base.models import (
    Gateway,
    Sensor,
)


signal(SIGINT, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = f'Hourly Parse {UserWarningConfig.TITLE.value}'

    def handle(self, *args, **kwargs):
        command = get_command(full_path=__file__, drop_extention=True)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        today_ymd = get_today_ymd()

        sensor_list_of_names = Sensor.get_list_of_names()

        if not sensor_list_of_names:
            return abort(self, 'no sensors.')

        gateway_list_of_names = Gateway.get_list_of_names()
        if gateway_list_of_names:
            gateways_piped = '|'.join(gateway_list_of_names)  ## GW1|GW2|GW3|GW4|GW5
            gateways_dict  = sort_dict({_: {'percents': []} for _ in gateway_list_of_names}, based_on='key', reverse=False)
            ## '--> {'GW1': {'percents': []}, 'GW2': {'percents': []}, 'GW3': {'percents': []}, ...}
        else:
            gateways_piped = ''
            gateways_dict  = {}

        for sensor_name in sensor_list_of_names:
            sensor_start = perf_counter()

            dest_dir          = f'{UserWarningConfig.get_logs_parsed_dir()}/{sensor_name}/{today_ymd}'
            # accomplished_file = f'{dest_dir}/{today_ymd}-accomplished.log'
            log_file          = f'{dest_dir}/{today_ymd}.log'

            database_name = create_name_of_database(UserWarningConfig.SLUG.value, today_ymd, sensor_name)
            instance      = UserWarningParser(
                slug=UserWarningConfig.SLUG.value,
                ymd=today_ymd,
                object_name=sensor_name,
                gateways_dict=gateways_dict,
            )

            ## get instance.rows
            ## (throws exception if database does not exist)
            try:
                with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                    with conn.cursor() as cur:
                        cur.execute(f'{UserWarningConfig.get_select_statement()};')
                        instance.rows = cur.fetchall()
            except:
                pass

            ## instance.rows = [
            ##     (1, '2024-08-29', '09:47:29', ...),
            ##     (2, '2024-08-29', '09:47:30', ...),
            ##     ...
            ## ]

            ## instance.rows can be empty
            ## even if reading database was successful
            if not instance.rows:
                ## NOTE do NOT abort(...)
                ##      otherwise other instances will not be parsed.
                ##      use print(...) and continue instead
                print(colorize(self, 'error', f'empty rows from database {database_name}'))
                continue


            ## needed for creating log_file
            if not path.exists(dest_dir):
                print(colorize(self, 'creating', f'creating {to_tilda(dest_dir)}'))
                makedirs(dest_dir)



            ## times = ...

            ################################################
            ## __INDEXES_ONE_OFF__

            messages = map(itemgetter(3+1), instance.rows)
            for message in messages:
                if instance.gateways_dict and gateways_piped:
                    if all([
                        match(f'^{gateways_piped}', message),
                        ' loss ' in message,
                    ]):
                        message_splited = message.split()
                        gw              = message_splited[0]  ## GW1
                        percent         = message_splited[-1]  ## 22%
                        percent         = int(sub('%$', '', percent))  ## 22
                        instance.gateways_dict[gw]['percents'].append(percent)

            ## instance.gateways_dict = {
            ##     'GW1': {'percents': []},
            ##     'GW2': {'percents': [22, 7, 21, 5, ...]},
            ##     'GW3': {'percents': [100, 100, 100, ...]},
            ##     ...
            ## }

            for gw, info in instance.gateways_dict.items():
                percents = info.get('percents', [])

                if percents:
                    len_ = len(percents)
                    min_ = f'{min(percents)}%'
                    max_ = f'{max(percents)}%'
                    avg_ = f'{int(mean(percents))}%'
                else:
                    len_ = 0
                    min_ = ''
                    max_ = ''
                    avg_ = ''

                instance.gateway_rows.append((
                    gw,
                    len_,
                    min_,
                    max_,
                    avg_,
                ))
            ## instance.gateway_rows = [
            ##     ('GW1', 0, '', '', ''),
            ##     ('GW2', 28, '5%', '22%', '16%'),
            ##     ('GW3', 27, '100%', '100%', '100%'),
            ##     ...
            ## ]



            table_name = 'packetlosstable'
            table_columns = f'''
                ID      {MYSQLConfig.ID_DATA_TYPE.value},
                Gateway {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                Count   {MYSQLConfig.COUNT_DATA_TYPE.value},
                Minimum {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                Maximum {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                Average {MYSQLConfig.DEFAULT_DATA_TYPE.value}'''
            table_keys = 'Gateway,Count,Minimum,Maximum,Average'
            table_marks = '%s,%s,%s,%s,%s'
            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    ## DROP table
                    save_log(self, command, settings.HOST_NAME, log_file, f'dropping table {table_name}')
                    cur.execute(f'DROP TABLE IF EXISTS {table_name};')

                    save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                    cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                    save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(instance.gateway_rows):,} rows into {table_name}')
                    cur.execute('START TRANSACTION;')
                    cur.executemany(
                        f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                        instance.gateway_rows
                    )
                    conn.commit()

            ################################################
            instance.truncate_all()
            ################################################

            save_log(self, command, settings.HOST_NAME, log_file, f'database: {database_name}, {get_size_of_database(database_name, convert=True)}')
            save_log(self, command, settings.HOST_NAME, log_file, f'tables: {str(get_tables_and_sizes(database_name, convert=True, reverse=True))}')

            ################################################

            ## create accomplished_file
            # save_log(self, command, settings.HOST_NAME, accomplished_file, 'accomplished', echo=False)

            sensor_end = perf_counter()
            sensor_duration = int(sensor_end - sensor_start)

            save_log(self, command, settings.HOST_NAME, log_file, f'accomplished in {sensor_duration:,} seconds ({convert_second(seconds=sensor_duration, verbose=False)})')
            print(separator())

        print(end_of_command_msg(self, command))
