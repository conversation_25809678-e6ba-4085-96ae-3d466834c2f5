'''
NOTE this parser uses quick parsing method
'''

from django.conf import settings
from django.core.management.base import BaseCommand

from collections import Counter
from datetime import datetime
from functools import partial
from multiprocessing import Pool, Process
from operator import itemgetter
from os import path, makedirs, remove
from shutil import rmtree
from signal import SIGINT, signal
from time import perf_counter, mktime
from typing import List, Optional, Tuple

from MySQLdb import connect
from natsort import natsorted
from rahavard import (
    abort,
    add_yearmonthday_force,
    colorize,
    convert_second,
    get_command,
    get_list_of_files,
    keyboard_interrupt_handler,
    save_log,
    sort_dict,
    to_tilda,
)

from base.utils_classes import (
    MYSQLConfig,
    WindowsServerParser,
    WindowsServerConfig,
)

from base.utils_constants import (
    ACTION_ON_ERROR,
)

from base.utils_database import (
    get_size_of_database,
    get_tables_and_sizes,
)

from base.utils_parsers import (
    parse_ln,
)

from base.utils import (
    all_values_are_0,
    create_name_of_database,
    create_path_of_infile,
    end_of_command_msg,
    evenly_sized_batches,
    filter_list,
    get_date_of_source_log,
    get_no_of_infiles,
    get_today_ymd,
    hms_to_hourkey,
    is_invalid_log_date,
    separator,
    source_log_info_line,
)

from base.models import WindowsServer


signal(SIGINT, keyboard_interrupt_handler)


windowsserver_list_of_names               = WindowsServer.get_list_of_names()
windowsserver_list_of_names_and_addresses = WindowsServer.get_list_of_names_and_addresses()
windowsserver_dict_of_addresses_and_names = WindowsServer.get_dict_of_addresses_and_names()


def parse_line(ln: str, already_accomplished: List[str]) -> Tuple[Optional[str], Optional[str]]:
    '''
    Parses a given line and extracts relevant information.

    Args:
        ln (str): The input line to be parsed.
        already_accomplished (List[str]): A list of object names that have already been processed.

    Returns:
        Tuple[Optional[str], Optional[str]]: A tuple containing the object name and the parsed line.
        If the line is invalid or the object has already been processed, returns (None, None).
    '''

    _windowsserver_name, _parsed_ln = parse_ln(
        ln.strip(),
        WindowsServerConfig,
        windowsserver_list_of_names_and_addresses,
        windowsserver_dict_of_addresses_and_names,
    )

    if _windowsserver_name in already_accomplished:
        return (None, None)

    return (_windowsserver_name, _parsed_ln)


class Command(BaseCommand):
    help = f'Parse {WindowsServerConfig.TITLE.value}'

    def add_arguments(self, parser):
        add_yearmonthday_force(parser, for_mysql=False)

    def handle(self, *args, **kwargs):
        year_months     = kwargs.get('year_months')
        year_month_days = kwargs.get('year_month_days')

        start_year_month     = kwargs.get('start_year_month')
        start_year_month_day = kwargs.get('start_year_month_day')

        end_year_month     = kwargs.get('end_year_month')
        end_year_month_day = kwargs.get('end_year_month_day')

        force = kwargs.get('force')

        if year_months:     year_months     = natsorted(set(year_months))
        if year_month_days: year_month_days = natsorted(set(year_month_days))

        if start_year_month and end_year_month:
            ## make sure start_year_month precedes end_year_month in time
            if start_year_month >= end_year_month:
                end_year_month = None

        if start_year_month_day and end_year_month_day:
            ## make sure start_year_month_day precedes end_year_month_day in time
            if start_year_month_day >= end_year_month_day:
                end_year_month_day = None

        #############################################################

        command = get_command(full_path=__file__, drop_extention=True)

        today_ymd = get_today_ymd()

        src_logs_dir = settings.LOGS_DIR
        if not path.exists(src_logs_dir):
            return abort(self, f'{src_logs_dir} does not exist.')

        source_logs = get_list_of_files(directory=src_logs_dir, extension='log')
        source_logs = filter_list(
            list_of_items=source_logs,

            year_months=year_months,
            year_month_days=year_month_days,

            start_year_month=start_year_month,
            start_year_month_day=start_year_month_day,

            end_year_month=end_year_month,
            end_year_month_day=end_year_month_day,
        )
        if not source_logs:
            return abort(self, 'no logs.')

        if not windowsserver_list_of_names:
            return abort(self, 'no windowsservers.')

        for source_log_index, source_log in enumerate(source_logs, start=1):
            ## source_log may have been removed
            ## since the start of this command
            if not path.exists(source_log):
                print(colorize(self, 'error', f'{to_tilda(source_log)} does not exist. skipping parsing'))
                continue

            source_log_start = perf_counter()

            log_date = get_date_of_source_log(log_path=source_log)

            if is_invalid_log_date(log_date, today_ymd):
                continue



            ## find object names (i.e. names of windowsservers, routers, etc.)
            ## that have already been accomplished
            ## for this specific log_date
            already_accomplished = []
            if not force:
                for _ in windowsserver_list_of_names:
                    if path.exists(f'{WindowsServerConfig.get_logs_parsed_dir()}/{_}/{log_date}/{log_date}-accomplished.log'):
                        already_accomplished.append(_)
                ## already_accomplished = [
                ##     'Router1',
                ##     'Router3',
                ##     ...
                ## ]

                if len(already_accomplished) == len(windowsserver_list_of_names):
                    print(colorize(self, 'already_parsed', f'{command}: {log_date} all windowsservers already parsed, skipping'))
                    continue



            ## create dictionary of instances
            windowsserver_names_and_instances = {
                _: WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=log_date, object_name=_)
                for _ in windowsserver_list_of_names
            }

            print(source_log_info_line(source_log, source_log_index, len(source_logs)))

            ## pass multiple arguments in pool.map or pool.imap:
            ## parse_line takes 2 arguments:
            ## 1. first one (i.e. ln) will be passed by pool itself
            ##    down below when getting parsed_rows using pool
            ## 2. second one (i.e. already_accomplished) is passed here
            ## (https://medium.com/@deveshparmar248/python-multiprocessing-maximize-the-cpu-utilization-eec3b60e6d40)
            ## (https://python.omics.wiki/multiprocessing_map/multiprocessing_partial_function_multiple_arguments)
            parse_line__partialed = partial(parse_line, already_accomplished=already_accomplished)

            with open(source_log, errors=ACTION_ON_ERROR) as lines:
                with Pool() as pool:
                    valid_lines = pool.imap(
                        func=parse_line__partialed,
                        iterable=lines,
                        chunksize=MYSQLConfig.POOL_CHUNKSIZE.value,
                    )

                    print('parsing...')
                    parse_start = perf_counter()
                    for windowsserver_name_, parsed_ln_ in valid_lines:
                        if not parsed_ln_:
                            continue

                        windowsserver_names_and_instances[windowsserver_name_].rows.append(parsed_ln_)
                    parse_end = perf_counter()
                    parse_duration = int(parse_end - parse_start)
                    print(f'parsed in {parse_duration:,} seconds ({convert_second(seconds=parse_duration, verbose=False)})')

                    valid_lines = None
                    del valid_lines

                lines = None
                del lines


            for windowsserver_name, instance in windowsserver_names_and_instances.items():
                windowsserver_start = perf_counter()

                dest_dir          = f'{WindowsServerConfig.get_logs_parsed_dir()}/{windowsserver_name}/{log_date}'
                accomplished_file = f'{dest_dir}/{log_date}-accomplished.log'
                log_file          = f'{dest_dir}/{log_date}.log'

                database_name = create_name_of_database(WindowsServerConfig.SLUG.value, log_date, windowsserver_name)

                instance__account_logon      = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=log_date, object_name=windowsserver_name, category='accountlogon')
                instance__account_management = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=log_date, object_name=windowsserver_name, category='accountmanagement')
                instance__detailed_tracking  = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=log_date, object_name=windowsserver_name, category='detailedtracking')
                instance__ds_access          = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=log_date, object_name=windowsserver_name, category='dsaccess')
                instance__logon_logoff       = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=log_date, object_name=windowsserver_name, category='logonlogoff')
                instance__object_access      = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=log_date, object_name=windowsserver_name, category='objectaccess')
                instance__policy_change      = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=log_date, object_name=windowsserver_name, category='policychange')
                instance__privilege_use      = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=log_date, object_name=windowsserver_name, category='privilegeuse')
                instance__system             = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=log_date, object_name=windowsserver_name, category='system')
                instance__miscellaneous      = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=log_date, object_name=windowsserver_name, category='miscellaneous')

                ################################################

                ## remove and/or create dest_dir
                if path.exists(dest_dir):
                    should_rm_dest_dir = False

                    if force:
                        should_rm_dest_dir = True
                    else:
                        if path.exists(accomplished_file):
                            print(colorize(self, 'already_parsed', f'{command}: {log_date} for windowsserver {windowsserver_name} is already parsed. skipping'))
                            continue
                        else:
                            should_rm_dest_dir = True

                    if should_rm_dest_dir:
                        print(colorize(self, 'removing', f'removing {to_tilda(dest_dir)}'))
                        rmtree(dest_dir)
                        print(colorize(self, 'creating', f'creating {to_tilda(dest_dir)}'))
                        makedirs(dest_dir)
                else:
                    print(colorize(self, 'creating', f'creating {to_tilda(dest_dir)}'))
                    makedirs(dest_dir)

                ################################################

                ## START __inserting_into_dbs__

                ## drop/create database
                with connect(**MYSQLConfig.MASTER_CREDS.value) as conn:
                    with conn.cursor() as cur:
                        save_log(self, command, settings.HOST_NAME, log_file, f'dropping database {database_name}')
                        cur.execute(f'DROP DATABASE IF EXISTS {database_name};')

                        save_log(self, command, settings.HOST_NAME, log_file, f'creating database {database_name}')
                        cur.execute(f'CREATE DATABASE {database_name};')

                ################################################
                ## *table

                ## __CHUNKED_INFILE__

                def write_into_infile(start_of_chunk, end_of_chunk, infile_path, infile_index, no_of_infiles):
                    _log_msg = f'  writing into {infile_path} ({infile_index}/{no_of_infiles}): {start_of_chunk:,} -> {end_of_chunk:,}'
                    save_log(self, command, settings.HOST_NAME, log_file, _log_msg)

                    _row_id = start_of_chunk
                    with open(infile_path, 'w') as opened:
                        for _instance_row in instance.rows[start_of_chunk:end_of_chunk]:
                            _row_id += 1
                            opened.write(
                                ## ('a', 'b', 'c') -> "1"-*@*-"a"-*@*-"b"-*@*-"c"
                                f'{MYSQLConfig.TERMINATED_BY.value}'.join(
                                    map(
                                        lambda _cell:
                                        f'{MYSQLConfig.ENCLOSED_BY.value}{_cell}{MYSQLConfig.ENCLOSED_BY.value}',

                                        ## add ID to each row:
                                        ## ('a', 'b', 'c') -> (1, 'a', 'b', 'c')
                                        (_row_id,) + _instance_row,
                                    )
                                )+'\n'
                            )

                no_of_infiles = get_no_of_infiles(length=instance.no_of_rows)

                if no_of_infiles:
                    save_log(self, command, settings.HOST_NAME, log_file, f'{instance.no_of_rows:,} rows will be inserted into database')

                    ## create table
                    with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                        with conn.cursor() as cur:
                            save_log(self, command, settings.HOST_NAME, log_file, f'creating table {WindowsServerConfig.get_table_name()}')
                            cur.execute(f'CREATE TABLE {WindowsServerConfig.get_table_name()} ({WindowsServerConfig.DB_COLUMNS.value});')

                            save_log(self, command, settings.HOST_NAME, log_file, f'{no_of_infiles} infiles will be created')

                            ## in each loop:
                            ## STEP 1: create n infiles at the same time
                            ## STEP 2: insert the n infiles into database one at a time
                            for batch_index, batch in enumerate(evenly_sized_batches(total_length=no_of_infiles), start=1):
                                save_log(self, command, settings.HOST_NAME, log_file, f'batch {batch_index}: writing into {len(batch)} infiles')

                                processes = []
                                infile_paths = []

                                ## STEP 1: create n infiles at the same time
                                for infile_index in batch:
                                    infile_path = create_path_of_infile(database_name, WindowsServerConfig.get_table_name(), infile_index)
                                    start_of_chunk = MYSQLConfig.INFILE_CHUNKSIZE.value * (infile_index - 1)
                                    end_of_chunk   = start_of_chunk + MYSQLConfig.INFILE_CHUNKSIZE.value

                                    infile_paths.append(infile_path)
                                    processes.append(
                                        Process(
                                            target=write_into_infile,
                                            args=(start_of_chunk, end_of_chunk, infile_path, infile_index, no_of_infiles)
                                        )
                                    )

                                for p in processes:
                                    p.start()

                                for p in processes:
                                    p.join()

                                ## STEP 2: insert the n infiles into database one at a time
                                log_msg = f'batch {batch_index}: inserting into {WindowsServerConfig.get_table_name()} from {len(infile_paths)} infiles'
                                save_log(self, command, settings.HOST_NAME, log_file, log_msg)

                                for infile_idx, infile_path in enumerate(natsorted(infile_paths), start=1):
                                    if not path.exists(infile_path):
                                        continue

                                    save_log(self, command, settings.HOST_NAME, log_file, f'  inserting from {infile_path}')
                                    cur.execute('SET UNIQUE_CHECKS=0;')
                                    cur.execute('SET FOREIGN_KEY_CHECKS=0;')
                                    ##
                                    ## __TRANSACTION__
                                    ## 'START TRANSACTION' preferred over
                                    ## 'BEGIN' and 'BEGIN WORK':
                                    ##  (https://stackoverflow.com/a/62614872/)
                                    cur.execute('START TRANSACTION;')
                                    cur.execute(f'''
                                        {MYSQLConfig.get_infile_statement()} "{infile_path}"
                                        INTO TABLE {WindowsServerConfig.get_table_name()}
                                        FIELDS TERMINATED BY "{MYSQLConfig.TERMINATED_BY.value}"
                                        ENCLOSED BY '{MYSQLConfig.ENCLOSED_BY.value}'
                                        LINES TERMINATED BY "\n"
                                        (ID,{WindowsServerConfig.DB_KEYS.value})
                                    ;''')

                                ## __TRANSACTION__ commit after loop
                                save_log(self, command, settings.HOST_NAME, log_file, '  committing...')
                                conn.commit()

                                for infile_path in infile_paths:
                                    ## remove infile
                                    save_log(self, command, settings.HOST_NAME, log_file, f'  removing {infile_path}')
                                    remove(infile_path)

                            ## just in case
                            processes = None
                            infile_paths = None
                            del processes
                            del infile_paths

                ################################################
                ## *_and_counts

                ## __INDEXES_ONE_OFF__
                ## the indexes in parse-<APP_SLUG>.py (this script) are lower by 1
                ## compared to its hourly counterpart (i.e. hourly-parse-<APP_SLUG>.py).
                ## in hourly-parse-<APP_SLUG>.py,
                ## instance.rows are directlry read from *table in database
                ## meaning ID column is also included
                ## so we have to increment indexes by 1
                ## to get the right columns

                save_log(self, command, settings.HOST_NAME, log_file, 'preparing *_and_counts')

                for hms, count in Counter(map(itemgetter(1), instance.rows)).items():
                    ## {'00:49:51': 12, '02:59:55': 1182, ...}
                    ## ->
                    ## {'00:00 - 00:59': 416787, '01:00 - 01:59': 416167, ...}
                    instance.times_and_counts[hms_to_hourkey(hms)] += count

                    ## -----

                    ## log_date hms -> millisecond
                    ## (2023-05-12 00:00:26 -> 1624973400000)
                    try:
                        ## a. log_date hms -> timestamp
                        ##    (2023-05-12 00:00:26 -> 1624973400)
                        timestamped = int(mktime(datetime.strptime(f'{log_date} {hms}', '%Y-%m-%d %H:%M:%S').timetuple()))

                        ## b. timestamp -> millisecond
                        ##    (1624973400 -> 1624973400000)
                        timestamped *= 1000

                        instance.milliseconds_and_counts[str(timestamped)] = count
                    except Exception:
                        pass

                instance.eventids_and_counts               = Counter(map(itemgetter(4), instance.rows))
                instance.categories_and_counts             = Counter(map(itemgetter(5), instance.rows))
                instance.potentialcriticalities_and_counts = Counter(map(itemgetter(6), instance.rows))
                instance.accountnames_and_counts           = Counter(map(itemgetter(7), instance.rows))
                instance.accountdomains_and_counts         = Counter(map(itemgetter(8), instance.rows))
                instance.sourceworkstations_and_counts     = Counter(map(itemgetter(9), instance.rows))

                ###########################################################
                ## *toptable

                with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                    with conn.cursor() as cur:
                        for dictionary, table_name, key in [
                            ## dictionary                                 table_name                      column/key
                            (instance.times_and_counts,                  'timetoptable',                 'Time'),
                            (instance.eventids_and_counts,               'eventidtoptable',              '`Event ID`'),
                            (instance.categories_and_counts,             'categorytoptable',             'Category'),
                            (instance.potentialcriticalities_and_counts, 'potentialcriticalitytoptable', '`Potential Criticality`'),
                            (instance.accountnames_and_counts,           'accountnametoptable',          '`Account Name`'),
                            (instance.accountdomains_and_counts,         'accountdomaintoptable',        '`Account Domain`'),
                            (instance.sourceworkstations_and_counts,     'sourceworkstationtoptable',    '`Source Workstation`'),

                            (instance.milliseconds_and_counts,           'millisecondtoptable',          'Millisecond'),
                        ]:
                            if key in ['Time', 'Millisecond'] and all_values_are_0(dictionary):
                                dictionary = {}

                            if not dictionary:
                                continue

                            if key in ['Time', 'Millisecond']:
                                sorted_dict = sort_dict(dictionary, based_on='key', reverse=False)
                            else:
                                sorted_dict = sort_dict(dictionary, based_on='value', reverse=True)

                            table_columns = f'''
                                ID    {MYSQLConfig.ID_DATA_TYPE.value},
                                {key} {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                                Count {MYSQLConfig.COUNT_DATA_TYPE.value}'''
                            table_keys = f'{key},Count'
                            table_marks = '%s,%s'

                            save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                            cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                            save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(sorted_dict):,} rows into {table_name}')
                            cur.execute('START TRANSACTION;')
                            cur.executemany(
                                f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                                tuple(sorted_dict.items())
                            )

                        conn.commit()






                ################################################
                ## vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv ##

                ## preparing categorized instances

                ## STEP 1: populate rows for categorized instances
                for row_ in instance.rows:
                    ## __INDEXES_ONE_OFF__
                    event_id = row_[4]
                    if event_id and event_id.isdigit():
                        if   event_id in WindowsServerConfig.EVENT_IDS__ACCOUNT_LOGON.value:      instance__account_logon.rows.append(row_)
                        elif event_id in WindowsServerConfig.EVENT_IDS__ACCOUNT_MANAGEMENT.value: instance__account_management.rows.append(row_)
                        elif event_id in WindowsServerConfig.EVENT_IDS__DETAILED_TRACKING.value:  instance__detailed_tracking.rows.append(row_)
                        elif event_id in WindowsServerConfig.EVENT_IDS__DS_ACCESS.value:          instance__ds_access.rows.append(row_)
                        elif event_id in WindowsServerConfig.EVENT_IDS__LOGONLOGOFF.value:        instance__logon_logoff.rows.append(row_)
                        elif event_id in WindowsServerConfig.EVENT_IDS__OBJECT_ACCESS.value:      instance__object_access.rows.append(row_)
                        elif event_id in WindowsServerConfig.EVENT_IDS__POLICY_CHANGE.value:      instance__policy_change.rows.append(row_)
                        elif event_id in WindowsServerConfig.EVENT_IDS__PRIVILEGE_USE.value:      instance__privilege_use.rows.append(row_)
                        elif event_id in WindowsServerConfig.EVENT_IDS__SYSTEM.value:             instance__system.rows.append(row_)
                        elif event_id in WindowsServerConfig.EVENT_IDS__MISCELLANEOUS.value:      instance__miscellaneous.rows.append(row_)
                        else: pass



                ## STEP 2: create a list of categorized instances
                instances_ctgzd = [
                    instance__account_logon,
                    instance__account_management,
                    instance__detailed_tracking,
                    instance__ds_access,
                    instance__logon_logoff,
                    instance__object_access,
                    instance__policy_change,
                    instance__privilege_use,
                    instance__system,
                    instance__miscellaneous,
                ]

                ## ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ ##
                ################################################

                for instance_ctgzd in instances_ctgzd:
                    if not instance_ctgzd:
                        continue

                    ## *table (categorized)

                    ## __CHUNKED_INFILE__

                    if   instance_ctgzd.category == 'accountlogon':      table_name_ = WindowsServerConfig.get_table_name(category='accountlogon')
                    elif instance_ctgzd.category == 'accountmanagement': table_name_ = WindowsServerConfig.get_table_name(category='accountmanagement')
                    elif instance_ctgzd.category == 'detailedtracking':  table_name_ = WindowsServerConfig.get_table_name(category='detailedtracking')
                    elif instance_ctgzd.category == 'dsaccess':          table_name_ = WindowsServerConfig.get_table_name(category='dsaccess')
                    elif instance_ctgzd.category == 'logonlogoff':       table_name_ = WindowsServerConfig.get_table_name(category='logonlogoff')
                    elif instance_ctgzd.category == 'objectaccess':      table_name_ = WindowsServerConfig.get_table_name(category='objectaccess')
                    elif instance_ctgzd.category == 'policychange':      table_name_ = WindowsServerConfig.get_table_name(category='policychange')
                    elif instance_ctgzd.category == 'privilegeuse':      table_name_ = WindowsServerConfig.get_table_name(category='privilegeuse')
                    elif instance_ctgzd.category == 'system':            table_name_ = WindowsServerConfig.get_table_name(category='system')
                    elif instance_ctgzd.category == 'miscellaneous':     table_name_ = WindowsServerConfig.get_table_name(category='miscellaneous')
                    else: table_name_ = None

                    if not table_name_:
                        continue

                    def write_into_infile(start_of_chunk, end_of_chunk, infile_path, infile_index, no_of_infiles):
                        _log_msg = f'  writing into {infile_path} ({infile_index}/{no_of_infiles}): {start_of_chunk:,} -> {end_of_chunk:,}'
                        save_log(self, command, settings.HOST_NAME, log_file, _log_msg)

                        _row_id = start_of_chunk
                        with open(infile_path, 'w') as opened:
                            for _instance_row in instance_ctgzd.rows[start_of_chunk:end_of_chunk]:
                                _row_id += 1
                                opened.write(
                                    ## ('a', 'b', 'c') -> "1"-*@*-"a"-*@*-"b"-*@*-"c"
                                    f'{MYSQLConfig.TERMINATED_BY.value}'.join(
                                        map(
                                            lambda _cell:
                                            f'{MYSQLConfig.ENCLOSED_BY.value}{_cell}{MYSQLConfig.ENCLOSED_BY.value}',

                                            ## add ID to each row:
                                            ## ('a', 'b', 'c') -> (1, 'a', 'b', 'c')
                                            (_row_id,) + _instance_row,
                                        )
                                    )+'\n'
                                )

                    no_of_infiles = get_no_of_infiles(length=instance_ctgzd.no_of_rows)

                    if no_of_infiles:
                        save_log(self, command, settings.HOST_NAME, log_file, f'{instance_ctgzd.no_of_rows:,} rows will be inserted into database')

                        ## create table
                        with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                            with conn.cursor() as cur:
                                save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name_}')
                                cur.execute(f'CREATE TABLE {table_name_} ({WindowsServerConfig.DB_COLUMNS.value});')

                                save_log(self, command, settings.HOST_NAME, log_file, f'{no_of_infiles} infiles will be created')

                                ## in each loop:
                                ## STEP 1: create n infiles at the same time
                                ## STEP 2: insert the n infiles into database one at a time
                                for batch_index, batch in enumerate(evenly_sized_batches(total_length=no_of_infiles), start=1):
                                    save_log(self, command, settings.HOST_NAME, log_file, f'batch {batch_index}: writing into {len(batch)} infiles')

                                    processes = []
                                    infile_paths = []

                                    ## STEP 1: create n infiles at the same time
                                    for infile_index in batch:
                                        infile_path = create_path_of_infile(database_name, table_name_, infile_index)
                                        start_of_chunk = MYSQLConfig.INFILE_CHUNKSIZE.value * (infile_index - 1)
                                        end_of_chunk   = start_of_chunk + MYSQLConfig.INFILE_CHUNKSIZE.value

                                        infile_paths.append(infile_path)
                                        processes.append(
                                            Process(
                                                target=write_into_infile,
                                                args=(start_of_chunk, end_of_chunk, infile_path, infile_index, no_of_infiles)
                                            )
                                        )

                                    for p in processes:
                                        p.start()

                                    for p in processes:
                                        p.join()

                                    ## STEP 2: insert the n infiles into database one at a time
                                    log_msg = f'batch {batch_index}: inserting into {table_name_} from {len(infile_paths)} infiles'
                                    save_log(self, command, settings.HOST_NAME, log_file, log_msg)

                                    for infile_idx, infile_path in enumerate(natsorted(infile_paths), start=1):
                                        if not path.exists(infile_path):
                                            continue

                                        save_log(self, command, settings.HOST_NAME, log_file, f'  inserting from {infile_path}')
                                        cur.execute('SET UNIQUE_CHECKS=0;')
                                        cur.execute('SET FOREIGN_KEY_CHECKS=0;')
                                        ##
                                        ## __TRANSACTION__
                                        ## 'START TRANSACTION' preferred over
                                        ## 'BEGIN' and 'BEGIN WORK':
                                        ##  (https://stackoverflow.com/a/62614872/)
                                        cur.execute('START TRANSACTION;')
                                        cur.execute(f'''
                                            {MYSQLConfig.get_infile_statement()} "{infile_path}"
                                            INTO TABLE {table_name_}
                                            FIELDS TERMINATED BY "{MYSQLConfig.TERMINATED_BY.value}"
                                            ENCLOSED BY '{MYSQLConfig.ENCLOSED_BY.value}'
                                            LINES TERMINATED BY "\n"
                                            (ID,{WindowsServerConfig.DB_KEYS.value})
                                        ;''')

                                    ## __TRANSACTION__ commit after loop
                                    save_log(self, command, settings.HOST_NAME, log_file, '  committing...')
                                    conn.commit()

                                    for infile_path in infile_paths:
                                        ## remove infile
                                        save_log(self, command, settings.HOST_NAME, log_file, f'  removing {infile_path}')
                                        remove(infile_path)

                                ## just in case
                                processes = None
                                infile_paths = None
                                del processes
                                del infile_paths






                    ## *_and_counts (categorized)

                    ## __INDEXES_ONE_OFF__

                    save_log(self, command, settings.HOST_NAME, log_file, 'preparing *_and_counts')

                    ## {'00:49:51': 12, '02:59:55': 1182, '17:54:43': 280, ...}
                    ## ->
                    ## {'00:00 - 00:59': 416787, '01:00 - 01:59': 416167, ...}
                    for hms, count in Counter(map(itemgetter(1), instance_ctgzd.rows)).items():
                        instance_ctgzd.times_and_counts[hms_to_hourkey(hms)] += count

                    instance_ctgzd.eventids_and_counts               = Counter(map(itemgetter(4), instance_ctgzd.rows))
                    instance_ctgzd.categories_and_counts             = Counter(map(itemgetter(5), instance_ctgzd.rows))
                    instance_ctgzd.potentialcriticalities_and_counts = Counter(map(itemgetter(6), instance_ctgzd.rows))
                    instance_ctgzd.accountnames_and_counts           = Counter(map(itemgetter(7), instance_ctgzd.rows))
                    instance_ctgzd.accountdomains_and_counts         = Counter(map(itemgetter(8), instance_ctgzd.rows))
                    instance_ctgzd.sourceworkstations_and_counts     = Counter(map(itemgetter(9), instance_ctgzd.rows))





                    ## __CATEGORIZED_TOPTABLE__
                    ## *toptable (categorized)
                    ##
                    ##   timetoptable__accountlogon      eventidtoptable__accountlogon      categorytoptable__accountlogon      potentialcriticalitytoptable__accountlogon      accountnametoptable__accountlogon      accountdomaintoptable__accountlogon      sourceworkstationtoptable__accountlogon
                    ##   timetoptable__accountmanagement eventidtoptable__accountmanagement categorytoptable__accountmanagement potentialcriticalitytoptable__accountmanagement accountnametoptable__accountmanagement accountdomaintoptable__accountmanagement sourceworkstationtoptable__accountmanagement
                    ##   timetoptable__detailedtracking  eventidtoptable__detailedtracking  categorytoptable__detailedtracking  potentialcriticalitytoptable__detailedtracking  accountnametoptable__detailedtracking  accountdomaintoptable__detailedtracking  sourceworkstationtoptable__detailedtracking
                    ##   timetoptable__dsaccess          eventidtoptable__dsaccess          categorytoptable__dsaccess          potentialcriticalitytoptable__dsaccess          accountnametoptable__dsaccess          accountdomaintoptable__dsaccess          sourceworkstationtoptable__dsaccess
                    ##   timetoptable__logonlogoff       eventidtoptable__logonlogoff       categorytoptable__logonlogoff       potentialcriticalitytoptable__logonlogoff       accountnametoptable__logonlogoff       accountdomaintoptable__logonlogoff       sourceworkstationtoptable__logonlogoff
                    ##   timetoptable__objectaccess      eventidtoptable__objectaccess      categorytoptable__objectaccess      potentialcriticalitytoptable__objectaccess      accountnametoptable__objectaccess      accountdomaintoptable__objectaccess      sourceworkstationtoptable__objectaccess
                    ##   timetoptable__policychange      eventidtoptable__policychange      categorytoptable__policychange      potentialcriticalitytoptable__policychange      accountnametoptable__policychange      accountdomaintoptable__policychange      sourceworkstationtoptable__policychange
                    ##   timetoptable__privilegeuse      eventidtoptable__privilegeuse      categorytoptable__privilegeuse      potentialcriticalitytoptable__privilegeuse      accountnametoptable__privilegeuse      accountdomaintoptable__privilegeuse      sourceworkstationtoptable__privilegeuse
                    ##   timetoptable__system            eventidtoptable__system            categorytoptable__system            potentialcriticalitytoptable__system            accountnametoptable__system            accountdomaintoptable__system            sourceworkstationtoptable__system
                    ##   timetoptable__miscellaneous     eventidtoptable__miscellaneous     categorytoptable__miscellaneous     potentialcriticalitytoptable__miscellaneous     accountnametoptable__miscellaneous     accountdomaintoptable__miscellaneous     sourceworkstationtoptable__miscellaneous

                    with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                        with conn.cursor() as cur:
                            for dictionary, table_name, key in [
                                ## dictionary                                        table_name                                                                                     column/key
                                (instance_ctgzd.times_and_counts,                  f'timetoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}',                 'Time'),
                                (instance_ctgzd.eventids_and_counts,               f'eventidtoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}',              '`Event ID`'),
                                (instance_ctgzd.categories_and_counts,             f'categorytoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}',             'Category'),
                                (instance_ctgzd.potentialcriticalities_and_counts, f'potentialcriticalitytoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}', '`Potential Criticality`'),
                                (instance_ctgzd.accountnames_and_counts,           f'accountnametoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}',          '`Account Name`'),
                                (instance_ctgzd.accountdomains_and_counts,         f'accountdomaintoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}',        '`Account Domain`'),
                                (instance_ctgzd.sourceworkstations_and_counts,     f'sourceworkstationtoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}',    '`Source Workstation`'),

                                # (instance_ctgzd.milliseconds_and_counts,           f'millisecondtoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}',          'Millisecond'),
                            ]:
                                if key in ['Time', 'Millisecond'] and all_values_are_0(dictionary):
                                    dictionary = {}

                                if not dictionary:
                                    continue

                                if key in ['Time', 'Millisecond']:
                                    sorted_dict = sort_dict(dictionary, based_on='key', reverse=False)
                                else:
                                    sorted_dict = sort_dict(dictionary, based_on='value', reverse=True)

                                table_columns = f'''
                                    ID    {MYSQLConfig.ID_DATA_TYPE.value},
                                    {key} {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                                    Count {MYSQLConfig.COUNT_DATA_TYPE.value}'''
                                table_keys = f'{key},Count'
                                table_marks = '%s,%s'

                                save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                                cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                                save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(sorted_dict):,} rows into {table_name}')
                                cur.execute('START TRANSACTION;')
                                cur.executemany(
                                    f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                                    tuple(sorted_dict.items())
                                )

                            conn.commit()

                ################################################
                instance.truncate_all()

                for instance_ctgzd in instances_ctgzd:
                    instance_ctgzd.truncate_all()
                ################################################

                save_log(self, command, settings.HOST_NAME, log_file, f'database: {database_name}, {get_size_of_database(database_name, convert=True)}')
                save_log(self, command, settings.HOST_NAME, log_file, f'tables: {str(get_tables_and_sizes(database_name, convert=True, reverse=True))}')

                ################################################

                ## create accomplished_file
                save_log(self, command, settings.HOST_NAME, accomplished_file, 'accomplished', echo=False)

                windowsserver_end = perf_counter()
                windowsserver_duration = int(windowsserver_end - windowsserver_start)

                save_log(self, command, settings.HOST_NAME, log_file, f'accomplished in {windowsserver_duration:,} seconds ({convert_second(seconds=windowsserver_duration, verbose=False)})')
                print(separator())

                ## END __inserting_into_dbs__


            source_log_end = perf_counter()
            source_log_duration = int(source_log_end - source_log_start)

            print(f'{command} {log_date} {len(windowsserver_list_of_names)} windowsservers in {source_log_duration:,} seconds ({convert_second(seconds=source_log_duration, verbose=False)})')
            print(separator())


        print(end_of_command_msg(self, command))
