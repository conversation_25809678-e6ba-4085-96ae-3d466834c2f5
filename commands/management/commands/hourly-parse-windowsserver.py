from django.conf import settings
from django.core.management.base import BaseCommand

from collections import Counter
from datetime import datetime
from operator import itemgetter
from os import path, makedirs
from signal import SIGINT, signal
from time import perf_counter, mktime

from MySQLdb import connect
from rahavard import (
    abort,
    colorize,
    convert_second,
    get_command,
    keyboard_interrupt_handler,
    save_log,
    sort_dict,
    to_tilda,
)

from base.utils_classes import (
    WindowsServerParser,
    WindowsServerConfig,
    MYSQLConfig,
)

from base.utils_database import (
    get_size_of_database,
    get_tables_and_sizes,
)

from base.utils import (
    all_values_are_0,
    command_instance_is_running,
    create_name_of_database,
    end_of_command_msg,
    get_today_ymd,
    hms_to_hourkey,
    separator,
)

from base.models import WindowsServer


signal(SIGINT, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = f'Hourly Parse {WindowsServerConfig.TITLE.value}'

    def handle(self, *args, **kwargs):
        command = get_command(full_path=__file__, drop_extention=True)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        today_ymd = get_today_ymd()

        windowsserver_list_of_names = WindowsServer.get_list_of_names()

        if not windowsserver_list_of_names:
            return abort(self, 'no windowsservers.')

        for windowsserver_name in windowsserver_list_of_names:
            windowsserver_start = perf_counter()

            dest_dir          = f'{WindowsServerConfig.get_logs_parsed_dir()}/{windowsserver_name}/{today_ymd}'
            # accomplished_file = f'{dest_dir}/{today_ymd}-accomplished.log'
            log_file          = f'{dest_dir}/{today_ymd}.log'

            database_name = create_name_of_database(WindowsServerConfig.SLUG.value, today_ymd, windowsserver_name)

            instance                     = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=today_ymd, object_name=windowsserver_name)
            instance__account_logon      = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=today_ymd, object_name=windowsserver_name, category='accountlogon')
            instance__account_management = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=today_ymd, object_name=windowsserver_name, category='accountmanagement')
            instance__detailed_tracking  = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=today_ymd, object_name=windowsserver_name, category='detailedtracking')
            instance__ds_access          = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=today_ymd, object_name=windowsserver_name, category='dsaccess')
            instance__logon_logoff       = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=today_ymd, object_name=windowsserver_name, category='logonlogoff')
            instance__object_access      = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=today_ymd, object_name=windowsserver_name, category='objectaccess')
            instance__policy_change      = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=today_ymd, object_name=windowsserver_name, category='policychange')
            instance__privilege_use      = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=today_ymd, object_name=windowsserver_name, category='privilegeuse')
            instance__system             = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=today_ymd, object_name=windowsserver_name, category='system')
            instance__miscellaneous      = WindowsServerParser(slug=WindowsServerConfig.SLUG.value, ymd=today_ymd, object_name=windowsserver_name, category='miscellaneous')

            ## get instance.rows
            ## (throws exception if database does not exist)
            try:
                with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                    with conn.cursor() as cur:
                        cur.execute(f'{WindowsServerConfig.get_select_statement(category=None)};')
                        instance.rows = cur.fetchall()
            except:
                pass

            ## instance.rows = [
            ##     (1, '2024-08-29', '09:47:29', ...),
            ##     (2, '2024-08-29', '09:47:30', ...),
            ##     ...
            ## ]

            ## instance.rows can be empty
            ## even if reading database was successful
            if not instance.rows:
                ## NOTE do NOT abort(...)
                ##      otherwise other instances will not be parsed.
                ##      use print(...) and continue instead
                print(colorize(self, 'error', f'empty rows from database {database_name}'))
                continue


            ## needed for creating log_file
            if not path.exists(dest_dir):
                print(colorize(self, 'creating', f'creating {to_tilda(dest_dir)}'))
                makedirs(dest_dir)



            ## __INDEXES_ONE_OFF__

            save_log(self, command, settings.HOST_NAME, log_file, 'preparing *_and_counts')

            for hms, count in Counter(map(itemgetter(1+1), instance.rows)).items():
                ## {'00:49:51': 12, '02:59:55': 1182, ...}
                ## ->
                ## {'00:00 - 00:59': 416787, '01:00 - 01:59': 416167, ...}
                instance.times_and_counts[hms_to_hourkey(hms)] += count

                ## -----

                ## today_ymd hms -> millisecond
                ## (2023-05-12 00:00:26 -> 1624973400000)
                try:
                    ## a. today_ymd hms -> timestamp
                    ##    (2023-05-12 00:00:26 -> 1624973400)
                    timestamped = int(mktime(datetime.strptime(f'{today_ymd} {hms}', '%Y-%m-%d %H:%M:%S').timetuple()))

                    ## b. timestamp -> millisecond
                    ##    (1624973400 -> 1624973400000)
                    timestamped *= 1000

                    instance.milliseconds_and_counts[str(timestamped)] = count
                except Exception:
                    pass

            instance.eventids_and_counts               = Counter(map(itemgetter(4+1), instance.rows))
            instance.categories_and_counts             = Counter(map(itemgetter(5+1), instance.rows))
            instance.potentialcriticalities_and_counts = Counter(map(itemgetter(6+1), instance.rows))
            instance.accountnames_and_counts           = Counter(map(itemgetter(7+1), instance.rows))
            instance.accountdomains_and_counts         = Counter(map(itemgetter(8+1), instance.rows))
            instance.sourceworkstations_and_counts     = Counter(map(itemgetter(9+1), instance.rows))

            ################################################
            ## *toptable

            with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    for dictionary, table_name, key in [
                        ## dictionary                                 table_name                      column/key
                        (instance.times_and_counts,                  'timetoptable',                 'Time'),
                        (instance.eventids_and_counts,               'eventidtoptable',              '`Event ID`'),
                        (instance.categories_and_counts,             'categorytoptable',             'Category'),
                        (instance.potentialcriticalities_and_counts, 'potentialcriticalitytoptable', '`Potential Criticality`'),
                        (instance.accountnames_and_counts,           'accountnametoptable',          '`Account Name`'),
                        (instance.accountdomains_and_counts,         'accountdomaintoptable',        '`Account Domain`'),
                        (instance.sourceworkstations_and_counts,     'sourceworkstationtoptable',    '`Source Workstation`'),

                        (instance.milliseconds_and_counts,           'millisecondtoptable',          'Millisecond'),
                    ]:
                        if key in ['Time', 'Millisecond'] and all_values_are_0(dictionary):
                            dictionary = {}

                        if not dictionary:
                            continue

                        if key in ['Time', 'Millisecond']:
                            sorted_dict = sort_dict(dictionary, based_on='key', reverse=False)
                        else:
                            sorted_dict = sort_dict(dictionary, based_on='value', reverse=True)

                        table_columns = f'''
                            ID    {MYSQLConfig.ID_DATA_TYPE.value},
                            {key} {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                            Count {MYSQLConfig.COUNT_DATA_TYPE.value}'''
                        table_keys = f'{key},Count'
                        table_marks = '%s,%s'

                        ## DROP table
                        save_log(self, command, settings.HOST_NAME, log_file, f'dropping table {table_name}')
                        cur.execute(f'DROP TABLE IF EXISTS {table_name};')

                        save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                        cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                        save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(sorted_dict):,} rows into {table_name}')
                        cur.execute('START TRANSACTION;')
                        cur.executemany(
                            f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                            tuple(sorted_dict.items())
                        )

                    conn.commit()






            ################################################
            ## vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv ##

            ## preparing categorized instances

            ## STEP 1: populate rows for categorized instances
            for row_ in instance.rows:
                ## __INDEXES_ONE_OFF__
                event_id = row_[4+1]
                if event_id and event_id.isdigit():
                    if   event_id in WindowsServerConfig.EVENT_IDS__ACCOUNT_LOGON.value:      instance__account_logon.rows.append(row_)
                    elif event_id in WindowsServerConfig.EVENT_IDS__ACCOUNT_MANAGEMENT.value: instance__account_management.rows.append(row_)
                    elif event_id in WindowsServerConfig.EVENT_IDS__DETAILED_TRACKING.value:  instance__detailed_tracking.rows.append(row_)
                    elif event_id in WindowsServerConfig.EVENT_IDS__DS_ACCESS.value:          instance__ds_access.rows.append(row_)
                    elif event_id in WindowsServerConfig.EVENT_IDS__LOGONLOGOFF.value:        instance__logon_logoff.rows.append(row_)
                    elif event_id in WindowsServerConfig.EVENT_IDS__OBJECT_ACCESS.value:      instance__object_access.rows.append(row_)
                    elif event_id in WindowsServerConfig.EVENT_IDS__POLICY_CHANGE.value:      instance__policy_change.rows.append(row_)
                    elif event_id in WindowsServerConfig.EVENT_IDS__PRIVILEGE_USE.value:      instance__privilege_use.rows.append(row_)
                    elif event_id in WindowsServerConfig.EVENT_IDS__SYSTEM.value:             instance__system.rows.append(row_)
                    elif event_id in WindowsServerConfig.EVENT_IDS__MISCELLANEOUS.value:      instance__miscellaneous.rows.append(row_)
                    else: pass



            ## STEP 2: create a list of categorized instances
            instances_ctgzd = [
                instance__account_logon,
                instance__account_management,
                instance__detailed_tracking,
                instance__ds_access,
                instance__logon_logoff,
                instance__object_access,
                instance__policy_change,
                instance__privilege_use,
                instance__system,
                instance__miscellaneous,
            ]

            ## ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ ##
            ################################################

            for instance_ctgzd in instances_ctgzd:
                if not instance_ctgzd:
                    continue

                ## *table (categorized)
                ## ...





                ## *_and_counts (categorized)

                ## __INDEXES_ONE_OFF__

                save_log(self, command, settings.HOST_NAME, log_file, 'preparing *_and_counts')

                ## {'00:49:51': 12, '02:59:55': 1182, '17:54:43': 280, ...}
                ## ->
                ## {'00:00 - 00:59': 416787, '01:00 - 01:59': 416167, ...}
                for hms, count in Counter(map(itemgetter(1+1), instance_ctgzd.rows)).items():
                    instance_ctgzd.times_and_counts[hms_to_hourkey(hms)] += count

                instance_ctgzd.eventids_and_counts               = Counter(map(itemgetter(4+1), instance_ctgzd.rows))
                instance_ctgzd.categories_and_counts             = Counter(map(itemgetter(5+1), instance_ctgzd.rows))
                instance_ctgzd.potentialcriticalities_and_counts = Counter(map(itemgetter(6+1), instance_ctgzd.rows))
                instance_ctgzd.accountnames_and_counts           = Counter(map(itemgetter(7+1), instance_ctgzd.rows))
                instance_ctgzd.accountdomains_and_counts         = Counter(map(itemgetter(8+1), instance_ctgzd.rows))
                instance_ctgzd.sourceworkstations_and_counts     = Counter(map(itemgetter(9+1), instance_ctgzd.rows))





                ## __CATEGORIZED_TOPTABLE__
                ## *toptable (categorized)
                ##
                ##   timetoptable__accountlogon      eventidtoptable__accountlogon      categorytoptable__accountlogon      potentialcriticalitytoptable__accountlogon      accountnametoptable__accountlogon      accountdomaintoptable__accountlogon      sourceworkstationtoptable__accountlogon
                ##   timetoptable__accountmanagement eventidtoptable__accountmanagement categorytoptable__accountmanagement potentialcriticalitytoptable__accountmanagement accountnametoptable__accountmanagement accountdomaintoptable__accountmanagement sourceworkstationtoptable__accountmanagement
                ##   timetoptable__detailedtracking  eventidtoptable__detailedtracking  categorytoptable__detailedtracking  potentialcriticalitytoptable__detailedtracking  accountnametoptable__detailedtracking  accountdomaintoptable__detailedtracking  sourceworkstationtoptable__detailedtracking
                ##   timetoptable__dsaccess          eventidtoptable__dsaccess          categorytoptable__dsaccess          potentialcriticalitytoptable__dsaccess          accountnametoptable__dsaccess          accountdomaintoptable__dsaccess          sourceworkstationtoptable__dsaccess
                ##   timetoptable__logonlogoff       eventidtoptable__logonlogoff       categorytoptable__logonlogoff       potentialcriticalitytoptable__logonlogoff       accountnametoptable__logonlogoff       accountdomaintoptable__logonlogoff       sourceworkstationtoptable__logonlogoff
                ##   timetoptable__objectaccess      eventidtoptable__objectaccess      categorytoptable__objectaccess      potentialcriticalitytoptable__objectaccess      accountnametoptable__objectaccess      accountdomaintoptable__objectaccess      sourceworkstationtoptable__objectaccess
                ##   timetoptable__policychange      eventidtoptable__policychange      categorytoptable__policychange      potentialcriticalitytoptable__policychange      accountnametoptable__policychange      accountdomaintoptable__policychange      sourceworkstationtoptable__policychange
                ##   timetoptable__privilegeuse      eventidtoptable__privilegeuse      categorytoptable__privilegeuse      potentialcriticalitytoptable__privilegeuse      accountnametoptable__privilegeuse      accountdomaintoptable__privilegeuse      sourceworkstationtoptable__privilegeuse
                ##   timetoptable__system            eventidtoptable__system            categorytoptable__system            potentialcriticalitytoptable__system            accountnametoptable__system            accountdomaintoptable__system            sourceworkstationtoptable__system
                ##   timetoptable__miscellaneous     eventidtoptable__miscellaneous     categorytoptable__miscellaneous     potentialcriticalitytoptable__miscellaneous     accountnametoptable__miscellaneous     accountdomaintoptable__miscellaneous     sourceworkstationtoptable__miscellaneous

                with connect(**MYSQLConfig.MASTER_CREDS.value, database=database_name) as conn:
                    with conn.cursor() as cur:
                        for dictionary, table_name, key in [
                            ## dictionary                                        table_name                                                                                     column/key
                            (instance_ctgzd.times_and_counts,                  f'timetoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}',                 'Time'),
                            (instance_ctgzd.eventids_and_counts,               f'eventidtoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}',              '`Event ID`'),
                            (instance_ctgzd.categories_and_counts,             f'categorytoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}',             'Category'),
                            (instance_ctgzd.potentialcriticalities_and_counts, f'potentialcriticalitytoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}', '`Potential Criticality`'),
                            (instance_ctgzd.accountnames_and_counts,           f'accountnametoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}',          '`Account Name`'),
                            (instance_ctgzd.accountdomains_and_counts,         f'accountdomaintoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}',        '`Account Domain`'),
                            (instance_ctgzd.sourceworkstations_and_counts,     f'sourceworkstationtoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}',    '`Source Workstation`'),

                            # (instance_ctgzd.milliseconds_and_counts,           f'millisecondtoptable{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{instance_ctgzd.category}',          'Millisecond'),
                        ]:
                            if key in ['Time', 'Millisecond'] and all_values_are_0(dictionary):
                                dictionary = {}

                            if not dictionary:
                                continue

                            if key in ['Time', 'Millisecond']:
                                sorted_dict = sort_dict(dictionary, based_on='key', reverse=False)
                            else:
                                sorted_dict = sort_dict(dictionary, based_on='value', reverse=True)

                            table_columns = f'''
                                ID    {MYSQLConfig.ID_DATA_TYPE.value},
                                {key} {MYSQLConfig.DEFAULT_DATA_TYPE.value},
                                Count {MYSQLConfig.COUNT_DATA_TYPE.value}'''
                            table_keys = f'{key},Count'
                            table_marks = '%s,%s'

                            ## DROP table
                            save_log(self, command, settings.HOST_NAME, log_file, f'dropping table {table_name}')
                            cur.execute(f'DROP TABLE IF EXISTS {table_name};')

                            save_log(self, command, settings.HOST_NAME, log_file, f'creating table {table_name}')
                            cur.execute(f'CREATE TABLE {table_name} ({table_columns});')

                            save_log(self, command, settings.HOST_NAME, log_file, f'inserting {len(sorted_dict):,} rows into {table_name}')
                            cur.execute('START TRANSACTION;')
                            cur.executemany(
                                f'INSERT INTO {table_name} ({table_keys}) VALUES ({table_marks});',
                                tuple(sorted_dict.items())
                            )

                        conn.commit()

            ################################################
            instance.truncate_all()

            for instance_ctgzd in instances_ctgzd:
                instance_ctgzd.truncate_all()
            ################################################

            save_log(self, command, settings.HOST_NAME, log_file, f'database: {database_name}, {get_size_of_database(database_name, convert=True)}')
            save_log(self, command, settings.HOST_NAME, log_file, f'tables: {str(get_tables_and_sizes(database_name, convert=True, reverse=True))}')

            ################################################

            ## create accomplished_file
            # save_log(self, command, settings.HOST_NAME, accomplished_file, 'accomplished', echo=False)

            windowsserver_end = perf_counter()
            windowsserver_duration = int(windowsserver_end - windowsserver_start)

            save_log(self, command, settings.HOST_NAME, log_file, f'accomplished in {windowsserver_duration:,} seconds ({convert_second(seconds=windowsserver_duration, verbose=False)})')
            print(separator())

        print(end_of_command_msg(self, command))
