from django.conf import settings
from django.core.management.base import Base<PERSON>ommand

from os import path, remove
from shutil import rmtree
from signal import SIGINT, signal
from time import sleep

from MySQLdb import connect
from rahavard import (
    abort,
    get_command,
    get_command_log_file,
    get_list_of_files,
    keyboard_interrupt_handler,
    save_log,
    to_tilda,
)

from base.utils_classes import (
    MYSQLConfig,
)

from base.utils_database import (
    get_databases,
)

from base.utils import (
    command_instance_is_running,
    dash_to_underscore,
    filter_databases,
    filter_list,
    get_directory_path_from_name_of_database,
    get_today_ymd,
    move_n_days,
)


signal(SIGINT, keyboard_interrupt_handler)


class Command(BaseCommand):
    help = 'Remove Old Logs and Databases'

    def handle(self, *args, **kwargs):
        command = get_command(full_path=__file__, drop_extention=True)
        log_file = get_command_log_file(command)

        if command_instance_is_running(command):
            return abort(self, f'{command} instance is running')

        if settings.DEBUG:
            return abort(self, f'{command} is only allowed on production server.')

        ## __DISABLE_MAX_N_DAYS_BY_0__
        if settings.MAX_N_DAYS == 0:
            return abort(self, 'MAX_N_DAYS is 0. skipping')

        today_ymd = get_today_ymd()

        ## source logs and databases
        ## whose date is before this
        ## will get removed
        min_date = move_n_days(today_ymd, n=settings.MAX_N_DAYS * -1)

        ## --------------------------------
        ## source logs

        src_logs_dir = settings.LOGS_DIR
        source_logs = get_list_of_files(directory=src_logs_dir, extension='log')
        source_logs = filter_list(
            list_of_items=source_logs,
            end_year_month_day=min_date,
        )

        if not source_logs:
            save_log(self, command, settings.HOST_NAME, log_file, 'no logs. skipping removing')
        else:
            for source_log in source_logs:
                save_log(self, command, settings.HOST_NAME, log_file, f'removing {to_tilda(source_log)}')
                remove(source_log)

        ## --------------------------------
        ## databases

        ## __DATABASE_YMD_PATTERN__
        ## 2024-01-10 -> 2024_01_10
        min_date_ = dash_to_underscore(min_date)

        databases = get_databases(include_builtins=False)
        databases = filter_databases(
            databases=databases,
            end_year_month_day=min_date_,
        )

        ## exclude non-dated databases
        databases = [
            _ for _ in databases
            if _ not in MYSQLConfig.NON_DATED_DATABASES.value
        ]

        if not databases:
            save_log(self, command, settings.HOST_NAME, log_file, 'no databases. skipping dropping')
        else:
            ## also in commands/mysql.py
            ## in 'drop databases' option
            with connect(**MYSQLConfig.MASTER_CREDS.value) as conn:
                with conn.cursor() as cur:
                    for db in databases:
                        ## drop database
                        save_log(self, command, settings.HOST_NAME, log_file, f'dropping database {db}')
                        cur.execute(f'DROP DATABASE IF EXISTS {db};')

                        ## remove directory
                        dir_name = get_directory_path_from_name_of_database(db)
                        if dir_name:
                            if path.exists(dir_name):
                                save_log(self, command, settings.HOST_NAME, log_file, f'removing {to_tilda(dir_name)}')
                                rmtree(dir_name)
                            else:
                                save_log(self, command, settings.HOST_NAME, log_file, f'{to_tilda(dir_name)} does not exist')
                        else:
                            save_log(self, command, settings.HOST_NAME, log_file, f'directory is {dir_name}')

                        sleep(.1)
