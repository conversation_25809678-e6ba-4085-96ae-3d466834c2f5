{% comment %}
  __USING_STRIPTAGS__
  we use ...|striptags in this file
  to strip/remove html tags from variables
  in action_menu, in *-url, etc.:
  <mark>***********</mark> -> ***********
{% endcomment %}


{% load tags-filters %}


{% for d_r in db_rows %}
  <tr
    id="htmx_fade_in"

    {% with d_r.3|striptags|is_warning_or_critical_event_type__daemon as warn_or_crit %}
      {% if   warn_or_crit == "is_warning"  %} class="text-warning"
      {% elif warn_or_crit == "is_critical" %} class="text-danger"
      {% endif %}
    {% endwith %}

    {% if forloop.last and not db_rows|length < limit_to_show %}
      hx-include="#dropdown_form"
      hx-get="{% url "daemon-search-url" %}"
      hx-vals='{"page": "{{page_number|add:"1"}}", "date": "{{date_to_show}}", "date-end": "{{date_end_to_show}}"}'
      hx-trigger="intersect once"
      hx-swap="afterend"
      hx-indicator="#{{id_for_htmx_indicator}}"
    {% endif %}
  >
    <td>{{ d_r.0 }}</td>
    <td>{{ d_r.1 }}</td>
    <td>{{ d_r.2 }}</td>
    <td>{{ d_r.3 }}</td>
    <td>{{ d_r.4 }}</td>
    <td>{{ d_r.5 }}</td>
  </tr>

  {% if forloop.last and db_rows|length < limit_to_show %}
    {% include '00-no-results-found.html' with page_number=page_number|add:"1" %}
  {% endif %}
{% empty %}
  {% comment %}first time page loads{% endcomment %}

  {% comment %}
    to make sure this block fires only on first hx request.
    without this, when rows is empty (e.g. because the date by dates section has not been parsed yet)
    it keeps generating this block and, in turn, sending hx requests infinitely
  {% endcomment %}
  {% if page_number == 0 %}

    {% comment %}
      this is a tr with empty cells and its purpose is to send the initial hx query
      so py-0 and d-none are added to make it not visible
    {% endcomment %}
    <tr
      id="htmx_fade_in"
      class="py-0"

      hx-include="#dropdown_form"
      hx-get="{% url "daemon-search-url" %}"
      hx-vals='{"page": "{{page_number|add:"1"}}", "date": "{{date_to_show}}", "date-end": "{{date_end_to_show}}"}'
      hx-trigger="intersect once"
      hx-swap="afterend"
      hx-indicator="#{{id_for_htmx_indicator}}"

      data-to_be_deleted_tr="true"
    >
      <td class="d-none" colspan="{{db_headers|length}}"></td>
    </tr>

  {% else %}
    {% include '00-no-results-found.html' %}
  {% endif %}

{% endfor %}
