{% load static %}
{% load humanize %}
{% load tags-filters %}


<div class="row g-3" id="dropdown_hx_destination">
  <div class="col-12">
    {% include '00-chart-level.html' with chart_height="full_height" %}
  </div>
</div>


{% if from_dropdown %}
  {% comment %}
    NOTE 1. ...
         2. ?version=* is added to prevent caching when loading graph.js
            so that a new version of graphs.js is loaded
            when htmx from dropdown is fired
            (https://stackoverflow.com/a/7413243/14388247)
         3. {% now "YmdGis" %} gives 20231220103114
            (https://docs.djangoproject.com/en/5.1/ref/templates/builtins/#std-templatefilter-date)
  {% endcomment %}
  <script type="module" src="{% static "js/graphs.js" %}?version={% now "YmdGis" %}"></script>

  {% include '00-set-cookies.html' %}
  {% include '00-set-dropdowns.html' %}
{% endif %}
