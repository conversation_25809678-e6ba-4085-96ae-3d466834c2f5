{% load tags-filters %}


<div class="row g-3" id="dropdown_hx_destination">
  {% create_id_for_htmx_indicator "detailed_report" as id_for_htmx_indicator %}
  <div class="col-12">
    <div class="card custom-card shadow">
      <div class="card-header justify-content-between">
        {# left #}
        <div class="card-title">
          {% include '00-header-title.html' with mode="single-date" ttl=date_to_show daysago=date_to_show|days_ago %}
        </div>
        {# right #}
        <div class="d-flex align-items-center">
          {% if newest_on_top %}
            {% include '00-newest-on-top.html' %}
          {% endif %}
          {% if refreshes_allowed and refresh_to_show %}
            {% include '00-refresh.html' with refresh_to_show=refresh_to_show %}
          {% endif %}
          {% if not refresh_to_show and page_has_scrollable_tables %}
            {% include '00-scrollable-table-icon.html' %}
          {% endif %}
        </div>
      </div>
      <div class="card-body">
        <div class="full_height">
          <table class="table table-striped table-bordered table-sm sortable">
            <thead class="{{class_for_thead}}">
              <tr>
                {% for d_h in db_headers %}
                  <th scope="col">{{d_h}}</th>
                {% endfor %}
              </tr>
            </thead>
            <tbody
              {% if refreshes_allowed and refresh_to_show %}
                hx-include="#dropdown_form"
                hx-get="{% url "snort-detailed-report-url" %}"
                hx-vals='{"page": "1"}'
                hx-trigger="load, every {{refresh_to_show}}s"
                hx-swap="afterbegin"
                {% comment %}^^ afterbegin to prepend newest rows to top{% endcomment %}
              {% endif %}
            >
              {% include 'snort/00-detailed-report--rows.html' with id_for_htmx_indicator=id_for_htmx_indicator %}
            </tbody>
          </table>

          {% include '00-htmx-indicator.html' with id_for_htmx_indicator=id_for_htmx_indicator %}
        </div>
      </div>
    </div>
  </div>

  <div class="col-12">
      <div class="card custom-card shadow">
          <div class="card-body">
              {# from checkout.html #}
              <ul class="nav nav-tabs tab-style-2 d-sm-flex d-block border-bottom border-block-end-dashed mb-4" id="myTab1" role="tablist">
                  <li class="nav-item" role="presentation">
                      <button class="nav-link active" id="source_ip_tab" data-bs-toggle="tab"
                          data-bs-target="#source_ip_tab_pane" type="button" role="tab"
                          aria-controls="source_ip_tab" aria-selected="true">Source IP</button>
                  </li>
                  <li class="nav-item" role="presentation">
                      <button class="nav-link" id="destination_ip_tab" data-bs-toggle="tab"
                          data-bs-target="#destination_ip_tab_pane" type="button" role="tab"
                          aria-controls="destination_ip_tab" aria-selected="false">Destination IP</button>
                  </li>
              </ul>

              <div class="tab-content" id="myTabContent">
                  {% create_id_for_htmx_indicator "by-date" "source-ip" date_to_show as id_for_htmx_indicator %}
                  <div class="tab-pane show active border-0 p-0" id="source_ip_tab_pane" role="tabpanel"
                      aria-labelledby="source_ip_tab_pane" tabindex="0">
                      <div class="low_height">
                        <table class="table table-striped table-sm sortable">
                          <thead class="{{class_for_thead}}">
                            <tr>
                              <th scope="col" class="sorttable_numeric"></th>
                              <th scope="col">Source IP</th>
                              <th scope="col">Computer Name</th>
                              <th scope="col">Real Name</th>
                              <th scope="col">MAC Address</th>
                              <th scope="col" class="sorttable_numeric">Count</th>
                              <th scope="col" class="sorttable_numeric">Percent</th>
                            </tr>
                          </thead>
                          <tbody>
                            {% include 'snort/00-tabular-category--rows.html' with section="by-date" by_date_ymd=date_to_show table_slug="source-ip" recent_to_show="None" date_end_to_show="None" page_number=0 id_for_htmx_indicator=id_for_htmx_indicator %}
                          </tbody>
                        </table>

                        {% include '00-htmx-indicator.html' with id_for_htmx_indicator=id_for_htmx_indicator %}
                      </div>
                  </div>
                  {% create_id_for_htmx_indicator "by-date" "destination-ip" date_to_show as id_for_htmx_indicator %}
                  <div class="tab-pane border-0 p-0" id="destination_ip_tab_pane" role="tabpanel"
                      aria-labelledby="destination_ip_tab_pane" tabindex="0">
                      <div class="low_height">
                        <table class="table table-striped table-sm sortable">
                          <thead class="{{class_for_thead}}">
                            <tr>
                              <th scope="col" class="sorttable_numeric"></th>
                              <th scope="col">Destination IP</th>
                              <th scope="col" class="sorttable_numeric">Count</th>
                              <th scope="col" class="sorttable_numeric">Percent</th>
                            </tr>
                          </thead>
                          <tbody>
                            {% include 'snort/00-tabular-category--rows.html' with section="by-date" by_date_ymd=date_to_show table_slug="destination-ip" recent_to_show="None" date_end_to_show="None" page_number=0 id_for_htmx_indicator=id_for_htmx_indicator %}
                          </tbody>
                        </table>

                        {% include '00-htmx-indicator.html' with id_for_htmx_indicator=id_for_htmx_indicator %}
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>
</div>


{% if from_dropdown %}
  {% include '00-set-cookies.html' %}
  {% include '00-set-dropdowns.html' %}
{% endif %}
