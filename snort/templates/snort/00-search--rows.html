{% comment %}
  __USING_STRIPTAGS__
  we use ...|striptags in this file
  to strip/remove html tags from variables
  in action_menu, in *-url, etc.:
  <mark>***********</mark> -> ***********
{% endcomment %}


{% load tags-filters %}


{% for d_r in db_rows %}
  <tr
    id="htmx_fade_in"

    {# is warning/critical priority #}
    {% with d_r.6|striptags|is_warning_or_critical_priority as warn_or_crit %}
      {% if   warn_or_crit == "is_warning"  %} class="text-warning"
      {% elif warn_or_crit == "is_critical" %} class="text-danger"
      {% endif %}
    {% endwith %}

    {% if forloop.last and not db_rows|length < limit_to_show %}
      hx-include="#dropdown_form"
      hx-get="{% url "snort-search-url" %}"
      hx-vals='{"page": "{{page_number|add:"1"}}", "date": "{{date_to_show}}", "date-end": "{{date_end_to_show}}"}'
      hx-trigger="intersect once"
      hx-swap="afterend"
      hx-indicator="#{{id_for_htmx_indicator}}"
    {% endif %}
  >
    <td>{{ d_r.0 }}</td>
    <td>{{ d_r.1 }}</td>

    {# Time #}
    <td>
      <div class="d-flex justify-content-between align-items-center">
        <div>{{ d_r.2 }}</div>
        {% if d_r.2 %}
          {% with d_r.2|striptags as striptagged %}
            <div class="ms-2 action_menu">
              <a href="javascript:void(0);" data-bs-toggle="dropdown" class="btn btn-icon btn-sm btn-link text-muted text-decoration-none">
                <i class="ti ti-dots-vertical"></i>
              </a>
              <ul class="dropdown-menu">
                <li><a target="_blank" class="dropdown-item" href="{% url "dhcp-detailed-report-url" %}?Time={{striptagged}}&date={{date_to_show}}">DHCP Detailed Report</a></li>
                <li><a target="_blank" class="dropdown-item" href="{% url "dns-detailed-report-url" %}?Time={{striptagged}}&date={{date_to_show}}">DNS Detailed Report</a></li>
                <li><a target="_blank" class="dropdown-item" href="{% url "snort-detailed-report-url" %}?Time={{striptagged}}&date={{date_to_show}}">Snort Detailed Report</a></li>
                <li><a target="_blank" class="dropdown-item" href="{% url "filterlog-detailed-report-url" %}?Time={{striptagged}}&date={{date_to_show}}">FilterLog Detailed Report</a></li>
                <li><a target="_blank" class="dropdown-item" href="{% url "squid-detailed-report-url" %}?Time={{striptagged}}&date={{date_to_show}}">Squid Detailed Report</a></li>
              </ul>
            </div>
          {% endwith %}
        {% endif %}
      </div>
    </td>

    <td>{{ d_r.3 }}</td>
    <td>{{ d_r.4 }}</td>
    <td>{{ d_r.5 }}</td>
    <td>{{ d_r.6 }}</td>
    <td>{{ d_r.7 }}</td>

    {# Source IP #}
    <td>
      <div class="d-flex justify-content-between align-items-center">
        <div>{{ d_r.8 }}</div>
        {% if d_r.8 %}
          {% with d_r.8|striptags as striptagged %}
            <div class="d-flex align-items-center">
              <div
                class="text-end small ms-3 d-block d-md-inline computer_name_or_flag"
                hx-get="{% url "base-get-computer-name-or-flag-url" striptagged %}"
                hx-trigger="intersect once"
                hx-swap="innerHTML"
              ></div>
              <div class="ms-2 action_menu">
                <a href="javascript:void(0);" data-bs-toggle="dropdown" class="btn btn-icon btn-sm btn-link text-muted text-decoration-none">
                  <i class="ti ti-dots-vertical"></i>
                </a>
                <ul class="dropdown-menu">
                  <li><a target="_blank" class="dropdown-item" href="{% url "dhcp-detailed-report-url" %}?Source IP={{striptagged}}&date={{date_to_show}}">DHCP Detailed Report</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "dns-detailed-report-url" %}?Source IP={{striptagged}}&date={{date_to_show}}">DNS Detailed Report</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "snort-detailed-report-url" %}?Source IP={{striptagged}}&date={{date_to_show}}">Snort Detailed Report</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "filterlog-detailed-report-url" %}?Source IP={{striptagged}}&date={{date_to_show}}">FilterLog Detailed Report</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "squid-detailed-report-url" %}?Source IP={{striptagged}}&date={{date_to_show}}">Squid Detailed Report</a></li>
                  <hr class="my-1">
                  <li><a target="_blank" class="dropdown-item" href="{% url "snort-visitors-of-ip-url" striptagged %}?date={{date_to_show}}">Snort Visitors of IP</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "general-lookup-url" %}?look-up={{striptagged}}">Lookup</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "malicious-detailed-report-url" "ip" %}?IP={{striptagged}}">Malicious - IP</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "geolocation-detailed-report-url" "ip" %}?IP={{striptagged}}">GeoLocation - IP</a></li>
                </ul>
              </div>
            </div>
          {% endwith %}
        {% endif %}
      </div>
    </td>

    {# Source Port #}
    <td>{{ d_r.9 }}</td>

    {# Destination IP #}
    <td>
      <div class="d-flex justify-content-between align-items-center">
        <div>{{ d_r.10 }}</div>
        {% if d_r.10 %}
          {% with d_r.10|striptags as striptagged %}
            <div class="d-flex align-items-center">
              <div
                class="text-end small ms-3 d-block d-md-inline computer_name_or_flag"
                hx-get="{% url "base-get-computer-name-or-flag-url" striptagged %}"
                hx-trigger="intersect once"
                hx-swap="innerHTML"
              ></div>
              <div class="ms-2 action_menu">
                <a href="javascript:void(0);" data-bs-toggle="dropdown" class="btn btn-icon btn-sm btn-link text-muted text-decoration-none">
                  <i class="ti ti-dots-vertical"></i>
                </a>
                <ul class="dropdown-menu">
                  <li><a target="_blank" class="dropdown-item" href="{% url "dns-detailed-report-url" %}?Source IP={{striptagged}}&date={{date_to_show}}">DNS Detailed Report</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "snort-detailed-report-url" %}?Destination IP={{striptagged}}&date={{date_to_show}}">Snort Detailed Report</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "filterlog-detailed-report-url" %}?Destination IP={{striptagged}}&date={{date_to_show}}">FilterLog Detailed Report</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "squid-detailed-report-url" %}?Destination IP={{striptagged}}&date={{date_to_show}}">Squid Detailed Report</a></li>
                  <hr class="my-1">
                  <li><a target="_blank" class="dropdown-item" href="{% url "snort-visitors-of-ip-url" striptagged %}?date={{date_to_show}}">Snort Visitors of IP</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "general-lookup-url" %}?look-up={{striptagged}}">Lookup</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "malicious-detailed-report-url" "ip" %}?IP={{striptagged}}">Malicious - IP</a></li>
                  <li><a target="_blank" class="dropdown-item" href="{% url "geolocation-detailed-report-url" "ip" %}?IP={{striptagged}}">GeoLocation - IP</a></li>
                </ul>
              </div>
            </div>
          {% endwith %}
        {% endif %}
      </div>
    </td>

    {# Destination Port #}
    <td>{{ d_r.11 }}</td>
  </tr>

  {% if forloop.last and db_rows|length < limit_to_show %}
    {% include '00-no-results-found.html' with page_number=page_number|add:"1" %}
  {% endif %}
{% empty %}
  {% comment %}first time page loads{% endcomment %}

  {% comment %}
    to make sure this block fires only on first hx request.
    without this, when rows is empty (e.g. because the date by dates section has not been parsed yet)
    it keeps generating this block and, in turn, sending hx requests infinitely
  {% endcomment %}
  {% if page_number == 0 %}

    {% comment %}
      this is a tr with empty cells and its purpose is to send the initial hx query
      so py-0 and d-none are added to make it not visible
    {% endcomment %}
    <tr
      id="htmx_fade_in"
      class="py-0"

      hx-include="#dropdown_form"
      hx-get="{% url "snort-search-url" %}"
      hx-vals='{"page": "{{page_number|add:"1"}}", "date": "{{date_to_show}}", "date-end": "{{date_end_to_show}}"}'
      hx-trigger="intersect once"
      hx-swap="afterend"
      hx-indicator="#{{id_for_htmx_indicator}}"

      data-to_be_deleted_tr="true"
    >
      <td class="d-none" colspan="{{db_headers|length}}"></td>
    </tr>

  {% else %}
    {% include '00-no-results-found.html' %}
  {% endif %}

{% endfor %}
