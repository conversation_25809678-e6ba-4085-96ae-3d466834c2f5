{% extends 'dashboard.html' %}
{% load static %}
{% load humanize %}


{% block content %}
<div class="row g-3">
  <div class="col-12">
    <div class="card custom-card shadow">
      {% comment %}
      <div class="card-header justify-content-between">
        {# left #}
        <div class="card-title"></div>
        <div></div>
      </div>
      {% endcomment %}
      <div class="card-body">
        <div class="full_height">
          <table class="table table-striped table-sm">
            <thead class="{{class_for_thead}}">
              <tr>
                <th scope="col" class="sorttable_numeric"></th>
                <th scope="col">Description</th>
                <th scope="col">Classtype</th>
                <th scope="col">Priority</th>
                <th scope="col">Index</th>
                <th scope="col">Indicator</th>
              </tr>
            </thead>

            <tbody>

              {% for k, v in classifications_dict.items %}
                <tr>
                  <td sorttable_customkey="{{ forloop.counter }}">{{ forloop.counter|intcomma }}</td>
                  <td>{{k}}</td>
                  <td>{{v.classtype}}</td>
                  <td>{{v.priority}}</td>
                  <td>{{v.index}}</td>
                  <td>
                    {% if v.index == 1 %}
                      <i class="bi bi-circle-fill critical_indicator"></i>
                    {% elif v.index == 2 %}
                      <i class="bi bi-circle-fill warning_indicator"></i>
                    {% elif v.index == 3 %}
                      <i class="bi bi-circle-fill low_indicator"></i>
                    {% else %}
                      <i class="bi bi-circle-fill very_low_indicator"></i>
                    {% endif %}
                  </td>
                </tr>
              {% endfor %}

            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
