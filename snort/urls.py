from django.urls import path

from . import views


urlpatterns = [
    path('combined-overview/',                   views.combined_overview,       name='snort-combined-overview-url'),
    path('default-classifications/',             views.default_classifications, name='snort-default-classifications-url'),
    path('detailed-activity/',                   views.detailed_activity,       name='snort-detailed-activity-url'),
    path('detailed-report/',                     views.detailed_report,         name='snort-detailed-report-url'),
    path('detailed-trend/<str:table_slug>/',     views.detailed_trend,          name='snort-detailed-trend-url'),
    path('graphical-category/<str:chart_slug>/', views.graphical_category,      name='snort-graphical-category-url'),
    path('graphical-overview/<str:mode>/',       views.graphical_overview,      name='snort-graphical-overview-url'),
    path('level-chart/',                         views.level_chart,             name='snort-level-chart-url'),
    path('overall-activity/',                    views.overall_activity,        name='snort-overall-activity-url'),
    path('overall-trend/',                       views.overall_trend,           name='snort-overall-trend-url'),
    path('search/',                              views.search,                  name='snort-search-url'),
    path('tabular-category/<str:table_slug>/',   views.tabular_category,        name='snort-tabular-category-url'),
    path('tabular-overview/',                    views.tabular_overview,        name='snort-tabular-overview-url'),
    path('visitors-of-ip/<str:destination_ip>/', views.visitors_of_ip,          name='snort-visitors-of-ip-url'),
]
