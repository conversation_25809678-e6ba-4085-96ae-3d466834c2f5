from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

from base.views import serve_protected_file


admin.site.index_title = settings.PROJECT_TITLE  ## left part of tab title
admin.site.site_title  = f'{settings.PROJECT_TITLE} - Admin Page'  ## 1. right part of tab title
                                                                   ## 2. for unfold: under welcome note on login page
admin.site.site_header = f'{settings.PROJECT_TITLE} - Admin Page'  ## dashboard sidebar

urlpatterns = [
    path(f'{settings.SECRET_ADMIN_PREFIX}/admin/', admin.site.urls),

    path('accounts/',      include('accounts.urls')),
    path('daemon/',        include('daemon.urls')),
    path('dhcp/',          include('dhcp.urls')),
    path('dns/',           include('dns.urls')),
    path('filterlog/',     include('filterlog.urls')),
    path('general/',       include('general.urls')),
    path('geolocation/',   include('geolocation.urls')),
    path('malicious/',     include('malicious.urls')),
    path('router/',        include('router.urls')),
    path('routerboard/',   include('routerboard.urls')),
    path('snort/',         include('snort.urls')),
    path('squid/',         include('squid.urls')),
    path('switch/',        include('switch.urls')),
    path('useraudit/',     include('useraudit.urls')),
    path('usernotice/',    include('usernotice.urls')),
    path('userwarning/',   include('userwarning.urls')),
    path('vmware/',        include('vmware.urls')),
    path('vpnserver/',     include('vpnserver.urls')),
    path('windowsserver/', include('windowsserver.urls')),

    path('captcha/',    include('captcha.urls')),  ## for django-simple-captcha package
    path('robots.txt/', include('django_rahavard_robots.urls')),

    path('', include('base.urls')),
]

urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

urlpatterns += [
    ## NOTE no trailing / after <path:path>
    path('protected_uploads/<path:path>', serve_protected_file),
]


handler400 = 'base.handlers.bad_request'
handler403 = 'base.handlers.permission_denied'
handler404 = 'base.handlers.page_not_found'
handler500 = 'base.handlers.server_500'
