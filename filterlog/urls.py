from django.urls import path

from . import views


urlpatterns = [
    path('combined-overview/',                   views.combined_overview,  name='filterlog-combined-overview-url'),
    path('detailed-activity/',                   views.detailed_activity,  name='filterlog-detailed-activity-url'),
    path('detailed-report/',                     views.detailed_report,    name='filterlog-detailed-report-url'),
    path('detailed-trend/<str:table_slug>/',     views.detailed_trend,     name='filterlog-detailed-trend-url'),
    path('graphical-category/<str:chart_slug>/', views.graphical_category, name='filterlog-graphical-category-url'),
    path('graphical-overview/<str:mode>/',       views.graphical_overview, name='filterlog-graphical-overview-url'),
    path('interface-activity/',                  views.interface_activity, name='filterlog-interface-activity-url'),
    path('overall-activity/',                    views.overall_activity,   name='filterlog-overall-activity-url'),
    path('overall-trend/',                       views.overall_trend,      name='filterlog-overall-trend-url'),
    path('search/',                              views.search,             name='filterlog-search-url'),
    path('tabular-category/<str:table_slug>/',   views.tabular_category,   name='filterlog-tabular-category-url'),
    path('tabular-overview/',                    views.tabular_overview,   name='filterlog-tabular-overview-url'),
]
