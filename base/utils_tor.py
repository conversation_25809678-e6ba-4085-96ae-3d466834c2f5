from django.conf import settings

from json import loads
from subprocess import run
from time import sleep

import httpx

from rahavard import (
    colorize,
)

from .utils_constants import (
    HTTP_HEADERS,
    MAX_TRIES,
    TIMEOUTS,
)


def check_is_tor(self):
    '''
    Checks if the current connection is using the Tor network.

    This function attempts to connect to a specified URL through a Tor proxy
    and checks the response to determine if the connection is using Tor.

    Args:
        self: The instance of Command class in the Django custom command.

    Returns:
        tuple: A tuple containing:
            - bool: True if the connection is using Tor, False otherwise.
            - str: The IP address of the connection if available, empty string otherwise.

    Raises:
        httpx.HTTPStatusError: If the HTTP request returns an unsuccessful status code.
        Exception: For any other exceptions that occur during the request.

    Note:
        - The function will retry the connection up to a maximum number of attempts
          defined by MAX_TRIES.is_tor, with a delay of 1 second between attempts.
    '''

    httpx_timeout = httpx.Timeout(TIMEOUTS.is_tor)

    check_successful = False
    check_try = 1

    while not check_successful and check_try <= MAX_TRIES.is_tor:
        try:
            with httpx.Client(proxy=settings.TOR_PROXY) as client:
                response = client.get(
                    url=settings.IS_TOR_URL,
                    headers=HTTP_HEADERS,
                    timeout=httpx_timeout,
                )

                response_text = response.text
                response_dict = loads(response_text)

                check_successful = True

                return (
                    response_dict.get('IsTor', False),  ## True/False
                    response_dict.get('IP', ''),
                )
        except httpx.HTTPStatusError as exc:
            response_status_code = exc.response.status_code
            print(colorize(self, 'error', f'status code: {response_status_code}'))

        except Exception as exc:
            print(colorize(self, 'error', f'{exc!r}'))

        check_try += 1

        sleep(1)

    return (False, '')

def renew_tor_identity() -> None:
    '''
    Sends a SIGHUP signal to the Tor process to force it to reload its configuration and obtain a new identity.

    This function runs the command `sudo killall -HUP tor` using the shell, which requires the user to have
    sudo privileges and the `tor` service to be running.

    Raises:
        subprocess.CalledProcessError: If the command fails to execute.

    Note:
        - https://stackoverflow.com/a/18478178/
    '''

    run('sudo killall -HUP tor', shell=True)
