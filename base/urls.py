from django.urls import path

from . import (
    utils_htmx,
    views,
)


urlpatterns = [

    ## only by hx:
    path('convert-byte/<int:byte>/',                                        utils_htmx.convert_byte,                             name='base-convert-byte-url'),
    path('convert-millisecond/<int:millisecond>/',                          utils_htmx.convert_millisecond,                      name='base-convert-millisecond-url'),
    path('get-computer-name-from-mac-address/<str:mac_address>/<str:ymd>/', utils_htmx.get_computer_name_from_mac_address,       name='base-get-computer-name-from-mac-address-url'),
    path('get-computer-name-or-flag/<str:ip>/',                             utils_htmx.get_computer_name_or_flag,                name='base-get-computer-name-or-flag-url'),
    path('get-computer-name/<str:ip>/',                                     utils_htmx.get_computer_name,                        name='base-get-computer-name-url'),
    path('get-country-of-domain/<str:domain>/',                             utils_htmx.get_country_of_domain,                    name='base-get-country-of-domain-url'),
    path('get-disk-usage-statistics/',                                      utils_htmx.get_disk_usage_statistics,                name='base-get-disk-usage-statistics-url'),
    path('get-firewall-rule-name/<int:tracking_id>/',                       utils_htmx.get_firewall_rule_name,                   name='base-get-firewall-rule-name-url'),
    path('get-interface/<str:real_interface>/',                             utils_htmx.get_interface,                            name='base-get-interface-url'),
    path('get-ip-from-mac-address/<str:mac_address>/<str:ymd>/',            utils_htmx.get_ip_from_mac_address,                  name='base-get-ip-from-mac-address-url'),
    path('get-mac-address/<str:ip>/<str:ymd>/',                             utils_htmx.get_mac_address,                          name='base-get-mac-address-url'),
    path('get-parsed-dates-statistics/',                                    utils_htmx.get_parsed_dates_statistics,              name='base-get-parsed-dates-statistics-url'),
    path('get-real-name-from-mac-address/<str:mac_address>/<str:ymd>/',     utils_htmx.get_real_name_from_mac_address,           name='base-get-real-name-from-mac-address-url'),
    path('get-real-name/<str:ip>/',                                         utils_htmx.get_real_name,                            name='base-get-real-name-url'),
    path('get-windowsserver-category-from-event-id/<str:event_id>/',        utils_htmx.get_windowsserver_category_from_event_id, name='base-get-windowsserver-category-from-event-id-url'),

    path('', views.homepage, name='base-homepage-url'),
]
