CLASSIFICATIONS_DICT = {
    'Attempted Administrator Privilege Gain': {
        'classtype': 'attempted-admin',
        'priority': 'high',
        'index': 1,
    },
    'Attempted User Privilege Gain': {
        'classtype': 'attempted-user',
        'priority': 'high',
        'index': 1,
    },
    'Inappropriate Content was Detected': {
        'classtype': 'inappropriate-content',
        'priority': 'high',
        'index': 1,
    },
    'Potential Corporate Privacy Violation': {
        'classtype': 'policy-violation',
        'priority': 'high',
        'index': 1,
    },
    'Executable code was detected': {
        'classtype': 'shellcode-detect',
        'priority': 'high',
        'index': 1,
    },
    'Successful Administrator Privilege Gain': {
        'classtype': 'successful-admin',
        'priority': 'high',
        'index': 1,
    },
    'Successful User Privilege Gain': {
        'classtype': 'successful-user',
        'priority': 'high',
        'index': 1,
    },
    'A Network Trojan was detected': {
        'classtype': 'trojan-activity',
        'priority': 'high',
        'index': 1,
    },
    'Unsuccessful User Privilege Gain': {
        'classtype': 'unsuccessful-user',
        'priority': 'high',
        'index': 1,
    },
    'Web Application Attack': {
        'classtype': 'web-application-attack',
        'priority': 'high',
        'index': 1,
    },
    'Attempted Denial of Service': {
        'classtype': 'attempted-dos',
        'priority': 'medium',
        'index': 2,
    },
    'Attempted Information Leak': {
        'classtype': 'attempted-recon',
        'priority': 'medium',
        'index': 2,
    },
    'Potentially Bad Traffic': {
        'classtype': 'bad-unknown',
        'priority': 'medium',
        'index': 2,
    },
    'Attempt to login by a default username and password': {
        'classtype': 'default-login-attempt',
        'priority': 'medium',
        'index': 2,
    },
    'Detection of a Denial of Service Attack': {
        'classtype': 'denial-of-service',
        'priority': 'medium',
        'index': 2,
    },
    'Misc Attack': {
        'classtype': 'misc-attack',
        'priority': 'medium',
        'index': 2,
    },
    'Detection of a non-standard protocol or event': {
        'classtype': 'non-standard-protocol',
        'priority': 'medium',
        'index': 2,
    },
    'Decode of an RPC Query': {
        'classtype': 'rpc-portmap-decode',
        'priority': 'medium',
        'index': 2,
    },
    'Denial of Service': {
        'classtype': 'successful-dos',
        'priority': 'medium',
        'index': 2,
    },
    'Large Scale Information Leak': {
        'classtype': 'successful-recon-largescale',
        'priority': 'medium',
        'index': 2,
    },
    'Information Leak': {
        'classtype': 'successful-recon-limited',
        'priority': 'medium',
        'index': 2,
    },
    'A suspicious filename was detected': {
        'classtype': 'suspicious-filename-detect',
        'priority': 'medium',
        'index': 2,
    },
    'An attempted login using a suspicious username was detected': {
        'classtype': 'suspicious-login',
        'priority': 'medium',
        'index': 2,
    },
    'A system call was detected': {
        'classtype': 'system-call-detect',
        'priority': 'medium',
        'index': 2,
    },
    'A client was using an unusual port': {
        'classtype': 'unusual-client-port-connection',
        'priority': 'medium',
        'index': 2,
    },
    'Access to a potentially vulnerable web application': {
        'classtype': 'web-application-activity',
        'priority': 'medium',
        'index': 2,
    },
    'Generic ICMP event': {
        'classtype': 'icmp-event',
        'priority': 'low',
        'index': 3,
    },
    'Misc activity': {
        'classtype': 'misc-activity',
        'priority': 'low',
        'index': 3,
    },
    'Detection of a Network Scan': {
        'classtype': 'network-scan',
        'priority': 'low',
        'index': 3,
    },
    'Not Suspicious Traffic': {
        'classtype': 'not-suspicious',
        'priority': 'low',
        'index': 3,
    },
    'Generic Protocol Command Decode': {
        'classtype': 'protocol-command-decode',
        'priority': 'low',
        'index': 3,
    },
    'A suspicious string was detected': {
        'classtype': 'string-detect',
        'priority': 'low',
        'index': 3,
    },
    'Unknown Traffic': {
        'classtype': 'unknown',
        'priority': 'low',
        'index': 3,
    },
    'A TCP connection was detected': {
        'classtype': 'tcp-connection',
        'priority': 'very low',
        'index': 4,
    },
}

CLASSIFICATIONS__CRITICALS = []
CLASSIFICATIONS__WARNINGS  = []
CLASSIFICATIONS__LOWS      = []
CLASSIFICATIONS__VERY_LOWS = []

for classification, info in CLASSIFICATIONS_DICT.items():
    index = info.get('index', 0)
    if index == 1:
        CLASSIFICATIONS__CRITICALS.append(classification)
    elif index == 2:
        CLASSIFICATIONS__WARNINGS.append(classification)
    elif index == 3:
        CLASSIFICATIONS__LOWS.append(classification)
    elif index == 4:
        CLASSIFICATIONS__VERY_LOWS.append(classification)
