from django.conf import settings

from datetime import timedelta
from os import environ, pathsep
from shutil import which
from types import SimpleNamespace
from typing import Tuple


PICK_AN_OPTION = 'Pick an Option'

## to avoid the error:
## UnicodeDecodeError: 'utf-8' codec can't decode byte 0xa8 in position 5923: invalid start byte
## (https://stackoverflow.com/questions/45529507/unicodedecodeerror-utf-8-codec-cant-decode-byte-0x96-in-position-35-invalid)
## NOTE using encoding='ISO-8859-1' will mess up persian/arabic characters
ACTION_ON_ERROR = 'ignore'

MAX_KEY_LENGTH = 20

MIN_LOG_SIZE = 1024 * 1024 * 20  ## 20MB

PRINT_ENDPOINT = '\r'

CACHE_LIFE_SPAN = int(timedelta(minutes=30).total_seconds())  ## 1800
LRU_CACHE_MAXSIZE = 500  ## default is 128

_shell_path = environ.get('PATH', '')
_usr_local_bin = '/usr/local/bin'
if all([
    _shell_path,
    not settings.DEBUG,
    _usr_local_bin not in _shell_path.split(pathsep),
]):
    _shell_path += f'{pathsep}{_usr_local_bin}'
##
BINARY_PATHS = SimpleNamespace(
    ## possible paths:
    ##   /bin/<name>
    ##   /sbin/<name>
    ##   /usr/bin/<name>
    ##   /usr/local/bin/<name>
    nslookup=which('nslookup', path=_shell_path),
    pgrep=which('pgrep', path=_shell_path),
    service=which('service', path=_shell_path),
    sudo=which('sudo', path=_shell_path),
)
##
## pre-check for missing binaries as a runtime safeguard
for bin_name_, bin_path_ in vars(BINARY_PATHS).items():
    if all([
        bin_path_ is None,
        (bin_name_ == 'service' and not settings.DEBUG)
    ]):
        raise EnvironmentError(f"Required binary '{bin_name_}' not found in system PATH.")

ON_TRUE = ['on', 'true']

LOGICAL_OPERATORS = SimpleNamespace(
    values=['AND', 'OR'],
    default='AND',
    for_field='OR',
)

SEARCH_SIGNS = SimpleNamespace(
    asterisk='*',
    caret='^',
    dollar='$',
    field_separator='||',
)

LIMITS = SimpleNamespace(
    values=[25, 50, 100, 200, 500],
    default=25,
)

TOPS_TO_SHOW = SimpleNamespace(
    values=[5, 10, 15, 20],
    default=10,
)

REFRESHES = SimpleNamespace(
    values=[0, 5, 10, 30, 45, 60],
    default=0,
    min_nonzero=5,
)

LAST_LINES = SimpleNamespace(
    values=[10, 20, 50, 100, 200],
    default=20,
    max=200,
)

RECENTS_TO_SHOW = SimpleNamespace(
    values=['Week', '2 Weeks', '3 Weeks', 'Month', '2 Months', '3 Months', '6 Months', '9 Months', 'Year'],
    default='2 Weeks',
)

TIMEOUTS = SimpleNamespace(
    is_tor=5,

    ## setting to 2 caused
    ## contant ConnectTimeout errors.
    ## 5 seems to be a proper value.
    fetcher=5,
)

MAX_TRIES = SimpleNamespace(
    dl=10,  ## high number because we are using async
    restart=10,
    is_tor=5,
)

SEASONS_LIST: Tuple[str, ...] = (
    'Spring',
    'Summer',
    'Fall',
    'Winter',
)

MONTHS_LIST: Tuple[str, ...] = (
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
)

HTTP_HEADERS = {
    'User-Agent':      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept':          'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',

    ## Do Not Track
    'DNT': '1',

    ## using 'close' can slow down scraping
    ## or mark you as unusual
    'Connection': 'keep-alive',

    ## this is sent by browsers when accessing HTTPS URLs
    ## and signals the browser prefers secure content
    'Upgrade-Insecure-Requests': '1',

    # 'Referer': 'https://example.com/',

    ## Modern browsers send these in real requests.
    ## They're important for bypassing bot protections like Cloudflare, Akamai, etc.
    # 'Sec-Fetch-Dest': 'document',
    # 'Sec-Fetch-Mode': 'navigate',
    # 'Sec-Fetch-Site': 'none',   # or 'same-origin', 'cross-site' depending on context
    # 'Sec-Fetch-User': '?1',
}

LIVE_MONITOR_DB_HEADERS = [
    '',
    'Message',
]
