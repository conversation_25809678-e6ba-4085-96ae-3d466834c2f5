'''
    functions that could not be put inside utils.py
    because they need to import models from models.py
    and that would create import conflicts
'''

# from django.conf import settings
from django.db.models import Q

from functools import lru_cache
from subprocess import run

from MySQLdb import connect

from .utils_classes import (
    DHCPConfig,
    MYSQLConfig,
    VPNServerConfig,
)

from .utils_patterns import (
    NSLOOKUP_NAME_PATTERN,
)

from .utils_constants import (
    BINARY_PATHS,
    LRU_CACHE_MAXSIZE,
)

from .utils import (
    create_name_of_database,
)

from .models import (
    FirewallRule,
    Interface,
    Sensor,
    StaticIP,
    WindowsServer,
)


## __NO_CACHE__
def get_vpnserver_object():
    return WindowsServer.active_objects.filter(
        ## using __iexact in order to ignore casing,
        ## so that .filter() can find object
        ## whose name is VPNSERVER, vpnserver, VPNServer, etc.
        name__iexact=VPNServerConfig.SLUG.value,
    ).first()

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def get_real_name(computer_name):
    ## full message can be:
    ##   ** server can't find *************.in-addr.arpa: NXDOMAIN
    ##   name = c.gtld-servers.net. Authoritative answers can be found from:
    ##   ;; communications error to ***********#53: timed out ;; Got SERVFAIL reply from ***********, trying next server ** server can't find *******.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.0.3.c.d.*******.2.ip6.arpa: SERVFAIL
    ##   empty string (i.e. literal '')
    if not computer_name or \
       "server can't find" in computer_name or \
       'Authoritative answers can be found from' in computer_name or \
       'communications error to' in computer_name or \
       'timed out' in computer_name:

        return ''

    if (obj := StaticIP.active_objects.filter(computer_name__exact=computer_name).first()):
        return obj.real_name

    return ''

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def get_mac_address(ip, ymd):
    try:
        database_name_ = create_name_of_database(DHCPConfig.SLUG.value, ymd)
        with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name_) as conn:
            with conn.cursor() as cur:
                cur.execute('''
                    SELECT `MAC Address` FROM ipmactable
                    WHERE (IP = %s);
                ''', (ip,))
                return cur.fetchone()[0]  ## '2X6E61A9I'
    except Exception:
        return ''

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def get_ip_from_mac_address(mac_address, ymd):
    try:
        database_name_ = create_name_of_database(DHCPConfig.SLUG.value, ymd)
        with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name_) as conn:
            with conn.cursor() as cur:
                cur.execute('''
                    SELECT IP FROM ipmactable
                    WHERE (`MAC Address` = %s);
                ''', (mac_address,))
                return cur.fetchone()[0]  ## '************'
    except Exception:
        return ''

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def get_firewall_rule_name(tracking_id):
    if (obj := FirewallRule.active_objects.filter(tracking_id=tracking_id).first()):
        return obj.name
    return ''

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def get_computer_name(ip=None):
    ## NOTE do NOT remove this if statement because try won't catch it
    ##      and it will make nslookup command just wait for user input
    if not ip:
        return ''

    ## NOTE keep the order of steps

    ## Step 1: get computer_name using static ips
    if (static_ip_object := StaticIP.active_objects.filter(Q(address=ip) | Q(virtual_address=ip)).first()):
        return static_ip_object.computer_name

    ## Step 2: get computer_name using nslookup
    try:
        ## __NEEDS_FULL_PATH__
        ## this shell command uses full path(s)
        ## (i.e. /usr/bin/sudo instead of just sudo)
        ## because it is (also) used/run by django
        ## (when user is visiting pages on browser)
        ## and django has no idea about shell paths
        ## and about where binaries are loacted
        output = run(
            f'{BINARY_PATHS.nslookup} -timeout=1 {ip}',
            shell=True,
            universal_newlines=True,
            capture_output=True,
        )

        if output.returncode == 0:  ## returncode 0 means everything is fine
            # output.stderr.strip() returns ''
            # output.stdout.strip() returns '22***********.in-addr.arpa\tname = sth.sth2.sth3.'
            matches = NSLOOKUP_NAME_PATTERN.findall(output.stdout.strip())
            if not matches:
                return ''
            return matches[0]
            # return output.stdout.strip().split('name = ')[-1]  ## sth.sth2.sth3.
        else:
            # output.stderr.strip() returns ''
            # output.stdout.strip() returns "** server can't find ***********.in-addr.arpa: NXDOMAIN"
            return output.stdout.strip()
    except Exception:
        return ''

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def get_interface(real_interface):
    '''
    Examples:
        >>> get_interface('em1')
        'real interface'

        >>> get_interface('LAN')
        'interface'
    '''

    obj = Interface.active_objects.filter(real_interface=real_interface).first()

    if not obj:
        return ''

    return obj.interface

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def get_ip_of_chosen_sensor_name(name):
    obj = Sensor.active_objects.filter(name=name).first()

    if not obj:
        return None

    return obj.address

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def get_interfaces_dict(chosensensorname):
    return {
        _.real_interface: _.interface
        for _ in Interface.active_objects.filter(sensors__name__in=[chosensensorname])
    } or ''
