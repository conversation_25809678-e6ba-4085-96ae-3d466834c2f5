from functools import lru_cache
from ipaddress import ip_network
from re import match

from .utils_constants import (
    LRU_CACHE_MAXSIZE,
)


@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def is_cidr(string: str) -> bool:
    '''
    Check if the given string is a valid CIDR notation.

    CIDR (Classless Inter-Domain Routing) notation is a compact representation
    of an IP address and its associated network mask.

    Args:
        string (str): The string to be checked.

    Returns:
        bool: True if the string is a valid CIDR notation, False otherwise.

    Examples:
        >>> is_cidr('***********/24')
        True
        >>> is_cidr('10.0.0.0/8')
        True
        >>> is_cidr('**********/12')
        True
        >>> is_cidr('0.0.0.0/0')
        True
        >>> is_cidr('***********/32')
        True
        >>> is_cidr('2001:db8::/32')
        True
        >>> is_cidr('fe80::/10')
        True
        >>> is_cidr('::1/128')
        True
        >>> is_cidr('***********')
        False
        >>> is_cidr('2001:db8::')
        False
        >>> is_cidr('***********/33')
        False
        >>> is_cidr('2001:db8::/129')
        False
        >>> is_cidr('not-an-ip/24')
        False
        >>> is_cidr('***********/-1')
        False
        >>> is_cidr('***********/abc')
        False
        >>> is_cidr('')
        False
        >>> is_cidr(None)
        False
    '''
    ## __HAS_TEST__
    ## __BY_AI__ generated by augment

    if not isinstance(string, str):
        return False

    ## check if string contains a slash followed by digits
    if '/' not in string:
        return False

    ip_part, prefix_part = string.split('/', 1)

    ## validate prefix part is a valid number
    if not prefix_part.isdigit():
        return False

    prefix = int(prefix_part)

    ## check if IP part is valid IPv4 or IPv6
    if is_ip_v4(ip_part):
        ## IPv4 prefix must be between 0 and 32
        return 0 <= prefix <= 32
    elif is_ip_v6(ip_part):
        ## IPv6 prefix must be between 0 and 128
        return 0 <= prefix <= 128

    return False

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def is_ip(string: str) -> bool:
    '''
    Check if the given string is a valid IPv4 or IPv6 address.

    This function uses `is_ip_v4` and `is_ip_v6` to determine if the string is a valid IP address.

    Args:
        string (str): The string to check.

    Returns:
        bool: True if the string is a valid IPv4 or IPv6 address, False otherwise.

    Examples:
        >>> For examples, refer to `is_ip_v4` and `is_ip_v6`
    '''

    return is_ip_v4(string) or is_ip_v6(string)

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def is_ip_v4(string: str) -> bool:
    '''
    Check if the given string is a valid IPv4 address.

    This function validates IPv4 addresses by splitting the string into parts
    and checking that each part is a valid number between 0 and 255.
    The function uses a direct string parsing approach rather than regex
    for better performance.

    Args:
        string (str): The string to be checked.

    Returns:
        bool: True if the string is a valid IPv4 address, False otherwise.

    Examples:
        >>> is_ip_v4('***********')
        True
        >>> is_ip_v4('***************')
        True
        >>> is_ip_v4('256.256.256.256')
        False
        >>> is_ip_v4('192.168.1')
        False
        >>> is_ip_v4('abc.def.ghi.jkl')
        False
        >>> is_ip_v4('')
        False
        >>> is_ip_v4(None)
        False
    '''
    ## __HAS_TEST__
    ## __BY_AI__ generated by augment

    # Use a more efficient approach with direct pattern matching
    # IPv4 pattern: 4 groups of 1-3 digits separated by dots, each group between 0-255
    if not isinstance(string, str):
        return False

    parts = string.split('.')
    if len(parts) != 4:
        return False

    for part in parts:
        if not part.isdigit():
            return False
        num = int(part)
        if num < 0 or num > 255:
            return False

    return True

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def is_ip_v6(string: str) -> bool:
    '''
    Check if the given string is a valid IPv6 address.

    This function validates IPv6 addresses using a regular expression pattern.
    The pattern matches standard IPv6 format including compressed notation
    with double colons.

    Args:
        string (str): The string to be checked.

    Returns:
        bool: True if the string is a valid IPv6 address, False otherwise.

    Examples:
        >>> is_ip_v6('2001:0db8:85a3:0000:0000:8a2e:0370:7334')
        True
        >>> is_ip_v6('2001:db8:85a3::8a2e:370:7334')
        True
        >>> is_ip_v6('::1')
        True
        >>> is_ip_v6('::')
        True
        >>> is_ip_v6('2001:db8::')
        True
        >>> is_ip_v6('fe80::')
        True
        >>> is_ip_v6('fe80::1')
        True
        >>> is_ip_v6('fe80::215:5dff:fe00:402')
        True
        >>> is_ip_v6('fe80::215:5dff:fe00:402%eth0')
        True
        >>> is_ip_v6('::ffff:***********')
        True
        >>> is_ip_v6('64:ff9b::**********')
        True
        >>> is_ip_v6('2001:db8:3:4::**********')
        True
        >>> is_ip_v6('1200::AB00:1234::2552:7777:1313')
        False
        >>> is_ip_v6('***********')
        False
        >>> is_ip_v6('2001:db8::/32')
        False
        >>> is_ip_v6('fe80::/10')
        False
        >>> is_ip_v6('::1/128')
        False
        >>> is_ip_v6('hello')
        False
        >>> is_ip_v6('')
        False
        >>> is_ip_v6(None)
        False
    '''
    ## __HAS_TEST__
    ## __BY_AI__ generated by augment

    if not isinstance(string, str):
        return False

    # More accurate IPv6 regex pattern that handles compressed notation correctly
    return match(
        r'^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|'           # 1:2:3:4:5:6:7:8
        r'([0-9a-fA-F]{1,4}:){1,7}:|'                           # 1::                1:2:3:4:5:6:7::
        r'([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|'           # 1::8               1:2:3:4:5:6::8
        r'([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|'    # 1::7:8             1:2:3:4:5::7:8
        r'([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|'    # 1::6:7:8           1:2:3:4::6:7:8
        r'([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|'    # 1::5:6:7:8         1:2:3::5:6:7:8
        r'([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|'    # 1::4:5:6:7:8       1:2::4:5:6:7:8
        r'[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|'         # 1::3:4:5:6:7:8
        r':((:[0-9a-fA-F]{1,4}){1,7}|:)|'                       # ::2:3:4:5:6:7:8    ::
        r'fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]+|'          # fe80::7:8%eth0     fe80::7:8%1
        r'::(ffff(:0{1,4})?:)?((25[0-5]|(2[0-4]|1?[0-9])?[0-9])\.){3}(25[0-5]|(2[0-4]|1?[0-9])?[0-9])|' # ::***************   ::ffff:***************  ::ffff:0:***************
        r'([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1?[0-9])?[0-9])\.){3}(25[0-5]|(2[0-4]|1?[0-9])?[0-9]))$',  # 2001:db8:3:4::**********  64:ff9b::**********
        string
    ) is not None

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def is_private(string: str) -> bool:
    '''
    Checks if the given IP address (IPv4 or IPv6) is private.

    Args:
        string (str): The IP address to check.

    Returns:
        bool: True if the IP address is private, False otherwise.

    Examples:
        >>> For examples, refer to `is_private_v4` and `is_private_v6`
    '''

    return is_private_v4(string) or is_private_v6(string)

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def is_private_v4(string: str) -> bool:
    '''
    Check if the given IPv4 address is a private address.

    This function determines if the provided IPv4 address string belongs to one of the private address ranges:
    - 10.0.0.0 to **************
    - ********** to **************
    - *********** to ***************

    Args:
        string (str): The IPv4 address as a string.

    Returns:
        bool: True if the address is private, False otherwise.

    Examples:
        >>> is_private_v4('********')
        True
        >>> is_private_v4('**************')
        True
        >>> is_private_v4('**********')
        True
        >>> is_private_v4('***********')
        True
        >>> is_private_v4('**************')
        True
        >>> is_private_v4('***********')
        True
        >>> is_private_v4('***************')
        True
        >>> is_private_v4('127.0.0.1')
        False
        >>> is_private_v4('***********')
        False
        >>> is_private_v4('**********')
        False
        >>> is_private_v4('**********')
        False
        >>> is_private_v4('***********')
        False
        >>> is_private_v4('***********')
        False
        >>> is_private_v4('*******')
        False
        >>> is_private_v4('***********')
        False
        >>> is_private_v4('invalid-ip')
        False
        >>> is_private_v4('')
        False
        >>> is_private_v4(None)
        False
    '''
    ## __HAS_TEST__
    ## __BY_AI__ generated by gemini

    if not is_ip_v4(string):
        return False

    return string.startswith('10.') or \
        string.startswith('192.168.') or \
        (string.startswith('172.') and 16 <= int(string.split('.')[1]) <= 31)

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def is_private_v6(string: str) -> bool:
    '''
    Determines if the given IPv6 address is a private address.

    This function checks if the provided IPv6 address falls within the ranges
    designated for private use, including Unique Local Addresses (ULA) and
    Link-Local Addresses.

    Args:
        string (str): The IPv6 address to check.

    Returns:
        bool: True if the address is private, False otherwise.

    Examples:
        >>> is_private_v6('fc00::1')
        True
        >>> is_private_v6('fd12:3456:789a:1::1')
        True
        >>> is_private_v6('fdff:ffff:ffff:ffff::1')
        True
        >>> is_private_v6('fe80::1')
        True
        >>> is_private_v6('fe80::215:5dff:fe00:402')
        True
        >>> is_private_v6('::1')
        True
        >>> is_private_v6('::ffff:127.0.0.1')
        False
        >>> is_private_v6('2001:db8::1')
        False
        >>> is_private_v6('2001:4860:4860::8888')
        False
        >>> is_private_v6('2606:4700:4700::1111')
        False
        >>> is_private_v6('ff02::1')
        False
        >>> is_private_v6('invalid-ipv6')
        False
        >>> is_private_v6('')
        False
        >>> is_private_v6(None)
        False
    '''
    ## __HAS_TEST__
    ## __BY_AI__ generated by augment

    if not is_ip_v6(string):
        return False

    ## convert to lowercase for consistent comparison
    string = string.lower()

    ## check for loopback address (::1)
    if string == '::1':
        return True

    ## check for Unique Local Addresses (ULA) - fc00::/7
    if string.startswith('fc') or string.startswith('fd'):
        return True

    ## check for Link-Local Addresses - fe80::/10
    if string.startswith('fe80:'):
        return True

    return False

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
def get_hosts(ip: str) -> list[str]:
    '''
    Given an IP address or network, returns a list of all possible host addresses within that network.

    Args:
        ip (str): A string representing an IP address or network in CIDR notation.

    Returns:
        list: A list of strings, each representing a host IP address within the given network.
              Returns an empty list if the input is invalid or an error occurs.

    Examples:
        >>> get_hosts('***********/30')
        ['***********', '***********']
        >>> get_hosts('********/32')
        ['********']
        >>> get_hosts('***********/29')
        ['***********', '***********', '***********', '***********', '***********', '***********']
        >>> get_hosts('2001:db8::/126')
        ['2001:db8::1', '2001:db8::2', '2001:db8::3']
        >>> get_hosts('***********/33')
        []
        >>> get_hosts('invalid_ip')
        []
    '''
    ## __HAS_TEST__
    ## __BY_AI__ optimized by copilot, gemini

    try:
        return list(map(str, ip_network(ip).hosts()))
    except Exception:
        return []
