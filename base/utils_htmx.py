'''
    functions used/called by htmx requests
'''

from django.contrib.auth.decorators import login_required
from django.http import HttpResponse
from django.shortcuts import render
from django.views.decorators.cache import cache_page

from MySQLdb import connect

from rahavard import (
    comes_from_htmx,
    convert_byte as convert_byte_,
    convert_millisecond as convert_millisecond_,
)

from .utils_classes import (
    GeoLocationConfig,
    MYSQLConfig,
    WindowsServerConfig,
)

from .utils_constants import (
    CACHE_LIFE_SPAN,
)

from base.utils_ip import (
    is_ip,
    is_private,
)

from .utils import (
    create_name_of_database,
    read_statistics_file,
    strip_protocol_and_path_from_url,
    unquote_domain,
)

from .utils_extra import (
    get_computer_name as get_computer_name_,
    get_firewall_rule_name as get_firewall_rule_name_,
    get_interface as get_interface_,
    get_ip_from_mac_address as get_ip_from_mac_address_,
    get_mac_address as get_mac_address_,
    get_real_name as get_real_name_,
)

from .models import (
    StaticIP,
)


@cache_page(CACHE_LIFE_SPAN)
def convert_byte(request, byte):
    return HttpResponse(f'<span id="htmx_fade_in">{convert_byte_(byte)} <span class="text-muted small">({byte:,} bytes)</span></span>')

@cache_page(CACHE_LIFE_SPAN)
def convert_millisecond(request, millisecond):
    return HttpResponse(f'<span id="htmx_fade_in">{convert_millisecond_(millisecond)} <span class="text-muted small">({millisecond:,} ms)</span></span>')

@login_required
@cache_page(CACHE_LIFE_SPAN)
def get_real_name(request, ip):
    computer_name = get_computer_name_(ip)
    return HttpResponse(f'<span id="htmx_fade_in">{get_real_name_(computer_name)}</span>')

@login_required
@cache_page(CACHE_LIFE_SPAN)
def get_mac_address(request, ip, ymd):
    return HttpResponse(f'<span id="htmx_fade_in">{get_mac_address_(ip, ymd)}</span>')

@login_required
@cache_page(CACHE_LIFE_SPAN)
def get_ip_from_mac_address(request, mac_address, ymd):
    return HttpResponse(f'<span id="htmx_fade_in">{get_ip_from_mac_address_(mac_address, ymd)}</span>')

@login_required
@cache_page(CACHE_LIFE_SPAN)
def get_computer_name_from_mac_address(request, mac_address, ymd):
    ip = get_ip_from_mac_address_(mac_address, ymd)
    return HttpResponse(f'<span id="htmx_fade_in">{get_computer_name_(ip)}</span>')

@login_required
@cache_page(CACHE_LIFE_SPAN)
def get_real_name_from_mac_address(request, mac_address, ymd):
    ip = get_ip_from_mac_address_(mac_address, ymd)
    computer_name = get_computer_name_(ip)
    return HttpResponse(f'<span id="htmx_fade_in">{get_real_name_(computer_name)}</span>')

@login_required
@cache_page(CACHE_LIFE_SPAN)
def get_computer_name(request, ip):
    return HttpResponse(f'<span id="htmx_fade_in">{get_computer_name_(ip)}</span>')

@login_required
@cache_page(CACHE_LIFE_SPAN)
def get_firewall_rule_name(request, tracking_id):
    '''
        gets tracking id (e.g. 1000016319)
        returns name (e.g. APAC1200)
    '''
    return HttpResponse(f'''
        <span
          id="htmx_fade_in"
          class="badge border-0 bg-transparent fs-11 bg-outline-info"
        >{get_firewall_rule_name_(tracking_id)}</span>
    ''')

@login_required
@cache_page(CACHE_LIFE_SPAN)
def get_country_of_domain(request, domain):
    domain = unquote_domain(domain)
    domain = strip_protocol_and_path_from_url(domain)

    ## __DOT_LOCAL__
    if domain.endswith('.local') or not domain:
        return HttpResponse('')

    database_name = create_name_of_database(GeoLocationConfig.SLUG.value)
    try:
        with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:
                cur.execute(f'''
                    SELECT Country, `Country Code`
                    FROM {GeoLocationConfig.get_table_name(geo_mode='domain')}
                    WHERE (Domain = %s)
                ;''', (domain,))
                country, country_code = cur.fetchone()
    except Exception:
        country, country_code = ('', '')

    if country:
        country_tooltip = f'data-bs-toggle="tooltip" data-bs-placement="top" title="{country}"'
    else:
        country_tooltip = ''

    badge__country_code = ''

    if country or country_code:
        badge__country_code = f'''
            <span
              class="badge border-0 bg-transparent fs-11 bg-outline-info text-teal"
              {country_tooltip}
            >{country_code}</span>
        '''

    return HttpResponse(f'''
        <span
          class="text-nowrap"
          id="htmx_fade_in"
        >
          {badge__country_code}
        </span>
    ''')

@login_required
@cache_page(CACHE_LIFE_SPAN)
def get_computer_name_or_flag(request, ip):
    '''
    if ip is not an ip
       returns ''

    if ip is private
       returns computer name
       e.g. someadds.abc.local

    if ip is public
       returns domain, computer name, country_code, flag
       e.g. example.com mydomain.mydomain.ir - IRN <flag img>
    '''

    ## is not an ip (sometimes ip is -)
    if not is_ip(ip):
        return HttpResponse('')

    ## is private ip
    if is_private(ip):
        return HttpResponse(f'''
            <span
              id="htmx_fade_in"
              class="badge border-0 bg-transparent fs-11 bg-outline-info"
            >{get_computer_name_(ip)}</span>
        ''')


    ## is public ip

    ## step 1: get Country, Country Code, Flag Emoji, Domain
    database_name = create_name_of_database(GeoLocationConfig.SLUG.value)
    try:
        with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
            with conn.cursor() as cur:
                cur.execute(f'''
                    SELECT Country, `Country Code`, `Flag Emoji`, Domain
                    FROM {GeoLocationConfig.get_table_name(geo_mode='ip')}
                    WHERE (IP = %s)
                ;''', (ip,))
                country, country_code, flag_emoji, domain = cur.fetchone()
    except Exception:
        country, country_code, flag_emoji, domain = ('', '', '', '')


    ## step 2: get computer_name if any
    static_ip_object = StaticIP.active_objects.filter(virtual_address=ip).first()
    if static_ip_object:
        computer_name = static_ip_object.computer_name
    else:
        computer_name = ''


    if country:
        country_tooltip = f'data-bs-toggle="tooltip" data-bs-placement="top" title="{country}"'
    else:
        country_tooltip = ''


    badge__domain = ''
    badge__computer_name_and_or_country_code = ''
    badge__flag = ''

    if domain:
        badge__domain = f'''
            <span
              class="badge border-0 bg-transparent fs-11 bg-outline-info text-gray"
            >{domain}</span>
        '''

    if computer_name or country_code:
        badge__computer_name_and_or_country_code = f'''
            <span
              class="badge border-0 bg-transparent fs-11 bg-outline-info text-teal"
              {country_tooltip}
            >{" - ".join([computer_name, country_code]).strip(" - ")}</span>
        '''

    if flag_emoji:
        badge__flag = f'''
            <span
              class="rounded rounded-circle ml-1"
              {country_tooltip}
            >{flag_emoji}</span>
        '''

    return HttpResponse(f'''
        <span
          class="text-nowrap"
          id="htmx_fade_in"
        >
          {badge__domain}
          {badge__computer_name_and_or_country_code}
          {badge__flag}
        </span>
    ''')

@login_required
@cache_page(CACHE_LIFE_SPAN)
def get_interface(request, real_interface):
    '''
        gets real interface (e.g. em1)
        returns interface (e.g. LAN)
    '''
    return HttpResponse(f'''
        <span
          id="htmx_fade_in"
          class="badge border-0 bg-transparent fs-11 bg-outline-info"
        >{get_interface_(real_interface)}</span>
    ''')

@login_required
@cache_page(CACHE_LIFE_SPAN)
def get_windowsserver_category_from_event_id(request, event_id: str):
    '''
        gets event id (e.g. 4626)
        returns windows server category (e.g. Logon/Logoff)
    '''
    return HttpResponse(f'''
        <span
          id="htmx_fade_in"
          class="badge border-0 bg-transparent fs-11 bg-outline-info"
        >{WindowsServerConfig.get_category_from_event_id(str(event_id))}</span>
    ''')

@login_required
def get_parsed_dates_statistics(request):
    if comes_from_htmx(request):
        if request.user.is_superuser:
            parsed_dates_statistics = read_statistics_file('parsed-dates')
        else:
            parsed_dates_statistics = {}

        return render(
            request,
            'base/00-parsed-dates--li.html',
            context={
                'main_title': 'Parsed Dates',
                'parsed_dates_statistics': parsed_dates_statistics,
            }
        )

    return HttpResponse('')

@login_required
def get_disk_usage_statistics(request):
    if comes_from_htmx(request):
        if request.user.is_superuser:
            disk_usage_statistics = read_statistics_file('disk-usage')
        else:
            disk_usage_statistics = {}

        return render(
            request,
            'base/00-disk-usage-statistics--li.html',
            context={
                'main_title': 'Disk Usage Statistics',
                'disk_usage_statistics': disk_usage_statistics,
            }
        )

    return HttpResponse('')
