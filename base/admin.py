from django.contrib import admin

from rahavard import (
    ADMIN_PY__LIST_DISPLAY_LINKS,
    ADMIN_PY__LIST_FILTER,
    ADMIN_PY__READONLY_FIELDS,

    make_active,
    make_inactive,
)

from unfold.admin import ModelAdmin as unfoldModelAdmin

from .models import (
    FireHOL,
    FirewallRule,
    Gateway,
    Interface,
    Module,
    PublicIP,
    Router,
    RouterBoard,
    Sensor,
    StaticIP,
    Switch,
    VMware,
    WindowsServer,
)


@admin.register(FireHOL)
class FireHOLAdmin(unfoldModelAdmin):
    ## determine the order of fields in admin interface
    fieldsets = (
        (None, {
            'fields': (
                'name',
                'url',
            )
        }),

        ## BaseModel fields
        ('Extra', {
            'fields': (
                'description',
                'active',
                'short_uuid',
                'created',
                'updated',

                ## specific to this project
                ## ...
            )
        }),
    )

    list_display = ['id', 'short_uuid', 'name', 'url', 'description', 'active']
    search_fields = [
        'name',
        'url',
        'description',
        'active',
        'short_uuid',
        'created',
        'updated',
    ]
    list_display_links = ADMIN_PY__LIST_DISPLAY_LINKS
    readonly_fields    = ADMIN_PY__READONLY_FIELDS
    list_filter        = ADMIN_PY__LIST_FILTER
    # list_editable    = []
    actions            = [make_active, make_inactive]
    ordering           = ['-id']  ## newest on top


@admin.register(FirewallRule)
class FirewallRuleAdmin(unfoldModelAdmin):
    ## determine the order of fields in admin interface
    fieldsets = (
        (None, {
            'fields': (
                'tracking_id',
                'name',
                'interface',
            )
        }),

        ## BaseModel fields
        ('Extra', {
            'fields': (
                'description',
                'active',
                'short_uuid',
                'created',
                'updated',

                ## specific to this project
                ## ...
            )
        }),
    )

    list_display = ['id', 'short_uuid', 'tracking_id', 'name', 'interface__', 'description', 'active']
    search_fields = [
        'tracking_id',
        'name',
        'interface',  ## ForeignKey
        'description',
        'active',
        'short_uuid',
        'created',
        'updated',
    ]
    list_display_links = ADMIN_PY__LIST_DISPLAY_LINKS
    readonly_fields    = ADMIN_PY__READONLY_FIELDS
    list_filter        = ADMIN_PY__LIST_FILTER
    # list_editable    = []
    actions            = [make_active, make_inactive]
    ordering           = ['-id']  ## newest on top


@admin.register(Gateway)
class GatewayAdmin(unfoldModelAdmin):
    ## determine the order of fields in admin interface
    fieldsets = (
        (None, {
            'fields': (
                'name',
            )
        }),

        ## AddressMixin field
        (None, {
            'fields': (
                'address',
            )
        }),

        ## BaseModel fields
        ('Extra', {
            'fields': (
                'description',
                'active',
                'short_uuid',
                'created',
                'updated',

                ## specific to this project
                ## ...
            )
        }),
    )

    list_display = ['id', 'short_uuid', 'name', 'address', 'description', 'active']
    search_fields = [
        'name',
        'address',
        'description',
        'active',
        'short_uuid',
        'created',
        'updated',
    ]
    list_display_links = ADMIN_PY__LIST_DISPLAY_LINKS
    readonly_fields    = ADMIN_PY__READONLY_FIELDS
    list_filter        = ADMIN_PY__LIST_FILTER
    # list_editable    = []
    actions            = [make_active, make_inactive]
    ordering           = ['-id']  ## newest on top


@admin.register(Interface)
class InterfaceAdmin(unfoldModelAdmin):
    ## determine the order of fields in admin interface
    fieldsets = (
        (None, {
            'fields': (
                'real_interface',
                'interface',
                'sensors',
            )
        }),

        ## BaseModel fields
        ('Extra', {
            'fields': (
                'description',
                'active',
                'short_uuid',
                'created',
                'updated',

                ## specific to this project
                ## ...
            )
        }),
    )

    list_display = ['id', 'short_uuid', 'real_interface', 'interface', 'description', 'sensors__', 'active']
    search_fields = [
        'real_interface',
        'interface',
        # 'sensors',  ## is ManyToManyField
        'description',
        'active',
        'short_uuid',
        'created',
        'updated',
    ]
    list_display_links = ADMIN_PY__LIST_DISPLAY_LINKS
    readonly_fields    = ADMIN_PY__READONLY_FIELDS
    list_filter        = ADMIN_PY__LIST_FILTER
    # list_editable    = []
    actions            = [make_active, make_inactive]
    ordering           = ['-id']  ## newest on top


@admin.register(Module)
class ModuleAdmin(unfoldModelAdmin):
    ## determine the order of fields in admin interface
    fieldsets = (
        ## NameMixin field
        (None, {
            'fields': (
                'name',
            )
        }),

        # (None, {
        #     'fields': (
        #     )
        # }),

        ## BaseModel fields
        ('Extra', {
            'fields': (
                'description',
                'active',
                'short_uuid',
                'created',
                'updated',

                ## specific to this project
                ## ...
            )
        }),
    )

    list_display = ['id', 'short_uuid', 'name', 'description', 'active']
    search_fields = [
        'name',
        'description',
        'active',
        'short_uuid',
        'created',
        'updated',
    ]
    list_display_links = ADMIN_PY__LIST_DISPLAY_LINKS
    readonly_fields    = ADMIN_PY__READONLY_FIELDS
    list_filter        = ADMIN_PY__LIST_FILTER
    # list_editable    = []
    actions            = [make_active, make_inactive]
    ordering           = ['-id']  ## newest on top


@admin.register(PublicIP)
class PublicIPAdmin(unfoldModelAdmin):
    ## determine the order of fields in admin interface
    fieldsets = (
        (None, {
            'fields': (
                'address',
            )
        }),

        ## BaseModel fields
        ('Extra', {
            'fields': (
                'description',
                'active',
                'short_uuid',
                'created',
                'updated',

                ## specific to this project
                ## ...
            )
        }),
    )

    list_display = ['id', 'short_uuid', 'address', 'description', 'active']
    search_fields = [
        'address',
        'description',
        'active',
        'short_uuid',
        'created',
        'updated',
    ]
    list_display_links = ADMIN_PY__LIST_DISPLAY_LINKS
    readonly_fields    = ADMIN_PY__READONLY_FIELDS
    list_filter        = ADMIN_PY__LIST_FILTER
    # list_editable    = []
    actions            = [make_active, make_inactive]
    ordering           = ['-id']  ## newest on top


@admin.register(Router)
class RouterAdmin(unfoldModelAdmin):
    ## determine the order of fields in admin interface
    fieldsets = (
        ## NameMixin field
        (None, {
            'fields': (
                'name',
            )
        }),

        ## AddressMixin field
        (None, {
            'fields': (
                'address',
            )
        }),

        ## BaseModel fields
        ('Extra', {
            'fields': (
                'description',
                'active',
                'short_uuid',
                'created',
                'updated',

                ## specific to this project
                ## ...
            )
        }),
    )

    list_display = ['id', 'short_uuid', 'name', 'address', 'description', 'active']
    search_fields = [
        'name',
        'address',
        'description',
        'active',
        'short_uuid',
        'created',
        'updated',
    ]
    list_display_links = ADMIN_PY__LIST_DISPLAY_LINKS
    readonly_fields    = ADMIN_PY__READONLY_FIELDS
    list_filter        = ADMIN_PY__LIST_FILTER
    # list_editable    = []
    actions            = [make_active, make_inactive]
    ordering           = ['-id']  ## newest on top


@admin.register(RouterBoard)
class RouterBoardAdmin(unfoldModelAdmin):
    ## determine the order of fields in admin interface
    fieldsets = (
        ## NameMixin field
        (None, {
            'fields': (
                'name',
            )
        }),

        ## AddressMixin field
        (None, {
            'fields': (
                'address',
            )
        }),

        ## BaseModel fields
        ('Extra', {
            'fields': (
                'description',
                'active',
                'short_uuid',
                'created',
                'updated',

                ## specific to this project
                ## ...
            )
        }),
    )

    list_display = ['id', 'short_uuid', 'name', 'address', 'description', 'active']
    search_fields = [
        'name',
        'address',
        'description',
        'active',
        'short_uuid',
        'created',
        'updated',
    ]
    list_display_links = ADMIN_PY__LIST_DISPLAY_LINKS
    readonly_fields    = ADMIN_PY__READONLY_FIELDS
    list_filter        = ADMIN_PY__LIST_FILTER
    # list_editable    = []
    actions            = [make_active, make_inactive]
    ordering           = ['-id']  ## newest on top


@admin.register(Sensor)
class SensorAdmin(unfoldModelAdmin):
    ## determine the order of fields in admin interface
    fieldsets = (
        ## NameMixin field
        (None, {
            'fields': (
                'name',
            )
        }),

        ## AddressMixin field
        (None, {
            'fields': (
                'address',
            )
        }),

        ## BaseModel fields
        ('Extra', {
            'fields': (
                'description',
                'active',
                'short_uuid',
                'created',
                'updated',

                ## specific to this project
                ## ...
            )
        }),
    )

    list_display = ['id', 'short_uuid', 'name', 'address', 'description', 'active']
    search_fields = [
        'name',
        'address',
        'description',
        'active',
        'short_uuid',
        'created',
        'updated',
    ]
    list_display_links = ADMIN_PY__LIST_DISPLAY_LINKS
    readonly_fields    = ADMIN_PY__READONLY_FIELDS
    list_filter        = ADMIN_PY__LIST_FILTER
    # list_editable    = []
    actions            = [make_active, make_inactive]
    ordering           = ['-id']  ## newest on top


@admin.register(StaticIP)
class StaticIPAdmin(unfoldModelAdmin):
    ## determine the order of fields in admin interface
    fieldsets = (
        (None, {
            'fields': (
                'computer_name',
                'real_name',
            )
        }),

        ## AddressMixin field
        (None, {
            'fields': (
                'address',
            )
        }),

        ## AddressMixin field
        (None, {
            'fields': (
                'virtual_address',
            )
        }),

        ## BaseModel fields
        ('Extra', {
            'fields': (
                'description',
                'active',
                'short_uuid',
                'created',
                'updated',

                ## specific to this project
                ## ...
            )
        }),
    )

    list_display = ['id', 'short_uuid', 'computer_name', 'real_name', 'address', 'virtual_address', 'description', 'active']
    search_fields = [
        'computer_name',
        'real_name',
        'address',
        'virtual_address',
        'description',
        'active',
        'short_uuid',
        'created',
        'updated',
    ]
    list_display_links = ADMIN_PY__LIST_DISPLAY_LINKS
    readonly_fields    = ADMIN_PY__READONLY_FIELDS
    list_filter        = ADMIN_PY__LIST_FILTER
    # list_editable    = []
    actions            = [make_active, make_inactive]
    ordering           = ['-id']  ## newest on top


@admin.register(Switch)
class SwitchAdmin(unfoldModelAdmin):
    ## determine the order of fields in admin interface
    fieldsets = (
        ## NameMixin field
        (None, {
            'fields': (
                'name',
            )
        }),

        ## AddressMixin field
        (None, {
            'fields': (
                'address',
            )
        }),

        ## BaseModel fields
        ('Extra', {
            'fields': (
                'description',
                'active',
                'short_uuid',
                'created',
                'updated',

                ## specific to this project
                ## ...
            )
        }),
    )

    list_display = ['id', 'short_uuid', 'name', 'address', 'description', 'active']
    search_fields = [
        'name',
        'address',
        'description',
        'active',
        'short_uuid',
        'created',
        'updated',
    ]
    list_display_links = ADMIN_PY__LIST_DISPLAY_LINKS
    readonly_fields    = ADMIN_PY__READONLY_FIELDS
    list_filter        = ADMIN_PY__LIST_FILTER
    # list_editable    = []
    actions            = [make_active, make_inactive]
    ordering           = ['-id']  ## newest on top


@admin.register(VMware)
class VMwareAdmin(unfoldModelAdmin):
    ## determine the order of fields in admin interface
    fieldsets = (
        ## NameMixin field
        (None, {
            'fields': (
                'name',
            )
        }),

        ## AddressMixin field
        (None, {
            'fields': (
                'address',
            )
        }),

        ## BaseModel fields
        ('Extra', {
            'fields': (
                'description',
                'active',
                'short_uuid',
                'created',
                'updated',

                ## specific to this project
                ## ...
            )
        }),
    )

    list_display = ['id', 'short_uuid', 'name', 'address', 'description', 'active']
    search_fields = [
        'name',
        'address',
        'description',
        'active',
        'short_uuid',
        'created',
        'updated',
    ]
    list_display_links = ADMIN_PY__LIST_DISPLAY_LINKS
    readonly_fields    = ADMIN_PY__READONLY_FIELDS
    list_filter        = ADMIN_PY__LIST_FILTER
    # list_editable    = []
    actions            = [make_active, make_inactive]
    ordering           = ['-id']  ## newest on top


@admin.register(WindowsServer)
class WindowsServerAdmin(unfoldModelAdmin):
    ## determine the order of fields in admin interface
    fieldsets = (
        ## NameMixin field
        (None, {
            'fields': (
                'name',
            )
        }),

        ## AddressMixin field
        (None, {
            'fields': (
                'address',
            )
        }),

        ## BaseModel fields
        ('Extra', {
            'fields': (
                'description',
                'active',
                'short_uuid',
                'created',
                'updated',

                ## specific to this project
                ## ...
            )
        }),
    )

    list_display = ['id', 'short_uuid', 'name', 'address', 'description', 'active']
    search_fields = [
        'name',
        'address',
        'description',
        'active',
        'short_uuid',
        'created',
        'updated',
    ]
    list_display_links = ADMIN_PY__LIST_DISPLAY_LINKS
    readonly_fields    = ADMIN_PY__READONLY_FIELDS
    list_filter        = ADMIN_PY__LIST_FILTER
    # list_editable    = []
    actions            = [make_active, make_inactive]
    ordering           = ['-id']  ## newest on top
