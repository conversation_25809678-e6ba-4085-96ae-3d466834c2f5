from django.core.exceptions import ValidationError
from django.db import models

from re import match

from natsort import natsorted
from rahavard import (
    create_short_uuid,
    sort_dict,
)


MODELS_MANAGER = models.Manager()


# def validate_is_ip(value):
#     from .utils_ip import is_ip
#     if not is_ip(value):
#         raise ValidationError(f"'{value}' is not a valid IP")


def validate_has_allowed_chars(value):
    '''
    allowed characters:
      - `a-z`
      - `A-Z`
      - `0-9`
      - `-` (hyphen)

    __DATABASE_YMD_PATTERN__

    The reason why _ is not allowed for `name` field:
      - when creating name of database
        in base/utils.py@create_name_of_database,
        we - -> _ in object_name and ymd
      - when extracting path from name of database
        in base/utils.py@get_directory_path_from_name_of_database,
        we _ -> -
      - so if the original name has _
        we will end up with incorrect values
    '''

    if match(r'^[a-zA-Z0-9-]+$', value) is None:
        raise ValidationError(f'"{value}" did not match allowed characters')


class MethodsMixin:
    @property
    def model_name(self):
        return self.__class__.__name__

    @classmethod
    def get_list_of_names(cls):
        objects = cls.active_objects.all()

        if not objects:
            return []

        return natsorted(objects.values_list('name', flat=True))
        ## ['Sensor-1', 'Sensor-2', ...]

    @classmethod
    def get_list_of_addresses(cls):
        objects = cls.active_objects.all()

        if not objects:
            return []

        return natsorted(objects.values_list('address', flat=True))
        ## ['***********', '***********', ...]

    @classmethod
    def get_list_of_names_and_addresses(cls):
        objects = cls.active_objects.all()

        if not objects:
            return []

        return natsorted([
            item for tup
            in dict(objects.values_list('name', 'address')).items()
            for item in tup
        ])
        ## ['***********', 'Sensor-1', '***********', 'Sensor-2', ...]

    @classmethod
    def get_dict_of_addresses_and_names(cls):
        objects = cls.active_objects.all()

        if not objects:
            return {}

        return sort_dict(
            dict(objects.values_list('address', 'name')),
            based_on='key',
            reverse=False,
        )
        ## {
        ##     '***********': 'Sensor-1',
        ##     '***********': 'Sensor-2',
        ##     ...,
        ## }


class GetActiveObjects(models.Manager):
    def get_queryset(self):
        ## self.model.__name__ refers to the name of the model class
        ## that this custom manager is attached to, e.g. 'User', 'Router', etc.
        if self.model.__name__ == 'User':
            filter_field = 'is_active'
        else:
            filter_field = 'active'

        return super().get_queryset().filter(**{filter_field: True})


class NameMixin(models.Model):
    name = models.CharField(
        max_length=50,
        unique=True,
        validators=[validate_has_allowed_chars],
        help_text='e.g. Snort, Switch-1, etc. Allowed characters: letters (AaBbCc...), numbers (012...) and hyphen (-)',
        db_index=True,
    )

    class Meta:
        abstract = True


class AddressMixin(models.Model):
    address = models.GenericIPAddressField(
        protocol='IPv4',
        unique=True,
        blank=True,
        null=True,
        help_text='e.g. ***********',
        db_index=True,
    )

    class Meta:
        abstract = True


class VirtualAddressMixin(models.Model):
    virtual_address = models.GenericIPAddressField(
        protocol='IPv4',
        unique=True,
        blank=True,
        null=True,
        help_text='e.g. ***********',
        verbose_name='Virtual Address',
        db_index=True,
    )

    class Meta:
        abstract = True


class BaseModel(models.Model, MethodsMixin):
    description = models.TextField(null=True, blank=True)
    active      = models.BooleanField(default=True, db_index=True)
    short_uuid  = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID', db_index=True)
    created     = models.DateTimeField(auto_now_add=True)
    updated     = models.DateTimeField(auto_now=True)

    ## specific to this project
    ## ...

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    class Meta:
        abstract = True


class FireHOL(BaseModel):
    class Meta(BaseModel.Meta):
        verbose_name_plural = 'FireHOLs'

    name = models.CharField(max_length=50, unique=True, help_text='e.g. GitHub Source 1, etc.')
    url  = models.URLField(max_length=200, unique=True, help_text='e.g. https://example.com/hosts.txt', verbose_name='URL')

    def __str__(self):
        return f'#{self.id} - {self.short_uuid} - {self.name}'

    def __repr__(self):
        return f'<{self.model_name}: #{self.id} - {self.short_uuid} - {self.name}>'


class FirewallRule(BaseModel):
    class Meta(BaseModel.Meta):
        verbose_name_plural = 'Firewall Rules'

    tracking_id = models.IntegerField(unique=True, verbose_name='Tracking ID', db_index=True)

    ## do NOT add unique=True
    name = models.CharField(max_length=20, help_text='e.g. APAC1200')

    ## do NOT add unique=True
    interface = models.ForeignKey('Interface', null=True, related_name='firewallrule_interface_rel', on_delete=models.SET_NULL)  ## have to use null=True because on_delete=models.SET_NULL

    ## used in admin.py
    ##      in overview.html
    @property
    def interface__(self):
        return self.interface.interface

    def __str__(self):
        return f'#{self.id} - {self.short_uuid} - {self.name}'

    def __repr__(self):
        return f'<{self.model_name}: #{self.id} - {self.short_uuid} - {self.name}>'


class Gateway(AddressMixin, BaseModel):
    name = models.CharField(max_length=50, unique=True, help_text='e.g. GW One, GW-One, etc.')

    def __str__(self):
        return f'#{self.id} - {self.short_uuid} - {self.name}'

    def __repr__(self):
        return f'<{self.model_name}: #{self.id} - {self.short_uuid} - {self.name}>'


class Interface(BaseModel):
    ## do NOT add unique=True
    real_interface = models.CharField(max_length=50, help_text='e.g. em1', verbose_name='Real Interface', db_index=True)

    ## do NOT add unique=True
    interface = models.CharField(max_length=50, help_text='e.g. LAN')

    ## an Interface instance can have multiple sensors
    sensors = models.ManyToManyField('Sensor', blank=True)  ## null=True removed because of this warning: (fields.W340) null has no effect on ManyToManyField

    def __str__(self):
        return f'#{self.id} - {self.short_uuid} - {self.real_interface} - {self.interface}'

    def __repr__(self):
        return f'<{self.model_name}: #{self.id} - {self.short_uuid} - {self.real_interface} - {self.interface}>'

    ## used in admin.py
    ##      in overview.html
    @property
    def sensors__(self):
        return ', '.join(sorted(self.sensors.values_list('name', flat=True)))


class Module(NameMixin, BaseModel):
    def __str__(self):
        return f'#{self.id} - {self.short_uuid} - {self.name}'

    def __repr__(self):
        return f'<{self.model_name}: #{self.id} - {self.short_uuid} - {self.name}>'


class PublicIP(BaseModel):
    class Meta(BaseModel.Meta):
        verbose_name_plural = 'Public IPs'

    address = models.GenericIPAddressField(protocol='IPv4', unique=True, help_text='e.g. *********')

    def __str__(self):
        return f'#{self.id} - {self.short_uuid} - {self.address}'

    def __repr__(self):
        return f'<{self.model_name}: #{self.id} - {self.short_uuid} - {self.address}>'


class Router(NameMixin, AddressMixin, BaseModel):
    def __str__(self):
        return f'#{self.id} - {self.short_uuid} - {self.name}'

    def __repr__(self):
        return f'<{self.model_name}: #{self.id} - {self.short_uuid} - {self.name}>'


class RouterBoard(NameMixin, AddressMixin, BaseModel):
    class Meta(BaseModel.Meta):
        verbose_name_plural = 'RouterBoards'

    def __str__(self):
        return f'#{self.id} - {self.short_uuid} - {self.name}'

    def __repr__(self):
        return f'<{self.model_name}: #{self.id} - {self.short_uuid} - {self.name}>'


class Sensor(NameMixin, AddressMixin, BaseModel):
    def __str__(self):
        return f'#{self.id} - {self.short_uuid} - {self.name}'

    def __repr__(self):
        return f'<{self.model_name}: #{self.id} - {self.short_uuid} - {self.name}>'


class StaticIP(AddressMixin, VirtualAddressMixin, BaseModel):
    class Meta(BaseModel.Meta):
        verbose_name_plural = 'Static IPs'

    ## do NOT add blank=True, null=True
    computer_name = models.CharField(max_length=50, unique=True, help_text='e.g. some-custom-name', verbose_name='Computer Name', db_index=True)

    ## do NOT add blank=True, null=True
    ## do NOT add unique=True because different computer_name (i.e. dc01 and dc02) can have the same real_name (i.e. ADDS)
    real_name = models.CharField(max_length=50, help_text='e.g. Jane Austen', verbose_name='Real Name')

    def __str__(self):
        return f'#{self.id} - {self.short_uuid} - {self.address} - {self.virtual_address}'

    def __repr__(self):
        return f'<{self.model_name}: #{self.id} - {self.short_uuid} - {self.address} - {self.virtual_address}>'


class Switch(NameMixin, AddressMixin, BaseModel):
    class Meta(BaseModel.Meta):
        verbose_name_plural = 'Switches'

    def __str__(self):
        return f'#{self.id} - {self.short_uuid} - {self.name}'

    def __repr__(self):
        return f'<{self.model_name}: #{self.id} - {self.short_uuid} - {self.name}>'


class VMware(NameMixin, AddressMixin, BaseModel):
    class Meta(BaseModel.Meta):
        verbose_name_plural = 'VMwares'

    def __str__(self):
        return f'#{self.id} - {self.short_uuid} - {self.name}'

    def __repr__(self):
        return f'<{self.model_name}: #{self.id} - {self.short_uuid} - {self.name}>'


class WindowsServer(NameMixin, AddressMixin, BaseModel):
    class Meta(BaseModel.Meta):
        verbose_name_plural = 'Windows Servers'

    def __str__(self):
        return f'#{self.id} - {self.short_uuid} - {self.name}'

    def __repr__(self):
        return f'<{self.model_name}: #{self.id} - {self.short_uuid} - {self.name}>'
