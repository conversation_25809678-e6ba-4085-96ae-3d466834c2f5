from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.http import FileResponse, Http404  # HttpResponse
from django.shortcuts import render

import os

from MySQLdb import connect
from rahavard import (
    comes_from_htmx,
    convert_string_True_False_None_0,
)

from .models import (
    Router,
    RouterBoard,
    Sensor,
    Switch,
    VMware,
    WindowsServer,
)

from .utils_classes import (
    DaemonConfig,
    MYSQLConfig,
)

from .utils_constants import (
    LIMITS,
    LOGICAL_OPERATORS,
    MONTHS_LIST,
    PICK_AN_OPTION,
    RECENTS_TO_SHOW,
    REFRESHES,
    SEARCH_SIGNS,
    SEASONS_LIST,
    TOPS_TO_SHOW,
)

from .utils_extra import (
    get_vpnserver_object,
)

from .utils import (
    create_name_of_database,
    get_random_wallpaper,
    get_to_shows,
    get_today_ymd,
    move_n_days,
    service_is_running,
)

from dhcp.all import all_tables as all_tables__dhcp
from dns.all import all_tables as all_tables__dns
from filterlog.all import all_tables as all_tables__filterlog
from snort.all import all_tables as all_tables__snort
from squid.all import all_tables as all_tables__squid
from usernotice.all import all_tables as all_tables__usernotice
from vpnserver.all import all_tables as all_tables__vpnserver
from windowsserver.all import all_tables as all_tables__windowsserver


APP_TITLE = 'Base'
APP_SLUG  = 'base'


def globals(request):  ## NOTE do NOT rename. this function is registered as 'base.views.globals' to TEMPLATES list in project's settings.py
    project_dict = {
        'project_title_persian': settings.PROJECT_TITLE_PERSIAN,
        'project_title':         settings.PROJECT_TITLE,
        'project_credit':        settings.PROJECT_CREDIT,
        'project_credit_url':    settings.PROJECT_CREDIT_URL,
        'project_start_year':    settings.PROJECT_START_YEAR,
    }

    classes_dict = {
        'classes_for_btn_of_btn_group': 'btn btn-outline-light border-0 text-gray',
        'class_for_thead': 'table-dark',
    }

    if not request.user.is_authenticated:
        ## needed on login page
        return {
            **project_dict,
        }

    chosensensorname, \
    from_dropdown = get_to_shows(
        request,
        'chosen-sensor-name',
        'from-dropdown',
    )

    if from_dropdown:
        return {
            **classes_dict,
            'pick_an_option': PICK_AN_OPTION,
        }

    if comes_from_htmx(request):
        return {}

    yesterday_ymd = move_n_days(get_today_ymd(), n=-1)
    ## START JUMP_2 get the number of daemon errors for yesterday
    ## in order for it to be displayed at top navbar as notification
    if chosensensorname:
        cookie_content = request.COOKIES.get(f'daemon-errors-count-{chosensensorname}-{yesterday_ymd}')  ## 91/reset/None
        cookie_content = convert_string_True_False_None_0(cookie_content)

        if all([
            ## cookie exists
            cookie_content,

            ## it is a number, meaning:
            ##   1. previous attempts have successfully read count from db,
            ##      and
            ##   2. cookie has not been reset by user
            cookie_content not in ['not-set', 'reset'],
        ]):
            daemon_errors_count = int(cookie_content)

        elif any([
            ## cookie does not exist
            not cookie_content,

            ## previous attempts have failed to read count from db
            cookie_content == 'not-set',
        ]):
            database_name = create_name_of_database(DaemonConfig.SLUG.value, yesterday_ymd, chosensensorname)
            try:
                with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
                    with conn.cursor() as cur:
                        cur.execute('''
                            SELECT Level, Count FROM levelcounttable
                            WHERE (Level = %s);
                        ''', ('Error',))
                        daemon_errors_count = int(cur.fetchone()[-1])  ## 91
            except Exception:
                daemon_errors_count = 'net-set'

        else:
            ## cookie exists and it equals 'reset' (i.e. has been reset by user)
            daemon_errors_count = cookie_content
    else:
        daemon_errors_count = 'not-set'
    ## END JUMP_2 get the number of daemon errors for yesterday



    sensor_objects = Sensor.active_objects.all()
    if sensor_objects:
        name_of_first_sensor = sensor_objects.first().name
    else:
        name_of_first_sensor = None


    initials_for_avatar = ''
    tooltip_messages_for_avatar = []
    for service_name, tooltip_message, initial in [
        ## service_name  tooltip_message              initial
        ('live_parse',   'Live Parse is not Running', 'L'),
        ('mysql-server', 'MYSQL is not Running',      'M'),
        ('redis',        'Redis is not Running',      'R'),
        ('tor',          'TOR is not Running',        'T'),
    ]:
        if service_is_running(service_name):
            continue
        initials_for_avatar += initial
        tooltip_messages_for_avatar.append(tooltip_message)
    ##
    if tooltip_messages_for_avatar:
        tooltip_messages_for_avatar = ' | '.join(tooltip_messages_for_avatar)


    return {
        **project_dict,
        **classes_dict,

        'debug_': settings.DEBUG,

        'limits':          LIMITS.values,
        'recents_to_show': RECENTS_TO_SHOW.values,
        'refreshes':       REFRESHES.values,
        'tops_to_show':    TOPS_TO_SHOW.values,

        'months_list':  MONTHS_LIST,
        'seasons_list': SEASONS_LIST,

        'logical_operators':             LOGICAL_OPERATORS.values,
        'search_signs__field_separator': SEARCH_SIGNS.field_separator,

        'pick_an_option': PICK_AN_OPTION,

        'initials_for_avatar':         initials_for_avatar,
        'tooltip_messages_for_avatar': tooltip_messages_for_avatar,

        'router_objects':        Router.active_objects.all(),
        'routerboard_objects':   RouterBoard.active_objects.all(),
        'switch_objects':        Switch.active_objects.all(),
        'vmware_objects':        VMware.active_objects.all(),
        'windowsserver_objects': WindowsServer.active_objects.all(),

        'sensor_objects':       sensor_objects,
        'name_of_first_sensor': name_of_first_sensor,

        'vpnserver_object': get_vpnserver_object(),

        'cafebazaar_url': settings.CAFEBAZAAR_URL,

        'all_tables__dhcp':          all_tables__dhcp,
        'all_tables__dns':           all_tables__dns,
        'all_tables__filterlog':     all_tables__filterlog,
        'all_tables__snort':         all_tables__snort,
        'all_tables__squid':         all_tables__squid,
        'all_tables__usernotice':    all_tables__usernotice,
        'all_tables__vpnserver':     all_tables__vpnserver,
        'all_tables__windowsserver': all_tables__windowsserver,

        'daemon_errors_count': daemon_errors_count,
        'yesterday_ymd': yesterday_ymd,

        # 'files_inside_project_logs_dir': sorted(listdir(settings.PROJECT_LOGS_DIR)),
    }

def homepage(request):
    return render(
        request,
        f'{APP_SLUG}/homepage.html',
        context={
            'main_title': settings.PROJECT_TITLE,
            'random_wallpaper': get_random_wallpaper(),
        },
    )

@login_required
def serve_protected_file(request, path):
    # if not <condition>:
    #     raise Http404()

    file_path = os.path.join(settings.PROTECTED_MEDIA_ROOT, path)

    if not os.path.isfile(file_path):
        raise Http404()

    return FileResponse(open(file_path, 'rb'))
