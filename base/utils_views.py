'''
    common functions used/called/imported
    by viwes.py in different apps
'''


from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render

from collections import defaultdict
from datetime import datetime
from json import loads
from time import mktime

from MySQLdb import connect

from rahavard import (
    clear_messages,
    comes_from_htmx,
    convert_string_True_False_None_0,
    create_id_for_htmx_indicator,
    sort_dict,
)

from .utils_database import (
    get_max_id,
)

from .utils_classes import (
    MYSQLConfig,
)

from .utils import (
    create_date_range,
    create_name_of_database,
    create_warning_message,
    get_name_of_function,
    get_parsed_dirs,
    get_rts_dts_dets,
    get_to_shows,
    paginate,
)

from .handlers import (
    bad_request,
)

from .utils_extra import (
    get_interfaces_dict,
    get_ip_of_chosen_sensor_name,
)


def detailed_activity(
    request,
    chart_slug,
    datetoshow,
    from_dropdown,

    app_title,
    app_slug,
    src_dir,

    is_sensor_independent,

    chosensensorname,

    routerboardname=None,
    routername=None,
    switchname=None,
    vmwarename=None,
    windowsservername=None,
):
    if routerboardname:   routerboardname   = convert_string_True_False_None_0(routerboardname)
    if routername:        routername        = convert_string_True_False_None_0(routername)
    if switchname:        switchname        = convert_string_True_False_None_0(switchname)
    if vmwarename:        vmwarename        = convert_string_True_False_None_0(vmwarename)
    if windowsservername: windowsservername = convert_string_True_False_None_0(windowsservername)

    if   routerboardname:   main_title = f'{app_title}: {routerboardname} - Detailed Activity'
    elif routername:        main_title = f'{app_title}: {routername} - Detailed Activity'
    elif switchname:        main_title = f'{app_title}: {switchname} - Detailed Activity'
    elif vmwarename:        main_title = f'{app_title}: {vmwarename} - Detailed Activity'
    elif windowsservername: main_title = f'{app_title}: {windowsservername} - Detailed Activity'
    else:                   main_title = f'{app_title} - Detailed Activity'

    parsed_dirs = get_parsed_dirs(src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

    if not parsed_dirs:
        return bad_request(request, error_msg=f'No Databases for {app_title}')

    if datetoshow: date_to_show = datetoshow
    else:          date_to_show = get_to_shows(request, 'date')

    ## pick the latest available date for date_to_show
    if not date_to_show:
        date_to_show = parsed_dirs[-1]  ## 2023-05-26

    if chart_slug:
        try:
            if   routerboardname:   database_name = create_name_of_database(app_slug, date_to_show, routerboardname)
            elif routername:        database_name = create_name_of_database(app_slug, date_to_show, routername)
            elif switchname:        database_name = create_name_of_database(app_slug, date_to_show, switchname)
            elif vmwarename:        database_name = create_name_of_database(app_slug, date_to_show, vmwarename)
            elif windowsservername: database_name = create_name_of_database(app_slug, date_to_show, windowsservername)
            else:
                if is_sensor_independent:
                    database_name = create_name_of_database(app_slug, date_to_show)
                else:
                    database_name = create_name_of_database(app_slug, date_to_show, chosensensorname)

            with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
                with conn.cursor() as cur:
                    cur.execute('SELECT Millisecond, Count FROM millisecondtoptable;')
                    rows = dict(cur)  # .fetchall()
        except Exception as exc:
            clear_messages(request)
            warning_message = create_warning_message(request, app_title, date_to_show, get_name_of_function(), exc)
            messages.warning(request, warning_message)
            rows = {}

        return JsonResponse({
            'data': [
                [int(millisecond), count]
                for millisecond, count in rows.items()
            ],
            ## [
            ##     [1683837000, 157573],
            ##     [1683923400, 6628586],
            ##     ...
            ## ]

            ## to be used in graph's subtitle
            'start': date_to_show,
            'end': None,
            'graph_title': main_title,
            'chosensensorname': None if is_sensor_independent else chosensensorname,
            'chosensensorip':   None if is_sensor_independent else get_ip_of_chosen_sensor_name(name=chosensensorname),
        })

    if from_dropdown:
        html_file = '00-chart-detailed-activity--inside-content.html'
    else:
        html_file = '00-chart-detailed-activity.html'

    chart_url_base = f'{app_slug}-detailed-activity-url'

    return render(
        request,
        html_file,
        context={
            'multi_day_report_allowed': False,

            'date_to_show': date_to_show,
            'from_dropdown': from_dropdown,

            'main_title': main_title,

            'chosensensorname': chosensensorname,
            'router_name': routername,
            'routerboard_name': routerboardname,
            'switch_name': switchname,
            'vmware_name': vmwarename,
            'windowsserver_name': windowsservername,

            'chart_url_base': chart_url_base,
            'parsed_dirs': parsed_dirs,
            'recents_to_show': None,  ## to prevent recents dropdown from showing
        },
    )

def overall_activity(
    request,
    chart_slug,
    recenttoshow,
    datetoshow,
    dateendtoshow,
    from_dropdown,

    app_title,
    app_slug,
    src_dir,

    is_sensor_independent,

    chosensensorname,

    routerboardname=None,
    routername=None,
    switchname=None,
    vmwarename=None,
    windowsservername=None,

    is_real_interface=False,
):
    if routerboardname:   routerboardname   = convert_string_True_False_None_0(routerboardname)
    if routername:        routername        = convert_string_True_False_None_0(routername)
    if switchname:        switchname        = convert_string_True_False_None_0(switchname)
    if vmwarename:        vmwarename        = convert_string_True_False_None_0(vmwarename)
    if windowsservername: windowsservername = convert_string_True_False_None_0(windowsservername)
    if is_real_interface: is_real_interface = convert_string_True_False_None_0(is_real_interface)

    if   routerboardname:   main_title = f'{app_title}: {routerboardname} - Overall Activity'
    elif routername:        main_title = f'{app_title}: {routername} - Overall Activity'
    elif switchname:        main_title = f'{app_title}: {switchname} - Overall Activity'
    elif vmwarename:        main_title = f'{app_title}: {vmwarename} - Overall Activity'
    elif windowsservername: main_title = f'{app_title}: {windowsservername} - Overall Activity'
    elif is_real_interface: main_title = f'{app_title} - Interface Activity'
    else:                   main_title = f'{app_title} - Overall Activity'

    parsed_dirs = get_parsed_dirs(src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

    if not parsed_dirs:
        return bad_request(request, error_msg=f'No Databases for {app_title}')

    if recenttoshow: recent_to_show = recenttoshow
    else:            recent_to_show = get_to_shows(request, 'recent')

    if datetoshow: date_to_show = datetoshow
    else:          date_to_show = get_to_shows(request, 'date')

    if dateendtoshow: date_end_to_show = dateendtoshow
    else:             date_end_to_show = get_to_shows(request, 'date-end')

    recent_to_show, \
    date_to_show, \
    date_end_to_show = get_rts_dts_dets(recent_to_show, date_to_show, date_end_to_show)

    date_range = create_date_range(date_to_show, date_end_to_show)

    ## called by js/graph.js
    if chart_slug:
        chart_slug = chart_slug.lower()

        if is_real_interface:
            table_name = 'realinterfacetoptable'
        else:
            table_name = f'{app_slug}table'

        data = []
        dates_list = []
        max_ids = []

        for d_r in date_range:

            if   routerboardname:   database_name = create_name_of_database(app_slug, d_r, routerboardname)
            elif routername:        database_name = create_name_of_database(app_slug, d_r, routername)
            elif switchname:        database_name = create_name_of_database(app_slug, d_r, switchname)
            elif vmwarename:        database_name = create_name_of_database(app_slug, d_r, vmwarename)
            elif windowsservername: database_name = create_name_of_database(app_slug, d_r, windowsservername)
            else:
                if is_sensor_independent:
                    database_name = create_name_of_database(app_slug, d_r)
                else:
                    database_name = create_name_of_database(app_slug, d_r, chosensensorname)

            try:
                with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
                    with conn.cursor() as cur:
                        if is_real_interface:
                            cur.execute(f'''
                                SELECT `Real Interface`, Count FROM {table_name}
                                WHERE (`Real Interface` = %s);
                            ''', (chart_slug,))
                            max_id = cur.fetchone()[1] or 0
                        else:
                            max_id = get_max_id(database_name, table_name)
            except Exception:
                max_id = 0

            max_ids.append(max_id)

            ## convert d_r (i.e. 2023-05-12) to timestamp in ms (i.e. 1624973400000)
            try:
                timestamped = int(mktime(datetime.strptime(d_r, '%Y-%m-%d').timetuple()))*1000  ## *1000 to convert to ms
            except Exception:
                timestamped = None

            if timestamped:
                data.append([timestamped, max_id])
                dates_list.append(d_r)


       ## data = [
        ##     [1683837000, 157573],
        ##     [1683923400, 6628586],
        ##     ...
        ## ]

        ## dates_list (will be used to display actual date (i.e. 2023-05-12 instead of timestamped date) in tooltip):
        ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

        ## max_ids:
        ## [6628586, 5041886, 2749313, 3731521, 5435566, ...]

        return JsonResponse({
            'data': data,
            'dates_list': dates_list,
            'total': sum(max_ids),
            'real_interface': chart_slug,

            ## to be used in graph's subtitle
            'start': date_range[0],
            'end': date_range[-1],
            'graph_title': main_title,
            'chosensensorname': None if is_sensor_independent else chosensensorname,
            'chosensensorip':   None if is_sensor_independent else get_ip_of_chosen_sensor_name(name=chosensensorname),
        })

    if is_real_interface:
        chart_url_base = f'{app_slug}-interface-activity-url'
        real_interface = chart_slug
        interfaces_dict = sort_dict(
            get_interfaces_dict(chosensensorname) or {},
            based_on='value',
            reverse=False,
        )
    else:
        chart_url_base = f'{app_slug}-overall-activity-url'
        real_interface = None
        interfaces_dict = {}

    if from_dropdown:
        if is_real_interface:
            html_file = '00-chart-interface-activity--inside-content.html'
        else:
            html_file = '00-chart-overall-activity--inside-content.html'
    else:
        if is_real_interface:
            html_file = '00-chart-interface-activity.html'
        else:
            html_file = '00-chart-overall-activity.html'

    return render(
        request,
        html_file,
        context={
            'multi_day_report_allowed': True,
            'main_title': main_title,

            'date_to_show': date_to_show,
            'date_end_to_show': date_end_to_show,
            'recent_to_show': recent_to_show,
            'from_dropdown': from_dropdown,

            'chosensensorname': chosensensorname,
            'router_name': routername,
            'routerboard_name': routerboardname,
            'switch_name': switchname,
            'vmware_name': vmwarename,
            'windowsserver_name': windowsservername,

            ## by and for interface_activity
            'real_interface': real_interface,
            'interfaces_dict': interfaces_dict,

            'date_range': date_range,

            'chart_url_base': chart_url_base,
            'parsed_dirs': parsed_dirs,
        },
    )

def interface_activity(
    request,
    real_interface,
    recenttoshow,
    datetoshow,
    dateendtoshow,
    from_dropdown,

    app_title,
    app_slug,
    src_dir,

    is_sensor_independent,

    chosensensorname,

    is_real_interface=True,
):

    return overall_activity(
        request=request,
        chart_slug=real_interface,
        recenttoshow=recenttoshow,
        datetoshow=datetoshow,
        dateendtoshow=dateendtoshow,
        from_dropdown=from_dropdown,

        app_title=app_title,
        app_slug=app_slug,
        src_dir=src_dir,

        is_sensor_independent=is_sensor_independent,

        chosensensorname=chosensensorname,

        is_real_interface=is_real_interface,
    )

def visits(
    request,
    mode,
    from_dropdown,

    app_title,
    app_slug,
    src_dir,

    is_sensor_independent,

    chosensensorname,
):
    limit_to_show,    \
    recent_to_show,   \
    date_to_show,     \
    date_end_to_show, \
    page_number = get_to_shows(
        request,
        'limit',
        'recent',
        'date',
        'date-end',
        'page',

        pick_lowest=True,
    )

    mode = mode.lower()
    if mode == 'ip':
        column_name = 'IP'
        db_headers  = ['', 'IP', 'Computer Name', 'Real Name', 'Action', 'Domains']
        table_name  = 'ipdomainscountstable'
    elif mode == 'domain':
        column_name = 'Domain'
        db_headers  = ['', 'Domain', 'Action', 'IPs']
        table_name  = 'domainipscountstable'

    main_title = f'{app_title} - Visits - {column_name}'

    parsed_dirs = get_parsed_dirs(src_dir, reverse=False)  ## ['2023-05-12', '2023-05-13', '2023-05-14', ...]

    if not parsed_dirs:
        return bad_request(request, error_msg=f'No Databases for {app_title}')

    recent_to_show, \
    date_to_show, \
    date_end_to_show = get_rts_dts_dets(recent_to_show, date_to_show, date_end_to_show)

    date_range = create_date_range(date_to_show, date_end_to_show)

    if not from_dropdown and comes_from_htmx(request):
        domain, ip = get_to_shows(
            request,
            'domain',
            'ip',
        )

        ## triggered by the button
        if domain or ip:
            ## show
            right_col__main = defaultdict(int)
            if ip:
                left_col = ip
                db_query = f'''
                    SELECT DomainsCounts FROM {table_name}
                    WHERE (IP = %s)
                '''
            elif domain:
                left_col = domain
                db_query = f'''
                    SELECT IPsCounts FROM {table_name}
                    WHERE (Domain = %s)
                '''
            else:
                ## it may have been passed as ?domain= (not ?domain=www.example.com)
                return HttpResponse('Empty Parameters not Allowed')

            for d_r in date_range:
                if is_sensor_independent:
                    database_name = create_name_of_database(app_slug, d_r)
                else:
                    database_name = create_name_of_database(app_slug, d_r, chosensensorname)

                try:
                    with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
                        with conn.cursor() as cur:
                            cur.execute(f'{db_query};', (left_col,))
                            right_col = cur.fetchone()[0]
                            ## '{"************": 19}' (<- str)

                    right_col = loads(right_col)  ## right_col is a string turned (by loads) into a dictionary (__USING_DUMPS_LOADS__)
                    ## {'192.168.170.45': 70, '192.168.170.48': 21, ...}

                    for item, count in right_col.items():
                        right_col__main[item] += count
                except Exception:
                    right_col = '{}'

            right_col__main = sort_dict(right_col__main, based_on='value', reverse=True)

            ## JUMP_1 paginate a dictionary
            right_col__main = paginate(right_col__main, limit_to_show, page_number)

            if page_number == 1:
                html_file = f'{app_slug}/00-visits--td.html'
            else:
                html_file = f'{app_slug}/00-visits--td--rows.html'

            return render(
                request,
                html_file,
                context={
                    'column_name': column_name,
                    'date_end_to_show': date_end_to_show,
                    'date_to_show': date_to_show,
                    'db_headers': db_headers,
                    'domain': domain,
                    'ip': ip,
                    'left_col': left_col,
                    'limit_to_show': limit_to_show,
                    'mode': mode,
                    'page_number': page_number,
                    'right_col': right_col__main,

                    'chosensensorname': chosensensorname,
                },
            )

        else:

            item_counts_dict = defaultdict(int)
            for d_r in date_range:
                if is_sensor_independent:
                    database_name = create_name_of_database(app_slug, d_r)
                else:
                    database_name = create_name_of_database(app_slug, d_r, chosensensorname)

                try:
                    with connect(**MYSQLConfig.R_USER_CREDS.value, database=database_name) as conn:
                        with conn.cursor() as cur:

                            ## NOTE JUMP_9 below, we won't be calculating aggregations
                            ##             after getting rows from db
                            ##             so we are allowed to use limit_to_show right here in the query
                            if mode == 'ip':
                                cur.execute(f'SELECT IP, `No of Domains` FROM {table_name};')
                                info_of_day = dict(cur)  # .fetchall()
                            elif mode == 'domain':
                                cur.execute(f'SELECT Domain, `No of IPs` FROM {table_name};')
                                info_of_day = dict(cur)  # .fetchall()
                            ## {'***************': 83, '***************': 52, ...}

                    for item, count in info_of_day.items():
                        item_counts_dict[item] += count
                except Exception:
                    info_of_day = {}

            item_counts_dict = sort_dict(item_counts_dict, based_on='value', reverse=True)

            ## JUMP_1 paginate a dictionary
            item_counts_dict = paginate(item_counts_dict, limit_to_show, page_number)

            return render(
                request,
                f'{app_slug}/00-visits--rows.html',
                context={
                    'column_name': column_name,
                    'date_end_to_show': date_end_to_show,
                    'date_to_show': date_to_show,
                    'db_headers': db_headers,
                    'id_for_htmx_indicator': create_id_for_htmx_indicator('visits'),
                    'item_counts_dict': item_counts_dict,
                    'limit_to_show': limit_to_show,
                    'mode': mode,
                    'page_number': page_number,

                    'chosensensorname': chosensensorname,
                },
            )

    if from_dropdown:
        html_file = '00-visits--inside-content.html'
    else:
        html_file = 'visits.html'

    return render(
        request,
        f'{app_slug}/{html_file}',
        context={
            'page_has_scrollable_tables': True,
            'multi_day_report_allowed': True,
            'main_title': main_title,

            'column_name': column_name,
            'date_end_to_show': date_end_to_show,
            'date_to_show': date_to_show,
            'db_headers': db_headers,
            'from_dropdown': from_dropdown,
            'id_for_htmx_indicator': create_id_for_htmx_indicator('visits'),
            'limit_to_show': limit_to_show,
            'mode': mode,
            'page_number': 0,  ## set to 0 to prevent page input from showing
            'parsed_dirs': parsed_dirs,
            'recent_to_show': recent_to_show,
            'search_forbidden': True,

            'chosensensorname': chosensensorname,

        },
    )
