from django.shortcuts import render

from .utils import get_to_shows


## https://docs.djangoproject.com/en/4.0/topics/http/views/#returning-errors
## https://docs.djangoproject.com/en/4.0/ref/views/#error-views
## 400
def bad_request(request, exception=None, error_msg='Bad Request'):
    error_code = '400'
    error_title = f'ERROR {error_code}'

    return render(
        request,
        'base/handler.html',
        context={
            'main_title': error_title,
            'error_code': error_code,
            'error_title': error_title,
            'error_msg': error_msg,
            'is_handling': True,

            'chosensensorname': get_to_shows(request, 'chosen-sensor-name'),
            'parsed_dirs': None,  ## to prevent date picker from showing
            'recents_to_show': None,  ## to prevent recents dropdown from showing
        }
    )

## 403
def permission_denied(request, error_msg='Permission Denied', exception=None):
    error_code = '403'
    error_title = f'ERROR {error_code}'

    return render(
        request,
        'base/handler.html',
        context={
            'main_title': error_title,
            'error_code': error_code,
            'error_title': error_title,
            'error_msg': error_msg,
            'is_handling': True,

            'chosensensorname': get_to_shows(request, 'chosen-sensor-name'),
            'parsed_dirs': None,  ## to prevent date picker from showing
            'recents_to_show': None,  ## to prevent recents dropdown from showing
        }
    )

## 404
def page_not_found(request, error_msg='Page Not Found', exception=None):
    error_code = '404'
    error_title = f'ERROR {error_code}'

    return render(
        request,
        'base/handler.html',
        context={
            'main_title': error_title,
            'error_code': error_code,
            'error_title': error_title,
            'error_msg': error_msg,
            'is_handling': True,

            'chosensensorname': get_to_shows(request, 'chosen-sensor-name'),
            'parsed_dirs': None,  ## to prevent date picker from showing
            'recents_to_show': None,  ## to prevent recents dropdown from showing
        }
    )

## 500
def server_500(request, error_msg='Internal Server ERROR', exception=None):
    error_code = '500'
    error_title = f'ERROR {error_code}'

    return render(
        request,
        'base/handler.html',
        context={
            'main_title': error_title,
            'error_code': error_code,
            'error_title': error_title,
            'error_msg': error_msg,
            'is_handling': True,

            'chosensensorname': get_to_shows(request, 'chosen-sensor-name'),
            'parsed_dirs': None,  ## to prevent date picker from showing
            'recents_to_show': None,  ## to prevent recents dropdown from showing
        }
    )
