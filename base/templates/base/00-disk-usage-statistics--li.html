{% load humanize %}
{% load tags-filters %}

{# added 'text-dark bg-transparent' to avoid color change when hovered #}

{% if disk_usage_statistics %}
  {# aymdhms #}
  {% comment %}
  <li class="dropdown-item     text-dark bg-transparent py-3 fw-bold">
    <p class="mb-0 fw-medium text-muted">{{disk_usage_statistics.aymdhms}}</p>
  </li>
  {% endcomment %}

  {% for name, space in disk_usage_statistics.items %}
    {% if not name == "aymdhms" %}
      <li class="dropdown-item     text-dark bg-transparent py-1">
        <p class="mb-0 fw-medium">
          <span class="text-info">{{ name }}</span>&nbsp;&nbsp;
          {{space|convert_byte}}
          <small class="text-muted">({{space|intcomma}} bytes)</small>
          {% if not name == "Total" %}: {% get_percent space disk_usage_statistics.Total %}%{% endif %}
        </p>
      </li>
    {% endif %}
  {% endfor %}
{% else %}
  <li class="dropdown-item     text-dark bg-transparent">
    <p class="mb-0 fw-medium text-muted">No Disk Usage Statistics</p>
  </li>
{% endif %}
