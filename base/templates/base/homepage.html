{% load static %}

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{main_title}}</title>
  <link rel="icon" href='{% static "files/project-logo/logo.ico" %}'>
  <link rel="stylesheet" href="{% static "astro-motion/css/bootstrap.min.css" %}">
  <link rel="stylesheet" href="{% static "astro-motion/css/slick.css" %}" type="text/css" /> 
  <link rel="stylesheet" href="{% static "astro-motion/css/templatemo-style.css" %}">

  <link rel="stylesheet" href="{% static "astro-motion/custom.css" %}">
</head>
<body>
  <video id="bg-video"     style="background: url('{{random_wallpaper}}') top center no-repeat;">
      {# <source src="video/gfp-astro-timelapse.mp4" type="video/mp4"> #}
  </video>

  <div class="page-container">
      <div class="container-fluid">
        <div class="row">
          <div class="col-xs-12">
            <div class="cd-slider-nav">
              <nav class="navbar navbar-expand-lg     px-3" id="tm-nav">
                <div class="navbar-brand">
                  <div class="d-flex align-items-center">
                    {# logo #}
                    <div><img src="{% static "files/project-logo/logo.ico" %}" class="img img-fluid me-3"></div>
                    {# title #}
                    <div class="waviy_div text-center" href="{{request.path}}">
                      <h2 class="waviy floating mb-0">
                        <span style="--i:1">E</span>
                        <span style="--i:2">T</span>
                        <span style="--i:3">E</span>
                        <span style="--i:4">R</span>
                        <span style="--i:5">N</span>
                        <span style="--i:6">A</span>
                      </h2>
                    </div>
                  </div>
                </div>
              </nav>
            </div>
          </div>          
        </div>        
      </div>      
      <div class="container-fluid tm-content-container">
        <ul class="cd-hero-slider mb-0 py-5">
          <li class="px-3" data-page-no="1">
            <div class="position-relative page-width-1 page-left">
              <div class="tm-bg-dark content-pad">
                <figure class="auto_update_chart_div">
                  <div id="auto_update_chart" ></div>
                   <h2 class="mb-4">Welcome to Eterna</h2>
                   <p class="mb-4">
                     Eterna is a full-featured web application mastering at
                     collecting,
                     parsing,
                     displaying,
                     analyzing and
                     correlating network-wide logs and events.
                   </p>
                   <p class="mb-4">
                     The currently available areas of focus include the following:<br>
                     Snort,
                     FilterLog,
                     Squid,
                     DNS,
                     DHCP,
                     Windows Server,
                     VMware,
                     User Audit,
                     Daemon,
                     User Warning,
                     RouterBoard,
                     Switch and
                     Router.
                   </p>
                </figure>
                <div>
                  <a href="{% url "general-homepage-url" %}" data-page-no="2" class="btn">Go to Dashboard</a>
                </div> 
              </div>              
            </div>
          </li>
        </ul>
    </div>
    <div class="container-fluid">
      <footer class="row mx-auto tm-footer">
        <div class="col-md-6 px-0 small">
          {% include '00-copyright-and-credit.html' %}
        </div>
        <div class="col-md-6 px-0 tm-footer-right">
          {# Designed by <a rel="sponsored" href="https://templatemo.com" target="_blank" class="tm-link-white">TemplateMo</a> #}
        </div>
      </footer>
    </div>
  </div>

  <div id="loader-wrapper">            
    <div id="loader"></div>
    <div class="loader-section section-left"></div>
    <div class="loader-section section-right"></div>
  </div>  

  <script src="{% static "astro-motion/js/jquery-3.5.1.min.js" %}"></script>
  <script src="{% static "astro-motion/js/bootstrap.min.js" %}"></script>
  <script src="{% static "astro-motion/js/slick.js" %}"></script>
  <script src="{% static "astro-motion/js/templatemo-script.js" %}"></script>

  <script src="{% static "js/highcharts/highcharts.js" %}"></script>  {% comment %}keep on top{% endcomment %}
  <script type="text/javascript">
    // https://jsfiddle.net/gh/get/library/pure/highcharts/highcharts/tree/master/samples/highcharts/demo/dynamic-update
    Highcharts.chart('auto_update_chart', {
      chart: {
        type: 'spline',
        backgroundColor: 'rgba(255, 255, 255, 0)',
        animation: Highcharts.svg,  // don't animate in old IE
        marginRight: 0,
        marginLeft: 0,
        events: {
          load: function () {
            // set up the updating of the chart each second
            var series = this.series[0];
            setInterval(function () {
              var x = (new Date()).getTime(),  // current time
                y = Math.random()*100;
              series.addPoint([x, y], true, true);
            }, 1000);
          }
        }
      },

      time: {
        useUTC: false
      },

      title: {
          text: null,
      },

      credits: {
        enabled: false,
      },

      accessibility: {
        announceNewData: {
          enabled: true,
          minAnnounceInterval: 15000,
          announcementFormatter: function (allSeries, newSeries, newPoint) {
            if (newPoint) {
              return 'New point added. Value: ' + newPoint.y;
            }
            return false;
          }
        }
      },

      xAxis: {
        type: 'datetime',
        tickPixelInterval: 150,
        labels: {
          enabled: false,
        },
        visible: false,
        lineWidth: 0,
        minorGridLineWidth: 0,
        lineColor: 'transparent',
        minorTickLength: 0,
        tickLength: 0,
      },

      yAxis: {
        title: {
            text: null,
        },
        labels: {
          enabled: false,
        },
        plotLines: [{
            value: 0,
            width: 0,
            color: '#808080'
        }],

        // hide horizontal lines
        gridLineColor: '#ffffff00',
        lineColor: '#ffffff00',
      },

      tooltip: {
        headerFormat: null,
        pointFormat: null,
      },

      legend: {
        enabled: false
      },

      colors: ["rgba(255, 255, 255,.4)"],

      series: [{
        name: 'Random Data',
        marker: {
          enabled: false,
          symbol: 'diamond',
          radius: 3,
        },
        lineWidth: 1,
        // shadow: {  // works
        //     color: 'black',
        //     width: 4,
        //     opacity: 0.5,
        //     offsetX: 0,
        //     offsetY: 0
        // },
        data: (function () {
          // generate an array of random data
          var data = [],
              time = (new Date()).getTime(),
              i;

          for (i = -25; i <= 0; i++) {
            data.push({
              x: time + i * 1000,
              y: Math.random()*100,
            });
          }
          return data;
        }())
      }]
    });
  </script>
  </body>
</html>
