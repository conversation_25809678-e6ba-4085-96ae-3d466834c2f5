{# added 'text-dark bg-transparent' to avoid color change when hovered #}

{% if parsed_dates_statistics %}
  {# aymdhms #}
  <li class="dropdown-item     text-dark bg-transparent py-3 fw-bold">
    <p class="mb-0 fw-medium text-muted">{{parsed_dates_statistics.aymdhms}}</p>
  </li>

  {% for name, dates in parsed_dates_statistics.items %}
    {% if not name == "aymdhms" %}
      <li class="dropdown-item     text-dark bg-transparent py-1">
        <p class="mb-0 fw-medium">{{ dates|length }}&nbsp;&nbsp;<span class="text-info">{{ name }}</span></p>
      </li>
    {% endif %}
  {% endfor %}
{% else %}
  <li class="dropdown-item     text-dark bg-transparent">
    <p class="mb-0 fw-medium text-muted">No Parsed Dates Statistics</p>
  </li>
{% endif %}
