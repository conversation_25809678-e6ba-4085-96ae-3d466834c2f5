from unittest import TestCase

from base.utils_classes import MYSQLConfig

from dhcp.views import get_column_name_and_table_name_from_slug as gcnatnfs__dhcp
from dns.views import get_column_name_and_table_name_from_slug as gcnatnfs__dns
from filterlog.views import get_column_name_and_table_name_from_slug as gcnatnfs__filterlog
from geolocation.views import get_column_name_and_table_name_from_slug as gcnatnfs__geolocation
from snort.views import get_column_name_and_table_name_from_slug as gcnatnfs__snort
from squid.views import get_column_name_and_table_name_from_slug as gcnatnfs__squid
from usernotice.views import get_column_name_and_table_name_from_slug as gcnatnfs__usernotice
from windowsserver.views import get_column_name_and_table_name_from_slug as gcnatnfs__windowsserver


class TestDHCP(TestCase):
    '''
    Test cases for the get_column_name_and_table_name_from_slug function.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.dictionary = {
            ## slug         column_name     table_name
            'time':        ('Time',        'timetoptable'),
            'description': ('Description', 'descriptiontoptable'),
            'source-ip':   ('Source IP',   'sourceiptoptable'),
            'host-name':   ('Host Name',   'hostnametoptable'),
            'mac-address': ('MAC Address', 'macaddresstoptable'),

            'millisecond': ('Millisecond', 'millisecondtoptable'),
        }

    def test_valid_arguments(self):
        '''
        Test that valid slugs are correctly converted to column and table names.
        '''

        for slug, tup in self.dictionary.items():
            result = gcnatnfs__dhcp(slug)
            self.assertEqual(result, tup)

    def test_invalid_arguments(self):
        '''
        Test that invalid arguments return (None, None).
        '''

        result = gcnatnfs__dhcp('invalid-slug')
        self.assertEqual(result, (None, None))

class TestDNS(TestCase):
    '''
    Test cases for the get_column_name_and_table_name_from_slug function.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.dictionary = {
            ## slug                    column_name                table_name
            'time':                   ('Time',                   'timetoptable'),
            'udp-tcp-indicator':      ('UDP/TCP Indicator',      'udptcpindicatortoptable'),
            'send-receive-indicator': ('Send/Receive Indicator', 'sendreceiveindicatortoptable'),
            'source-ip':              ('Source IP',              'sourceiptoptable'),
            'responsecode':           ('ResponseCode',           'responsecodetoptable'),
            'question-type':          ('Question Type',          'questiontypetoptable'),
            'question-name':          ('Question Name',          'questionnametoptable'),

            'millisecond':            ('Millisecond',            'millisecondtoptable'),
        }

    def test_valid_arguments(self):
        '''
        Test that valid slugs are correctly converted to column and table names.
        '''

        for slug, tup in self.dictionary.items():
            result = gcnatnfs__dns(slug)
            self.assertEqual(result, tup)

    def test_invalid_arguments(self):
        '''
        Test that invalid arguments return (None, None).
        '''

        result = gcnatnfs__dns('invalid-slug')
        self.assertEqual(result, (None, None))

class TestFilterLog(TestCase):
    '''
    Test cases for the get_column_name_and_table_name_from_slug function.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.dictionary = {
            ## slug              column_name          table_name
            'time':             ('Time',             'timetoptable'),
            'protocol-name':    ('Protocol Name',    'protocolnametoptable'),
            'source-ip':        ('Source IP',        'sourceiptoptable'),
            'destination-ip':   ('Destination IP',   'destinationiptoptable'),
            'source-port':      ('Source Port',      'sourceporttoptable'),
            'destination-port': ('Destination Port', 'destinationporttoptable'),
            'real-interface':   ('Real Interface',   'realinterfacetoptable'),
            'reason':           ('Reason',           'reasontoptable'),
            'action':           ('Action',           'actiontoptable'),
            'direction':        ('Direction',        'directiontoptable'),

            'millisecond':      ('Millisecond',      'millisecondtoptable'),
        }

    def test_valid_arguments(self):
        '''
        Test that valid slugs are correctly converted to column and table names.
        '''

        for slug, tup in self.dictionary.items():
            result = gcnatnfs__filterlog(slug)
            self.assertEqual(result, tup)

    def test_invalid_arguments(self):
        '''
        Test that invalid arguments return (None, None).
        '''

        result = gcnatnfs__filterlog('invalid-slug')
        self.assertEqual(result, (None, None))

class TestGeoLocation(TestCase):
    '''
    Test cases for the get_column_name_and_table_name_from_slug function.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.dictionary_domain = {
            ## slug          column_name      table_name
            'country':      ('Country',      'countrytoptable__domain'),
            'city':         ('City',         'citytoptable__domain'),
            'timezone':     ('Timezone',     'timezonetoptable__domain'),
            'isp':          ('ISP',          'isptoptable__domain'),
            'organization': ('Organization', 'organizationtoptable__domain'),
            'ip':           ('IP',           'iptoptable__domain'),
        }

        self.dictionary_ip = {
            ## slug          column_name      table_name
            'type':         ('Type',         'typetoptable__ip',),
            'continent':    ('Continent',    'continenttoptable__ip',),
            'country':      ('Country',      'countrytoptable__ip',),
            'city':         ('City',         'citytoptable__ip',),
            'organization': ('Organization', 'organizationtoptable__ip',),
            'isp':          ('ISP',          'isptoptable__ip',),
            'domain':       ('Domain',       'domaintoptable__ip',),
            'timezone':     ('Timezone',     'timezonetoptable__ip',),
        }

    def test_valid_arguments_domain(self):
        '''
        Test that valid slugs are correctly converted to column and table names.
        '''

        for slug, tup in self.dictionary_domain.items():
            result = gcnatnfs__geolocation(slug, geo_mode='domain')
            self.assertEqual(result, tup)

    def test_valid_arguments_ip(self):
        '''
        Test that valid slugs are correctly converted to column and table names.
        '''

        for slug, tup in self.dictionary_ip.items():
            result = gcnatnfs__geolocation(slug, geo_mode='ip')
            self.assertEqual(result, tup)

    def test_invalid_arguments_domain(self):
        '''
        Test that invalid arguments return (None, None).
        '''

        ## invalid slug, valid geo_mode
        result = gcnatnfs__geolocation('invalid-slug', geo_mode='domain')
        self.assertEqual(result, (None, None))

        ## valid slug, invalid geo_mode
        result = gcnatnfs__geolocation(list(self.dictionary_domain.keys())[0], geo_mode='invalid-geo-mode')
        self.assertEqual(result, (None, None))

    def test_invalid_arguments_ip(self):
        '''
        Test that invalid arguments return (None, None).
        '''

        ## invalid slug, valid geo_mode
        result = gcnatnfs__geolocation('invalid-slug', geo_mode='ip')
        self.assertEqual(result, (None, None))

        ## valid slug, invalid geo_mode
        result = gcnatnfs__geolocation(list(self.dictionary_ip.keys())[0], geo_mode='invalid-geo-mode')
        self.assertEqual(result, (None, None))

class TestSnort(TestCase):
    '''
    Test cases for the get_column_name_and_table_name_from_slug function.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.dictionary = {
            ## slug              column_name          table_name
            'time':             ('Time',             'timetoptable'),
            'gidsid':           ('GID:SID',          'gidsidtoptable'),
            'description':      ('Description',      'descriptiontoptable'),
            'classification':   ('Classification',   'classificationtoptable'),
            'priority':         ('Priority',         'prioritytoptable'),
            'protocol':         ('Protocol',         'protocoltoptable'),
            'source-ip':        ('Source IP',        'sourceiptoptable'),
            'source-port':      ('Source Port',      'sourceporttoptable'),
            'destination-ip':   ('Destination IP',   'destinationiptoptable'),
            'destination-port': ('Destination Port', 'destinationporttoptable'),

            'millisecond':      ('Millisecond',      'millisecondtoptable'),
            'level':            ('Level',            'leveltoptable'),
        }

    def test_valid_arguments(self):
        '''
        Test that valid slugs are correctly converted to column and table names.
        '''

        for slug, tup in self.dictionary.items():
            result = gcnatnfs__snort(slug)
            self.assertEqual(result, tup)

    def test_invalid_arguments(self):
        '''
        Test that invalid arguments return (None, None).
        '''

        result = gcnatnfs__snort('invalid-slug')
        self.assertEqual(result, (None, None))

class TestSquid(TestCase):
    '''
    Test cases for the get_column_name_and_table_name_from_slug function.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.dictionary = {
            ## slug             column_name         table_name
            'time':            ('Time',            'timetoptable'),
            # 'duration':        ('Duration',        'durationtoptable'),
            'source-ip':       ('Source IP',       'sourceiptoptable'),
            'request-status':  ('Request Status',  'requeststatustoptable'),
            'status-code':     ('Status Code',     'statuscodetoptable'),
            # 'transfer':        ('Transfer',        'transfertoptable'),
            'http-method':     ('HTTP Method',     'httpmethodtoptable'),
            'url':             ('URL',             'urltoptable'),
            'client-identity': ('Client Identity', 'clientidentitytoptable'),
            'peer-code':       ('Peer Code',       'peercodetoptable'),
            'destination-ip':  ('Destination IP',  'destinationiptoptable'),
            'content-type':    ('Content Type',    'contenttypetoptable'),

            'transfer-report': ('Transfer Report', 'iptransfertable'),
            'duration-report': ('Duration Report', 'ipdurationtable'),

            'millisecond':     ('Millisecond',     'millisecondtoptable'),
        }

    def test_valid_arguments(self):
        '''
        Test that valid slugs are correctly converted to column and table names.
        '''

        for slug, tup in self.dictionary.items():
            result = gcnatnfs__squid(slug)
            self.assertEqual(result, tup)

    def test_invalid_arguments(self):
        '''
        Test that invalid arguments return (None, None).
        '''

        result = gcnatnfs__squid('invalid-slug')
        self.assertEqual(result, (None, None))

class TestUserNotice(TestCase):
    '''
    Test cases for the get_column_name_and_table_name_from_slug function.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.dictionary = {
            ## slug            column_name        table_name
            'time':           ('Time',           'timetoptable'),
            'server':         ('Server',         'servertoptable'),
            'user':           ('User',           'usertoptable'),
            'destination-ip': ('Destination IP', 'destinationiptoptable'),
            'port':           ('Port',           'porttoptable'),
            'status':         ('Status',         'statustoptable'),

            'millisecond':    ('Millisecond',    'millisecondtoptable'),
        }

    def test_valid_arguments(self):
        '''
        Test that valid slugs are correctly converted to column and table names.
        '''

        for slug, tup in self.dictionary.items():
            result = gcnatnfs__usernotice(slug)
            self.assertEqual(result, tup)

    def test_invalid_arguments(self):
        '''
        Test that invalid arguments return (None, None).
        '''

        result = gcnatnfs__usernotice('invalid-slug')
        self.assertEqual(result, (None, None))

class TestWindowsServer(TestCase):
    '''
    Test cases for the get_column_name_and_table_name_from_slug function.
    '''

    def setUp(self):
        '''
        Set up test data for the test cases.
        '''
        self.dictionary = {
            ## slug                   column_name               table_name
            'time':                  ('Time',                  'timetoptable'),
            'event-id':              ('Event ID',              'eventidtoptable'),
            'category':              ('Category',              'categorytoptable'),
            'potential-criticality': ('Potential Criticality', 'potentialcriticalitytoptable'),
            'account-name':          ('Account Name',          'accountnametoptable'),
            'account-domain':        ('Account Domain',        'accountdomaintoptable'),
            'source-workstation':    ('Source Workstation',    'sourceworkstationtoptable'),

            'millisecond':           ('Millisecond',           'millisecondtoptable'),
        }

        self.categories = [
            'accountlogon',
            'accountmanagement',
            'detailedtracking',
            'dsaccess',
            'logonlogoff',
            'objectaccess',
            'policychange',
            'privilegeuse',
            'system',
            'miscellaneous',
        ]

    def test_valid_arguments(self):
        '''
        Test that valid slugs are correctly converted to column and table names.
        '''

        for slug, tup in self.dictionary.items():
            result = gcnatnfs__windowsserver(slug, category=None)
            self.assertEqual(result, tup)

        for category in self.categories:
            for slug, tup in self.dictionary.items():
                result = gcnatnfs__windowsserver(slug, category=category)
                if slug == 'millisecond':
                    self.assertEqual(result, (None, None))
                else:
                    _column_name, _table_name = tup
                    _table_name__new = f'{_table_name}{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{category}'

                    tup = (_column_name, _table_name__new)

                    self.assertEqual(result, tup)

    def test_invalid_arguments(self):
        '''
        Test that invalid arguments return (None, None).
        '''

        ## invalid slug, valid category
        result = gcnatnfs__windowsserver('invalid-slug', category=self.categories[1])
        self.assertEqual(result, (None, None))

        ## valid slug, invalid category
        result = gcnatnfs__windowsserver(list(self.dictionary.keys())[0], category='invalid-category')
        self.assertEqual(result, (None, None))

        ## valid slug, valid category
        ## NOTE:
        ##   - category is not None
        ##   - category is valid
        ##   - the slug 'millisecond' is not allowed
        ##     when category is not None and is valid
        result = gcnatnfs__windowsserver('millisecond', category=self.categories[1])
        self.assertEqual(result, (None, None))
