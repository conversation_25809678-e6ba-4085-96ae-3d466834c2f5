from django.core.exceptions import ValidationError
from django.db import models
from django.test import TestCase
from unittest.mock import patch, MagicMock

from base.models import (
    MethodsMixin,
    FireHOL,
    FirewallRule,
    Gateway,
    GetActiveObjects,
    Interface,
    Module,
    PublicIP,
    Router,
    RouterBoard,
    Sensor,
    StaticIP,
    Switch,
    VMware,
    WindowsServer,
    validate_has_allowed_chars,
    # validate_is_ip,
)


class TestModule(TestCase):
    '''
    Tests for the Module model.

    This test suite verifies that the Module model correctly implements
    its fields, methods, and behaviors.
    '''

    def test_model_fields(self):
        '''
        Test that the Module model has the expected fields with correct attributes.
        '''
        # Test name field
        name_field = Module._meta.get_field('name')
        self.assertIsInstance(name_field, models.CharField)
        self.assertEqual(name_field.max_length, 50)
        self.assertTrue(name_field.unique)
        self.assertIn(validate_has_allowed_chars, name_field.validators)

        # Test description field
        description_field = Module._meta.get_field('description')
        self.assertIsInstance(description_field, models.TextField)
        self.assertTrue(description_field.null)
        self.assertTrue(description_field.blank)

        # Test active field
        active_field = Module._meta.get_field('active')
        self.assertIsInstance(active_field, models.BooleanField)
        self.assertTrue(active_field.default)

        # Test short_uuid field
        short_uuid_field = Module._meta.get_field('short_uuid')
        self.assertIsInstance(short_uuid_field, models.CharField)
        self.assertEqual(short_uuid_field.max_length, 10)
        self.assertTrue(short_uuid_field.unique)
        self.assertFalse(short_uuid_field.editable)

        # Test created field
        created_field = Module._meta.get_field('created')
        self.assertIsInstance(created_field, models.DateTimeField)
        self.assertTrue(created_field.auto_now_add)

        # Test updated field
        updated_field = Module._meta.get_field('updated')
        self.assertIsInstance(updated_field, models.DateTimeField)
        self.assertTrue(updated_field.auto_now)

    def test_model_name_property(self):
        '''
        Test that the model_name property returns the class name.
        '''
        module = Module(name='Test Module')
        self.assertEqual(module.model_name, 'Module')

    @patch('base.models.Module.active_objects')
    def test_get_list_of_names(self, mock_active_objects):
        '''
        Test that get_list_of_names returns the expected list of names.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['Module1', 'Module2', 'Module3']

        # Call the method
        result = Module.get_list_of_names()

        # Verify the result
        self.assertEqual(result, ['Module1', 'Module2', 'Module3'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('name', flat=True)

    @patch('base.models.Module.active_objects')
    def test_get_list_of_names_empty(self, mock_active_objects):
        '''
        Test that get_list_of_names returns an empty list when there are no objects.
        '''
        # Setup mock
        mock_active_objects.all.return_value = None

        # Call the method
        result = Module.get_list_of_names()

        # Verify the result
        self.assertEqual(result, [])
        mock_active_objects.all.assert_called_once()

    def test_validate_has_allowed_chars_valid(self):
        '''
        Test that validate_has_allowed_chars accepts valid values.
        '''
        # These should not raise exceptions
        validate_has_allowed_chars('ValidName')
        validate_has_allowed_chars('valid-name-123')
        validate_has_allowed_chars('123')

    def test_validate_has_allowed_chars_invalid(self):
        '''
        Test that validate_has_allowed_chars rejects invalid values.
        '''
        # These should raise ValidationError
        with self.assertRaises(ValidationError):
            validate_has_allowed_chars('Invalid_Name')  # Contains underscore

        with self.assertRaises(ValidationError):
            validate_has_allowed_chars('Invalid Name')  # Contains space

        with self.assertRaises(ValidationError):
            validate_has_allowed_chars('Invalid@Name')  # Contains special character

class TestSensor(TestCase):
    '''
    Tests for the Sensor model.

    This test suite verifies that the Sensor model correctly implements
    its fields, methods, and behaviors.
    '''

    def test_model_fields(self):
        '''
        Test that the Sensor model has the expected fields with correct attributes.
        '''
        # Test name field
        name_field = Sensor._meta.get_field('name')
        self.assertIsInstance(name_field, models.CharField)
        self.assertEqual(name_field.max_length, 50)
        self.assertTrue(name_field.unique)
        self.assertIn(validate_has_allowed_chars, name_field.validators)

        # Test address field
        address_field = Sensor._meta.get_field('address')
        self.assertIsInstance(address_field, models.GenericIPAddressField)
        self.assertEqual(address_field.max_length, 39)
        self.assertTrue(address_field.unique)
        self.assertTrue(address_field.blank)
        self.assertTrue(address_field.null)
        # self.assertIn(validate_is_ip, address_field.validators)

        # Test description field
        description_field = Sensor._meta.get_field('description')
        self.assertIsInstance(description_field, models.TextField)
        self.assertTrue(description_field.null)
        self.assertTrue(description_field.blank)

        # Test active field
        active_field = Sensor._meta.get_field('active')
        self.assertIsInstance(active_field, models.BooleanField)
        self.assertTrue(active_field.default)

        # Test short_uuid field
        short_uuid_field = Sensor._meta.get_field('short_uuid')
        self.assertIsInstance(short_uuid_field, models.CharField)
        self.assertEqual(short_uuid_field.max_length, 10)
        self.assertTrue(short_uuid_field.unique)
        self.assertFalse(short_uuid_field.editable)

        # Test created field
        created_field = Sensor._meta.get_field('created')
        self.assertIsInstance(created_field, models.DateTimeField)
        self.assertTrue(created_field.auto_now_add)

        # Test updated field
        updated_field = Sensor._meta.get_field('updated')
        self.assertIsInstance(updated_field, models.DateTimeField)
        self.assertTrue(updated_field.auto_now)

    def test_model_name_property(self):
        '''
        Test that the model_name property returns the class name.
        '''
        sensor = Sensor(name='Test Sensor')
        self.assertEqual(sensor.model_name, 'Sensor')

    @patch('base.models.Sensor.active_objects')
    def test_get_list_of_names(self, mock_active_objects):
        '''
        Test that get_list_of_names returns the expected list of names.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['Sensor-1', 'Sensor-2', 'Sensor-3']

        # Call the method
        result = Sensor.get_list_of_names()

        # Verify the result
        self.assertEqual(result, ['Sensor-1', 'Sensor-2', 'Sensor-3'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('name', flat=True)

    @patch('base.models.Sensor.active_objects')
    def test_get_list_of_addresses(self, mock_active_objects):
        '''
        Test that get_list_of_addresses returns the expected list of addresses.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['***********', '***********']

        # Call the method
        result = Sensor.get_list_of_addresses()

        # Verify the result
        self.assertEqual(result, ['***********', '***********'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('address', flat=True)

class TestSwitch(TestCase):
    '''
    Tests for the Switch model.

    This test suite verifies that the Switch model correctly implements
    its fields, methods, and behaviors.
    '''

    def test_model_fields(self):
        '''
        Test that the Switch model has the expected fields with correct attributes.
        '''
        # Test name field
        name_field = Switch._meta.get_field('name')
        self.assertIsInstance(name_field, models.CharField)
        self.assertEqual(name_field.max_length, 50)
        self.assertTrue(name_field.unique)
        self.assertIn(validate_has_allowed_chars, name_field.validators)

        # Test address field
        address_field = Switch._meta.get_field('address')
        self.assertIsInstance(address_field, models.GenericIPAddressField)
        self.assertEqual(address_field.max_length, 39)
        self.assertTrue(address_field.unique)
        self.assertTrue(address_field.blank)
        self.assertTrue(address_field.null)
        # self.assertIn(validate_is_ip, address_field.validators)

        # Test description field
        description_field = Switch._meta.get_field('description')
        self.assertIsInstance(description_field, models.TextField)
        self.assertTrue(description_field.null)
        self.assertTrue(description_field.blank)

        # Test active field
        active_field = Switch._meta.get_field('active')
        self.assertIsInstance(active_field, models.BooleanField)
        self.assertTrue(active_field.default)

        # Test short_uuid field
        short_uuid_field = Switch._meta.get_field('short_uuid')
        self.assertIsInstance(short_uuid_field, models.CharField)
        self.assertEqual(short_uuid_field.max_length, 10)
        self.assertTrue(short_uuid_field.unique)
        self.assertFalse(short_uuid_field.editable)

        # Test created field
        created_field = Switch._meta.get_field('created')
        self.assertIsInstance(created_field, models.DateTimeField)
        self.assertTrue(created_field.auto_now_add)

        # Test updated field
        updated_field = Switch._meta.get_field('updated')
        self.assertIsInstance(updated_field, models.DateTimeField)
        self.assertTrue(updated_field.auto_now)

    def test_model_name_property(self):
        '''
        Test that the model_name property returns the class name.
        '''
        switch = Switch(name='Test Switch')
        self.assertEqual(switch.model_name, 'Switch')

    @patch('base.models.Switch.active_objects')
    def test_get_list_of_names(self, mock_active_objects):
        '''
        Test that get_list_of_names returns the expected list of names.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['Switch-1', 'Switch-2', 'Switch-3']

        # Call the method
        result = Switch.get_list_of_names()

        # Verify the result
        self.assertEqual(result, ['Switch-1', 'Switch-2', 'Switch-3'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('name', flat=True)

    @patch('base.models.Switch.active_objects')
    def test_get_list_of_addresses(self, mock_active_objects):
        '''
        Test that get_list_of_addresses returns the expected list of addresses.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['***********', '***********']

        # Call the method
        result = Switch.get_list_of_addresses()

        # Verify the result
        self.assertEqual(result, ['***********', '***********'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('address', flat=True)

class TestRouter(TestCase):
    '''
    Tests for the Router model.

    This test suite verifies that the Router model correctly implements
    its fields, methods, and behaviors.
    '''

    def test_model_fields(self):
        '''
        Test that the Router model has the expected fields with correct attributes.
        '''
        # Test name field
        name_field = Router._meta.get_field('name')
        self.assertIsInstance(name_field, models.CharField)
        self.assertEqual(name_field.max_length, 50)
        self.assertTrue(name_field.unique)
        self.assertIn(validate_has_allowed_chars, name_field.validators)

        # Test address field
        address_field = Router._meta.get_field('address')
        self.assertIsInstance(address_field, models.GenericIPAddressField)
        self.assertEqual(address_field.max_length, 39)
        self.assertTrue(address_field.unique)
        self.assertTrue(address_field.blank)
        self.assertTrue(address_field.null)
        # self.assertIn(validate_is_ip, address_field.validators)

        # Test description field
        description_field = Router._meta.get_field('description')
        self.assertIsInstance(description_field, models.TextField)
        self.assertTrue(description_field.null)
        self.assertTrue(description_field.blank)

        # Test active field
        active_field = Router._meta.get_field('active')
        self.assertIsInstance(active_field, models.BooleanField)
        self.assertTrue(active_field.default)

        # Test short_uuid field
        short_uuid_field = Router._meta.get_field('short_uuid')
        self.assertIsInstance(short_uuid_field, models.CharField)
        self.assertEqual(short_uuid_field.max_length, 10)
        self.assertTrue(short_uuid_field.unique)
        self.assertFalse(short_uuid_field.editable)

        # Test created field
        created_field = Router._meta.get_field('created')
        self.assertIsInstance(created_field, models.DateTimeField)
        self.assertTrue(created_field.auto_now_add)

        # Test updated field
        updated_field = Router._meta.get_field('updated')
        self.assertIsInstance(updated_field, models.DateTimeField)
        self.assertTrue(updated_field.auto_now)

    def test_model_name_property(self):
        '''
        Test that the model_name property returns the class name.
        '''
        router = Router(name='Test Router')
        self.assertEqual(router.model_name, 'Router')

    @patch('base.models.Router.active_objects')
    def test_get_list_of_names(self, mock_active_objects):
        '''
        Test that get_list_of_names returns the expected list of names.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['Router-1', 'Router-2', 'Router-3']

        # Call the method
        result = Router.get_list_of_names()

        # Verify the result
        self.assertEqual(result, ['Router-1', 'Router-2', 'Router-3'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('name', flat=True)

    @patch('base.models.Router.active_objects')
    def test_get_list_of_addresses(self, mock_active_objects):
        '''
        Test that get_list_of_addresses returns the expected list of addresses.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['***********', '***********']

        # Call the method
        result = Router.get_list_of_addresses()

        # Verify the result
        self.assertEqual(result, ['***********', '***********'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('address', flat=True)

class TestVMware(TestCase):
    '''
    Tests for the VMware model.

    This test suite verifies that the VMware model correctly implements
    its fields, methods, and behaviors.
    '''

    def test_model_fields(self):
        '''
        Test that the VMware model has the expected fields with correct attributes.
        '''
        # Test name field
        name_field = VMware._meta.get_field('name')
        self.assertIsInstance(name_field, models.CharField)
        self.assertEqual(name_field.max_length, 50)
        self.assertTrue(name_field.unique)
        self.assertIn(validate_has_allowed_chars, name_field.validators)

        # Test address field
        address_field = VMware._meta.get_field('address')

        self.assertIsInstance(address_field, models.GenericIPAddressField)
        self.assertEqual(address_field.max_length, 39)
        self.assertTrue(address_field.unique)
        self.assertTrue(address_field.blank)
        self.assertTrue(address_field.null)
        # self.assertIn(validate_is_ip, address_field.validators)

        # Test description field
        description_field = VMware._meta.get_field('description')
        self.assertIsInstance(description_field, models.TextField)
        self.assertTrue(description_field.null)
        self.assertTrue(description_field.blank)

        # Test active field
        active_field = VMware._meta.get_field('active')
        self.assertIsInstance(active_field, models.BooleanField)
        self.assertTrue(active_field.default)

        # Test short_uuid field
        short_uuid_field = VMware._meta.get_field('short_uuid')
        self.assertIsInstance(short_uuid_field, models.CharField)
        self.assertEqual(short_uuid_field.max_length, 10)
        self.assertTrue(short_uuid_field.unique)
        self.assertFalse(short_uuid_field.editable)

        # Test created field
        created_field = VMware._meta.get_field('created')
        self.assertIsInstance(created_field, models.DateTimeField)
        self.assertTrue(created_field.auto_now_add)

        # Test updated field
        updated_field = VMware._meta.get_field('updated')
        self.assertIsInstance(updated_field, models.DateTimeField)
        self.assertTrue(updated_field.auto_now)

    def test_model_name_property(self):
        '''
        Test that the model_name property returns the class name.
        '''
        vmware = VMware(name='Test VMware')
        self.assertEqual(vmware.model_name, 'VMware')

    @patch('base.models.VMware.active_objects')
    def test_get_list_of_names(self, mock_active_objects):
        '''
        Test that get_list_of_names returns the expected list of names.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['VMware-1', 'VMware-2', 'VMware-3']

        # Call the method
        result = VMware.get_list_of_names()

        # Verify the result
        self.assertEqual(result, ['VMware-1', 'VMware-2', 'VMware-3'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('name', flat=True)

    @patch('base.models.VMware.active_objects')
    def test_get_list_of_addresses(self, mock_active_objects):
        '''
        Test that get_list_of_addresses returns the expected list of addresses.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['***********', '***********']

        # Call the method
        result = VMware.get_list_of_addresses()

        # Verify the result
        self.assertEqual(result, ['***********', '***********'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('address', flat=True)

class TestWindowsServer(TestCase):
    '''
    Tests for the WindowsServer model.

    This test suite verifies that the WindowsServer model correctly implements
    its fields, methods, and behaviors.
    '''

    def test_model_fields(self):
        '''
        Test that the WindowsServer model has the expected fields with correct attributes.
        '''
        # Test name field
        name_field = WindowsServer._meta.get_field('name')
        self.assertIsInstance(name_field, models.CharField)
        self.assertEqual(name_field.max_length, 50)
        self.assertTrue(name_field.unique)
        self.assertIn(validate_has_allowed_chars, name_field.validators)

        # Test address field
        address_field = WindowsServer._meta.get_field('address')
        self.assertIsInstance(address_field, models.GenericIPAddressField)
        self.assertEqual(address_field.max_length, 39)
        self.assertTrue(address_field.unique)
        self.assertTrue(address_field.blank)
        self.assertTrue(address_field.null)
        # self.assertIn(validate_is_ip, address_field.validators)

        # Test description field
        description_field = WindowsServer._meta.get_field('description')
        self.assertIsInstance(description_field, models.TextField)
        self.assertTrue(description_field.null)
        self.assertTrue(description_field.blank)

        # Test active field
        active_field = WindowsServer._meta.get_field('active')
        self.assertIsInstance(active_field, models.BooleanField)
        self.assertTrue(active_field.default)

        # Test short_uuid field
        short_uuid_field = WindowsServer._meta.get_field('short_uuid')
        self.assertIsInstance(short_uuid_field, models.CharField)
        self.assertEqual(short_uuid_field.max_length, 10)
        self.assertTrue(short_uuid_field.unique)
        self.assertFalse(short_uuid_field.editable)

        # Test created field
        created_field = WindowsServer._meta.get_field('created')
        self.assertIsInstance(created_field, models.DateTimeField)
        self.assertTrue(created_field.auto_now_add)

        # Test updated field
        updated_field = WindowsServer._meta.get_field('updated')
        self.assertIsInstance(updated_field, models.DateTimeField)
        self.assertTrue(updated_field.auto_now)

    def test_model_name_property(self):
        '''
        Test that the model_name property returns the class name.
        '''
        windows_server = WindowsServer(name='Test WindowsServer')
        self.assertEqual(windows_server.model_name, 'WindowsServer')

    @patch('base.models.WindowsServer.active_objects')
    def test_get_list_of_names(self, mock_active_objects):
        '''
        Test that get_list_of_names returns the expected list of names.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['WindowsServer-1', 'WindowsServer-2', 'WindowsServer-3']

        # Call the method
        result = WindowsServer.get_list_of_names()

        # Verify the result
        self.assertEqual(result, ['WindowsServer-1', 'WindowsServer-2', 'WindowsServer-3'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('name', flat=True)

    @patch('base.models.WindowsServer.active_objects')
    def test_get_list_of_addresses(self, mock_active_objects):
        '''
        Test that get_list_of_addresses returns the expected list of addresses.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['***********', '***********']

        # Call the method
        result = WindowsServer.get_list_of_addresses()

        # Verify the result
        self.assertEqual(result, ['***********', '***********'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('address', flat=True)

class TestRouterBoard(TestCase):
    '''
    Tests for the RouterBoard model.

    This test suite verifies that the RouterBoard model correctly implements
    its fields, methods, and behaviors.
    '''

    def test_model_fields(self):
        '''
        Test that the RouterBoard model has the expected fields with correct attributes.
        '''
        # Test name field
        name_field = RouterBoard._meta.get_field('name')
        self.assertIsInstance(name_field, models.CharField)
        self.assertEqual(name_field.max_length, 50)
        self.assertTrue(name_field.unique)
        self.assertIn(validate_has_allowed_chars, name_field.validators)

        # Test address field
        address_field = RouterBoard._meta.get_field('address')
        self.assertIsInstance(address_field, models.GenericIPAddressField)
        self.assertEqual(address_field.max_length, 39)
        self.assertTrue(address_field.unique)
        self.assertTrue(address_field.blank)
        self.assertTrue(address_field.null)
        # self.assertIn(validate_is_ip, address_field.validators)

        # Test description field
        description_field = RouterBoard._meta.get_field('description')
        self.assertIsInstance(description_field, models.TextField)
        self.assertTrue(description_field.null)
        self.assertTrue(description_field.blank)

        # Test active field
        active_field = RouterBoard._meta.get_field('active')
        self.assertIsInstance(active_field, models.BooleanField)
        self.assertTrue(active_field.default)

        # Test short_uuid field
        short_uuid_field = RouterBoard._meta.get_field('short_uuid')
        self.assertIsInstance(short_uuid_field, models.CharField)
        self.assertEqual(short_uuid_field.max_length, 10)
        self.assertTrue(short_uuid_field.unique)
        self.assertFalse(short_uuid_field.editable)

        # Test created field
        created_field = RouterBoard._meta.get_field('created')
        self.assertIsInstance(created_field, models.DateTimeField)
        self.assertTrue(created_field.auto_now_add)

        # Test updated field
        updated_field = RouterBoard._meta.get_field('updated')
        self.assertIsInstance(updated_field, models.DateTimeField)
        self.assertTrue(updated_field.auto_now)

    def test_model_name_property(self):
        '''
        Test that the model_name property returns the class name.
        '''
        routerboard = RouterBoard(name='Test RouterBoard')
        self.assertEqual(routerboard.model_name, 'RouterBoard')

    @patch('base.models.RouterBoard.active_objects')
    def test_get_list_of_names(self, mock_active_objects):
        '''
        Test that get_list_of_names returns the expected list of names.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['RouterBoard-1', 'RouterBoard-2', 'RouterBoard-3']

        # Call the method
        result = RouterBoard.get_list_of_names()

        # Verify the result
        self.assertEqual(result, ['RouterBoard-1', 'RouterBoard-2', 'RouterBoard-3'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('name', flat=True)

    @patch('base.models.RouterBoard.active_objects')
    def test_get_list_of_addresses(self, mock_active_objects):
        '''
        Test that get_list_of_addresses returns the expected list of addresses.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['***********', '***********']

        # Call the method
        result = RouterBoard.get_list_of_addresses()

        # Verify the result
        self.assertEqual(result, ['***********', '***********'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('address', flat=True)

class TestGateway(TestCase):
    '''
    Tests for the Gateway model.

    This test suite verifies that the Gateway model correctly implements
    its fields, methods, and behaviors.
    '''

    def test_model_fields(self):
        '''
        Test that the Gateway model has the expected fields with correct attributes.
        '''
        # Test name field
        name_field = Gateway._meta.get_field('name')
        self.assertIsInstance(name_field, models.CharField)
        self.assertEqual(name_field.max_length, 50)
        self.assertTrue(name_field.unique)

        # Test address field
        address_field = Gateway._meta.get_field('address')
        self.assertIsInstance(address_field, models.GenericIPAddressField)
        self.assertEqual(address_field.max_length, 39)
        self.assertTrue(address_field.unique)
        self.assertTrue(address_field.blank)
        self.assertTrue(address_field.null)
        # self.assertIn(validate_is_ip, address_field.validators)

        # Test description field
        description_field = Gateway._meta.get_field('description')
        self.assertIsInstance(description_field, models.TextField)
        self.assertTrue(description_field.null)
        self.assertTrue(description_field.blank)

        # Test active field
        active_field = Gateway._meta.get_field('active')
        self.assertIsInstance(active_field, models.BooleanField)
        self.assertTrue(active_field.default)

        # Test short_uuid field
        short_uuid_field = Gateway._meta.get_field('short_uuid')
        self.assertIsInstance(short_uuid_field, models.CharField)
        self.assertEqual(short_uuid_field.max_length, 10)
        self.assertTrue(short_uuid_field.unique)
        self.assertFalse(short_uuid_field.editable)

        # Test created field
        created_field = Gateway._meta.get_field('created')
        self.assertIsInstance(created_field, models.DateTimeField)
        self.assertTrue(created_field.auto_now_add)

        # Test updated field
        updated_field = Gateway._meta.get_field('updated')
        self.assertIsInstance(updated_field, models.DateTimeField)
        self.assertTrue(updated_field.auto_now)

    def test_model_name_property(self):
        '''
        Test that the model_name property returns the class name.
        '''
        gateway = Gateway(name='Test Gateway')
        self.assertEqual(gateway.model_name, 'Gateway')

    @patch('base.models.Gateway.active_objects')
    def test_get_list_of_names(self, mock_active_objects):
        '''
        Test that get_list_of_names returns the expected list of names.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['Gateway-1', 'Gateway-2', 'Gateway-3']

        # Call the method
        result = Gateway.get_list_of_names()

        # Verify the result
        self.assertEqual(result, ['Gateway-1', 'Gateway-2', 'Gateway-3'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('name', flat=True)

    @patch('base.models.Gateway.active_objects')
    def test_get_list_of_addresses(self, mock_active_objects):
        '''
        Test that get_list_of_addresses returns the expected list of addresses.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['***********', '***********']

        # Call the method
        result = Gateway.get_list_of_addresses()

        # Verify the result
        self.assertEqual(result, ['***********', '***********'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('address', flat=True)

class TestInterface(TestCase):
    '''
    Tests for the Interface model.

    This test suite verifies that the Interface model correctly implements
    its fields, methods, and behaviors.
    '''

    def test_model_fields(self):
        '''
        Test that the Interface model has the expected fields with correct attributes.
        '''
        # Test real_interface field
        real_interface_field = Interface._meta.get_field('real_interface')
        self.assertIsInstance(real_interface_field, models.CharField)
        self.assertEqual(real_interface_field.max_length, 50)
        self.assertFalse(real_interface_field.unique)

        # Test interface field
        interface_field = Interface._meta.get_field('interface')
        self.assertIsInstance(interface_field, models.CharField)
        self.assertEqual(interface_field.max_length, 50)
        self.assertFalse(interface_field.unique)

        # Test sensors field
        sensors_field = Interface._meta.get_field('sensors')
        self.assertIsInstance(sensors_field, models.ManyToManyField)
        self.assertTrue(sensors_field.blank)

        # Test description field
        description_field = Interface._meta.get_field('description')
        self.assertIsInstance(description_field, models.TextField)
        self.assertTrue(description_field.null)
        self.assertTrue(description_field.blank)

        # Test active field
        active_field = Interface._meta.get_field('active')
        self.assertIsInstance(active_field, models.BooleanField)
        self.assertTrue(active_field.default)

        # Test short_uuid field
        short_uuid_field = Interface._meta.get_field('short_uuid')
        self.assertIsInstance(short_uuid_field, models.CharField)
        self.assertEqual(short_uuid_field.max_length, 10)
        self.assertTrue(short_uuid_field.unique)
        self.assertFalse(short_uuid_field.editable)

        # Test created field
        created_field = Interface._meta.get_field('created')
        self.assertIsInstance(created_field, models.DateTimeField)
        self.assertTrue(created_field.auto_now_add)

        # Test updated field
        updated_field = Interface._meta.get_field('updated')
        self.assertIsInstance(updated_field, models.DateTimeField)
        self.assertTrue(updated_field.auto_now)

    def test_sensors_property(self):
        '''
        Test that the sensors__ property returns the expected string.
        '''
        # Create test objects
        interface = Interface(real_interface='em1', interface='LAN')
        interface.save()

        sensor1 = Sensor(name='Sensor-1')
        sensor1.save()
        sensor2 = Sensor(name='Sensor-2')
        sensor2.save()

        # Add sensors to interface
        interface.sensors.add(sensor1, sensor2)

        # Test the property
        self.assertEqual(interface.sensors__, 'Sensor-1, Sensor-2')

class TestStaticIP(TestCase):
    '''
    Tests for the StaticIP model.

    This test suite verifies that the StaticIP model correctly implements
    its fields, methods, and behaviors.
    '''

    def test_model_fields(self):
        '''
        Test that the StaticIP model has the expected fields with correct attributes.
        '''
        # Test computer_name field
        computer_name_field = StaticIP._meta.get_field('computer_name')
        self.assertIsInstance(computer_name_field, models.CharField)
        self.assertEqual(computer_name_field.max_length, 50)
        self.assertTrue(computer_name_field.unique)

        # Test real_name field
        real_name_field = StaticIP._meta.get_field('real_name')
        self.assertIsInstance(real_name_field, models.CharField)
        self.assertEqual(real_name_field.max_length, 50)
        self.assertFalse(real_name_field.unique)

        # Test address field
        address_field = StaticIP._meta.get_field('address')
        self.assertIsInstance(address_field, models.GenericIPAddressField)
        self.assertEqual(address_field.max_length, 39)
        self.assertTrue(address_field.unique)
        self.assertTrue(address_field.blank)
        self.assertTrue(address_field.null)
        # self.assertIn(validate_is_ip, address_field.validators)

        # Test virtual_address field
        virtual_address_field = StaticIP._meta.get_field('virtual_address')
        self.assertIsInstance(virtual_address_field, models.GenericIPAddressField)
        self.assertEqual(virtual_address_field.max_length, 39)
        self.assertTrue(virtual_address_field.unique)
        self.assertTrue(virtual_address_field.blank)
        self.assertTrue(virtual_address_field.null)
        # self.assertIn(validate_is_ip, virtual_address_field.validators)

        # Test description field
        description_field = StaticIP._meta.get_field('description')
        self.assertIsInstance(description_field, models.TextField)
        self.assertTrue(description_field.null)
        self.assertTrue(description_field.blank)

        # Test active field
        active_field = StaticIP._meta.get_field('active')
        self.assertIsInstance(active_field, models.BooleanField)
        self.assertTrue(active_field.default)

        # Test short_uuid field
        short_uuid_field = StaticIP._meta.get_field('short_uuid')
        self.assertIsInstance(short_uuid_field, models.CharField)
        self.assertEqual(short_uuid_field.max_length, 10)
        self.assertTrue(short_uuid_field.unique)
        self.assertFalse(short_uuid_field.editable)

        # Test created field
        created_field = StaticIP._meta.get_field('created')
        self.assertIsInstance(created_field, models.DateTimeField)
        self.assertTrue(created_field.auto_now_add)

        # Test updated field
        updated_field = StaticIP._meta.get_field('updated')
        self.assertIsInstance(updated_field, models.DateTimeField)
        self.assertTrue(updated_field.auto_now)

    def test_meta_verbose_name_plural(self):
        '''
        Test that the Meta class has the expected verbose_name_plural.
        '''
        self.assertEqual(StaticIP._meta.verbose_name_plural, 'Static IPs')

class TestPublicIP(TestCase):
    '''
    Tests for the PublicIP model.

    This test suite verifies that the PublicIP model correctly implements
    its fields, methods, and behaviors.
    '''

    def test_model_fields(self):
        '''
        Test that the PublicIP model has the expected fields with correct attributes.
        '''
        # Test address field
        address_field = PublicIP._meta.get_field('address')
        self.assertIsInstance(address_field, models.GenericIPAddressField)
        self.assertEqual(address_field.max_length, 39)
        self.assertTrue(address_field.unique)
        # self.assertIn(validate_is_ip, address_field.validators)

        # Test description field
        description_field = PublicIP._meta.get_field('description')
        self.assertIsInstance(description_field, models.TextField)
        self.assertTrue(description_field.null)
        self.assertTrue(description_field.blank)

        # Test active field
        active_field = PublicIP._meta.get_field('active')
        self.assertIsInstance(active_field, models.BooleanField)
        self.assertTrue(active_field.default)

        # Test short_uuid field
        short_uuid_field = PublicIP._meta.get_field('short_uuid')
        self.assertIsInstance(short_uuid_field, models.CharField)
        self.assertEqual(short_uuid_field.max_length, 10)
        self.assertTrue(short_uuid_field.unique)
        self.assertFalse(short_uuid_field.editable)

        # Test created field
        created_field = PublicIP._meta.get_field('created')
        self.assertIsInstance(created_field, models.DateTimeField)
        self.assertTrue(created_field.auto_now_add)

        # Test updated field
        updated_field = PublicIP._meta.get_field('updated')
        self.assertIsInstance(updated_field, models.DateTimeField)
        self.assertTrue(updated_field.auto_now)

    def test_meta_verbose_name_plural(self):
        '''
        Test that the Meta class has the expected verbose_name_plural.
        '''
        self.assertEqual(PublicIP._meta.verbose_name_plural, 'Public IPs')

    def test_model_name_property(self):
        '''
        Test that the model_name property returns the class name.
        '''
        public_ip = PublicIP(address='123.456.789.0')
        self.assertEqual(public_ip.model_name, 'PublicIP')

    @patch('base.models.PublicIP.active_objects')
    def test_get_list_of_addresses(self, mock_active_objects):
        '''
        Test that get_list_of_addresses returns the expected list of addresses.
        '''
        # Setup mock
        mock_queryset = MagicMock()
        mock_active_objects.all.return_value = mock_queryset
        mock_queryset.values_list.return_value = ['123.456.789.0', '123.456.789.1']

        # Call the method
        result = PublicIP.get_list_of_addresses()

        # Verify the result
        self.assertEqual(result, ['123.456.789.0', '123.456.789.1'])
        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('address', flat=True)

class TestFirewallRule(TestCase):
    '''
    Tests for the FirewallRule model.

    This test suite verifies that the FirewallRule model correctly implements
    its fields, methods, and behaviors.
    '''

    def setUp(self):
        '''
        Set up test data for FirewallRule tests.
        '''
        # Create a test interface for foreign key relationship
        self.test_interface = Interface.objects.create(
            real_interface='em1',
            interface='LAN'
        )

    def test_model_fields(self):
        '''
        Test that the FirewallRule model has the expected fields with correct attributes.
        '''
        # Test tracking_id field
        tracking_id_field = FirewallRule._meta.get_field('tracking_id')
        self.assertIsInstance(tracking_id_field, models.IntegerField)
        self.assertTrue(tracking_id_field.unique)
        self.assertEqual(tracking_id_field.verbose_name, 'Tracking ID')

        # Test name field
        name_field = FirewallRule._meta.get_field('name')
        self.assertIsInstance(name_field, models.CharField)
        self.assertEqual(name_field.max_length, 20)
        self.assertFalse(name_field.unique)  # Explicitly NOT unique according to comments
        self.assertEqual(name_field.help_text, 'e.g. APAC1200')

        # Test interface field (ForeignKey)
        interface_field = FirewallRule._meta.get_field('interface')
        self.assertIsInstance(interface_field, models.ForeignKey)
        self.assertTrue(interface_field.null)
        self.assertEqual(interface_field.related_model, Interface)
        self.assertEqual(interface_field.remote_field.on_delete, models.SET_NULL)
        self.assertEqual(interface_field.remote_field.related_name, 'firewallrule_interface_rel')

        # Test description field
        description_field = FirewallRule._meta.get_field('description')
        self.assertIsInstance(description_field, models.TextField)
        self.assertTrue(description_field.null)
        self.assertTrue(description_field.blank)

        # Test active field
        active_field = FirewallRule._meta.get_field('active')
        self.assertIsInstance(active_field, models.BooleanField)
        self.assertTrue(active_field.default)

        # Test short_uuid field
        short_uuid_field = FirewallRule._meta.get_field('short_uuid')
        self.assertIsInstance(short_uuid_field, models.CharField)
        self.assertEqual(short_uuid_field.max_length, 10)
        self.assertTrue(short_uuid_field.unique)
        self.assertFalse(short_uuid_field.editable)
        self.assertEqual(short_uuid_field.verbose_name, 'Short UUID')

        # Test created field
        created_field = FirewallRule._meta.get_field('created')
        self.assertIsInstance(created_field, models.DateTimeField)
        self.assertTrue(created_field.auto_now_add)

        # Test updated field
        updated_field = FirewallRule._meta.get_field('updated')
        self.assertIsInstance(updated_field, models.DateTimeField)
        self.assertTrue(updated_field.auto_now)

    def test_meta_verbose_name_plural(self):
        '''
        Test that the Meta class has the expected verbose_name_plural.
        '''
        self.assertEqual(FirewallRule._meta.verbose_name_plural, 'Firewall Rules')

    def test_interface_property(self):
        '''
        Test the interface__ property returns the interface's interface field.
        '''
        firewall_rule = FirewallRule.objects.create(
            tracking_id=12345,
            name='APAC1200',
            interface=self.test_interface
        )
        self.assertEqual(firewall_rule.interface__, 'LAN')

    def test_interface_property_with_null_interface(self):
        '''
        Test the interface__ property when interface is None.
        '''
        firewall_rule = FirewallRule.objects.create(
            tracking_id=12345,
            name='APAC1200',
            interface=None
        )
        # This should raise an AttributeError since interface is None
        with self.assertRaises(AttributeError):
            _ = firewall_rule.interface__

    def test_create_firewall_rule_with_interface(self):
        '''
        Test creating a FirewallRule with an interface.
        '''
        firewall_rule = FirewallRule.objects.create(
            tracking_id=12345,
            name='APAC1200',
            interface=self.test_interface,
            description='Test firewall rule'
        )

        self.assertEqual(firewall_rule.tracking_id, 12345)
        self.assertEqual(firewall_rule.name, 'APAC1200')
        self.assertEqual(firewall_rule.interface, self.test_interface)
        self.assertEqual(firewall_rule.description, 'Test firewall rule')
        self.assertTrue(firewall_rule.active)
        self.assertIsNotNone(firewall_rule.short_uuid)
        self.assertIsNotNone(firewall_rule.created)
        self.assertIsNotNone(firewall_rule.updated)

    def test_create_firewall_rule_without_interface(self):
        '''
        Test creating a FirewallRule without an interface (null=True).
        '''
        firewall_rule = FirewallRule.objects.create(
            tracking_id=54321,
            name='APAC5400',
            interface=None
        )

        self.assertEqual(firewall_rule.tracking_id, 54321)
        self.assertEqual(firewall_rule.name, 'APAC5400')
        self.assertIsNone(firewall_rule.interface)
        self.assertTrue(firewall_rule.active)

    def test_tracking_id_uniqueness(self):
        '''
        Test that tracking_id must be unique.
        '''
        FirewallRule.objects.create(
            tracking_id=12345,
            name='APAC1200'
        )

        # Attempting to create another with the same tracking_id should fail
        with self.assertRaises(Exception):  # IntegrityError in practice
            FirewallRule.objects.create(
                tracking_id=12345,
                name='APAC5400'
            )

    def test_name_not_unique(self):
        '''
        Test that name field is not unique (multiple rules can have same name).
        '''
        FirewallRule.objects.create(
            tracking_id=12345,
            name='APAC1200'
        )

        # Should be able to create another with the same name but different tracking_id
        firewall_rule2 = FirewallRule.objects.create(
            tracking_id=54321,
            name='APAC1200'
        )

        self.assertEqual(firewall_rule2.name, 'APAC1200')
        self.assertEqual(firewall_rule2.tracking_id, 54321)

    def test_managers(self):
        '''
        Test that the model has the expected managers.
        '''
        # Test that objects manager exists
        self.assertTrue(hasattr(FirewallRule, 'objects'))

        # Test that active_objects manager exists
        self.assertTrue(hasattr(FirewallRule, 'active_objects'))
        self.assertIsInstance(FirewallRule.active_objects, GetActiveObjects)

    def test_active_objects_manager(self):
        '''
        Test that the active_objects manager filters correctly.
        '''
        # Create active and inactive firewall rules
        active_rule = FirewallRule.objects.create(
            tracking_id=12345,
            name='Active Rule',
            active=True
        )

        inactive_rule = FirewallRule.objects.create(
            tracking_id=54321,
            name='Inactive Rule',
            active=False
        )

        # Test that active_objects only returns active rules
        active_rules = FirewallRule.active_objects.all()
        self.assertIn(active_rule, active_rules)
        self.assertNotIn(inactive_rule, active_rules)

        # Test that objects manager returns all rules
        all_rules = FirewallRule.objects.all()
        self.assertIn(active_rule, all_rules)
        self.assertIn(inactive_rule, all_rules)

    def test_common_methods_inheritance(self):
        '''
        Test that FirewallRule inherits from MethodsMixin.
        '''
        self.assertTrue(issubclass(FirewallRule, MethodsMixin))

        # Test model_name property
        firewall_rule = FirewallRule(tracking_id=12345, name='Test')
        self.assertEqual(firewall_rule.model_name, 'FirewallRule')

    @patch('base.models.FirewallRule.active_objects')
    def test_get_list_of_names(self, mock_active_objects):
        '''
        Test the get_list_of_names class method from MethodsMixin.
        '''
        # Mock the queryset and values_list
        mock_queryset = MagicMock()
        mock_queryset.values_list.return_value = ['APAC1200', 'APAC5400', 'APAC100']
        mock_active_objects.all.return_value = mock_queryset

        result = FirewallRule.get_list_of_names()

        # Should return naturally sorted list
        expected = ['APAC100', 'APAC1200', 'APAC5400']  # Natural sort order
        self.assertEqual(result, expected)

        mock_active_objects.all.assert_called_once()
        mock_queryset.values_list.assert_called_once_with('name', flat=True)

    @patch('base.models.FirewallRule.active_objects')
    def test_get_list_of_names_empty(self, mock_active_objects):
        '''
        Test get_list_of_names when no objects exist.
        '''
        mock_active_objects.all.return_value = None

        result = FirewallRule.get_list_of_names()
        self.assertEqual(result, [])

    def test_interface_on_delete_set_null(self):
        '''
        Test that when an interface is deleted, the firewall rule's interface is set to NULL.
        '''
        firewall_rule = FirewallRule.objects.create(
            tracking_id=12345,
            name='APAC1200',
            interface=self.test_interface
        )

        # Verify the relationship is established
        self.assertEqual(firewall_rule.interface, self.test_interface)

        # Delete the interface
        interface_id = self.test_interface.id
        self.test_interface.delete()

        # Refresh the firewall rule from database
        firewall_rule.refresh_from_db()

        # The interface should now be None due to SET_NULL
        self.assertIsNone(firewall_rule.interface)

    def test_related_name_functionality(self):
        '''
        Test that the related_name 'firewallrule_interface_rel' works correctly.
        '''
        firewall_rule1 = FirewallRule.objects.create(
            tracking_id=12345,
            name='APAC1200',
            interface=self.test_interface
        )

        firewall_rule2 = FirewallRule.objects.create(
            tracking_id=54321,
            name='APAC5400',
            interface=self.test_interface
        )

        # Test reverse relationship
        related_rules = self.test_interface.firewallrule_interface_rel.all()
        self.assertIn(firewall_rule1, related_rules)
        self.assertIn(firewall_rule2, related_rules)
        self.assertEqual(related_rules.count(), 2)

class TestFireHOL(TestCase):
    '''
    Tests for the FireHOL model.

    This test suite verifies that the FireHOL model correctly implements
    its fields, methods, and behaviors.
    '''

    def test_model_fields(self):
        '''
        Test that the FireHOL model has the expected fields with correct attributes.
        '''
        # Test name field
        name_field = FireHOL._meta.get_field('name')
        self.assertIsInstance(name_field, models.CharField)
        self.assertEqual(name_field.max_length, 50)
        self.assertTrue(name_field.unique)

        # Test url field
        url_field = FireHOL._meta.get_field('url')
        self.assertIsInstance(url_field, models.CharField)
        self.assertEqual(url_field.max_length, 200)
        self.assertTrue(url_field.unique)

        # Test description field
        description_field = FireHOL._meta.get_field('description')
        self.assertIsInstance(description_field, models.TextField)
        self.assertTrue(description_field.null)
        self.assertTrue(description_field.blank)

        # Test active field
        active_field = FireHOL._meta.get_field('active')
        self.assertIsInstance(active_field, models.BooleanField)
        self.assertTrue(active_field.default)

        # Test short_uuid field
        short_uuid_field = FireHOL._meta.get_field('short_uuid')
        self.assertIsInstance(short_uuid_field, models.CharField)
        self.assertEqual(short_uuid_field.max_length, 10)
        self.assertTrue(short_uuid_field.unique)
        self.assertFalse(short_uuid_field.editable)

        # Test created field
        created_field = FireHOL._meta.get_field('created')
        self.assertIsInstance(created_field, models.DateTimeField)
        self.assertTrue(created_field.auto_now_add)

        # Test updated field
        updated_field = FireHOL._meta.get_field('updated')
        self.assertIsInstance(updated_field, models.DateTimeField)
        self.assertTrue(updated_field.auto_now)

    def test_meta_verbose_name_plural(self):
        '''
        Test that the Meta class has the expected verbose_name_plural.
        '''
        self.assertEqual(FireHOL._meta.verbose_name_plural, 'FireHOLs')
