from django import template
from django.utils.html import strip_tags

from datetime import datetime
from functools import lru_cache
from json import loads
from operator import itemgetter
from re import sub

from rahavard import (
    convert_byte as convert_byte_,
    create_id_for_htmx_indicator as create_id_for_htmx_indicator_,
    get_percent as get_percent_,
    sort_dict as sort_dict_,
)

from base.utils_classes import (
    EVENT_TYPES__CRITICALS,
    EVENT_TYPES__WARNINGS,

    DaemonConfig,
    SnortConfig,
    WindowsServerConfig,
)

from base.utils import (
    LRU_CACHE_MAXSIZE,
    get_sum_of_values as get_sum_of_values_,
    quote_domain as quote_domain_,
    trim_dict as trim_dict_,
)

from base.models import FireHOL


register = template.Library()


## JUMP_1 (https://stackoverflow.com/a/77605139/)
## usage in *html to save returned result as variable:
##   {% join_strings "string1" "string2" as title %}
## NOTE also used in ariel and nova
@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.simple_tag
def join_strings(*args):
    return ''.join(args)

## JUMP_1
@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.simple_tag
def create_id_for_htmx_indicator(*args):
    return create_id_for_htmx_indicator_(*args)

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.simple_tag
def calculate_id_of_visits_table(limit_to_show, page_number, forloop_counter, to_comma=True):
    _id = ((page_number - 1) * limit_to_show) + forloop_counter
    if to_comma:
        return f'{_id:,}'
    return _id

@register.simple_tag
def dict_to_comma_joined_str(dict_, section, is_for=None):

    if section == 'keys':
        return ','.join(map(str, list(dict_.keys())))

    if section == 'values':

        ## dict_ is called main and has a structure of its own
        if is_for and is_for == 'Trends Sparkline':
            list_ = [get_sum_of_values(v) for k, v in dict_.items()]
            ## '--> [10272838, 3209400, 2495430, 291676, ...]
            return ','.join(map(str, list_))

        ## dict_ is called loss_dict and has a structure of its own
        elif is_for and is_for == 'Packet Loss':
            list_ = [get_total_packet_loss(v) for k, v in dict_.items()]
            ## '--> [38, 3, 24, 9, ...]

            return ','.join(map(str, list_))

        return ','.join(map(str, list(dict_.values())))

    return dict_

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.simple_tag
def get_percent(smaller_number, total_number):
    return get_percent_(smaller_number, total_number)

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.simple_tag
def displaying(per_page, page_no, total):
    if total == 0:
        return total

    start = 1
    end = per_page

    if page_no > 1:
        for i in range(page_no - 1):
            start += per_page

        end = start + per_page - 1

    if end > total:
        end = total

    return f'{start:,}-{end:,} of {total:,}'

@register.filter
def get_total_packet_loss(rows):
    '''
        takes rows, which is
        [('GW1', 268, 22, 100, 99), ('GW2', 0, '', '', ''), ...]
        or
        [('GW1', 0, '', '', ''), ('GW2', 0, '', '', ''), ...]

        returns the sum of second items (which is loss count)
    '''

    return sum(map(itemgetter(1), rows))  ## 18

## replica of dashify in utils.js
@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.filter
def dashify(string):
    '''
        ************      -> 10-19-38-250      (ip)
        2001:500:856e::30 -> 2001-500-856e--30 (ip)
        igb2.25           -> igb2-25           (real interface)
        edge.skype.com    -> edge-skype-com    (domain)

        replaces anything other than a letter or a number with -
        for string to be able to be used
        as an element id in the html page
    '''
    return sub('[^a-zA-Z0-9]+', '-', string)

@register.filter
def get_sum_of_values(dic):
    return get_sum_of_values_(dic)

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.filter
def to_short_weekday(ymd):
    ''' 2024-04-12 -> Fri '''
    return datetime.strptime(ymd, '%Y-%m-%d').strftime('%a')

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.filter
def to_long_weekday(ymd):
    ''' 2024-04-12 -> Friday '''
    return datetime.strptime(ymd, '%Y-%m-%d').strftime('%A')

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.filter
def days_ago(ymd):
    try:
        now = datetime.now()
        before = datetime.strptime(ymd, '%Y-%m-%d')
        before_weekday = before.strftime('%a')
        delta = now - before

        delta_days = delta.days

        ## NOTE the structure of what's returned
        ##      is also used in days_ago function
        ##      in set-cookies.js

        if delta_days == 0:
            return 'today'

        if delta_days == 1:
            return f'{before_weekday}, yesterday'

        if delta_days < 1:
            if delta_days == -1:
                return f'{before_weekday}, tomorrow'
            else:
                return f'{before_weekday}, {abs(delta_days)} days from now'


        return f'{before_weekday}, {delta_days} days ago'

    except Exception:
        return None

@register.filter
def get_value_of_dict_key(dic, key):
    if not isinstance(dic, dict):
        return ''
    return dic.get(key, '')

@register.filter
def trim_list(list_, limit):
    return list_[:limit]

@register.filter
def list_to_comma_joined_str(list_):
    return ','.join(map(str, list_))

@register.filter
def list_to_joined_str(list_, join_by):
    return f'{join_by}'.join(map(str, list_))

@register.filter
def trim_dict(dict_, limit):
    return trim_dict_(dict_, limit)

@register.filter
def make_dict_ascending(dict_):
    return sort_dict_(dict_, based_on='key', reverse=False)

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.filter
def expand_sources(string):
    '''
        gets '[3, 4, 16, 22, 42, 44, 49, 55, 56, 62]' which is str
        turns it into list
        finds and returns FireHOL objects corresponding to the ids
    '''

    ## '[3, 4, 22, <mark>27</mark>]'
    ## ->
    ## '[3, 4, 22, 27]'
    string = strip_tags(string)

    return FireHOL.active_objects.filter(id__in=loads(string))

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.filter
def dumped_destination_ips_to_list(string):
    '''
        gets '["*******", ...]' which is str
        turns it into list
    '''

    ## '[3, 4, 22, <mark>27</mark>]'
    ## ->
    ## '[3, 4, 22, 27]'
    string = strip_tags(string)

    return loads(string)

@register.filter
def get_nth_element_of_list(list_, index):
    return list_[index]

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.filter
def convert_byte(size):
    return convert_byte_(size)

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.filter
def quote_domain(domain):
    return quote_domain_(domain)


_LIST_OF_CRITICALS = [
    ## classification
    *SnortConfig.CLASSIFICATIONS__CRITICALS.value,

    ## event type
    *EVENT_TYPES__CRITICALS,

    ## daemon
    *DaemonConfig.EVENT_TYPES__CRITICALS.value,
]
##
_LIST_OF_WARNINGS = [
    ## classification
    *SnortConfig.CLASSIFICATIONS__WARNINGS.value,

    ## event type
    *EVENT_TYPES__WARNINGS,

    ## daemon
    *DaemonConfig.EVENT_TYPES__WARNINGS.value,
]
##
## __NO_CACHE__
@register.filter
def is_warning_or_critical_line(ln):
    '''
    ln is a full line received from live monitor
    having been read off of .log file
    '''

    ## snort priority
    if 'Priority: 1' in ln:
        return 'is_critical'
    if 'Priority: 2' in ln:
        return 'is_warning'

    for item in _LIST_OF_CRITICALS:
        if item in ln:
            return 'is_critical'

    for item in _LIST_OF_WARNINGS:
        if item in ln:
            return 'is_warning'

    return False

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.filter
def is_warning_or_critical_event_id(event_id):
    if event_id in WindowsServerConfig.EVENT_IDS__HIGH.value:
        return 'is_critical'

    if event_id in WindowsServerConfig.EVENT_IDS__MEDIUM.value:
        return 'is_warning'

    return False

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.filter
def is_warning_or_critical_potential_criticality(potential_criticality):
    if potential_criticality in WindowsServerConfig.HIGH_POTENTIAL_CRITICALITIES.value:
        return 'is_critical'

    if potential_criticality in WindowsServerConfig.MEDIUM_POTENTIAL_CRITICALITIES.value:
        return 'is_warning'

    return False

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.filter
def is_warning_or_critical_level(level):
    if level == 'Critical':
        return 'is_critical'

    if level == 'Warning':
        return 'is_warning'

    return False

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.filter
def is_warning_or_critical_classification(classification):
    if classification in SnortConfig.CLASSIFICATIONS__CRITICALS.value:
        return 'is_critical'

    if classification in SnortConfig.CLASSIFICATIONS__WARNINGS.value:
        return 'is_warning'

    return False

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.filter
def is_warning_or_critical_priority(priority):
    if priority in ['1', 1]:
        return 'is_critical'

    if priority in ['2', 2]:
        return 'is_warning'

    return False

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.filter
def is_warning_or_critical_event_type(event_type):
    if event_type in EVENT_TYPES__CRITICALS:
        return 'is_critical'

    if event_type in EVENT_TYPES__WARNINGS:
        return 'is_warning'

    return False

@lru_cache(maxsize=LRU_CACHE_MAXSIZE)
@register.filter
def is_warning_or_critical_event_type__daemon(event_type):
    if event_type in DaemonConfig.EVENT_TYPES__CRITICALS.value:
        return 'is_critical'

    if event_type in DaemonConfig.EVENT_TYPES__WARNINGS.value:
        return 'is_warning'

    return False
