# Generated by Django 5.2.3 on 2025-07-10 22:21

import base.models
import django.db.models.deletion
import rahavard.utils
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='FireHOL',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True, null=True)),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('short_uuid', models.CharField(db_index=True, default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('name', models.Char<PERSON>ield(help_text='e.g. GitHub Source 1, etc.', max_length=50, unique=True)),
                ('url', models.URLField(help_text='e.g. https://example.com/hosts.txt', unique=True, verbose_name='URL')),
            ],
            options={
                'verbose_name_plural': 'FireHOLs',
                'abstract': False,
            },
            bases=(models.Model, base.models.MethodsMixin),
        ),
        migrations.CreateModel(
            name='Gateway',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.GenericIPAddressField(blank=True, db_index=True, help_text='e.g. ***********', null=True, protocol='IPv4', unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('short_uuid', models.CharField(db_index=True, default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(help_text='e.g. GW One, GW-One, etc.', max_length=50, unique=True)),
            ],
            options={
                'abstract': False,
            },
            bases=(models.Model, base.models.MethodsMixin),
        ),
        migrations.CreateModel(
            name='Interface',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True, null=True)),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('short_uuid', models.CharField(db_index=True, default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('real_interface', models.CharField(db_index=True, help_text='e.g. em1', max_length=50, verbose_name='Real Interface')),
                ('interface', models.CharField(help_text='e.g. LAN', max_length=50)),
            ],
            options={
                'abstract': False,
            },
            bases=(models.Model, base.models.MethodsMixin),
        ),
        migrations.CreateModel(
            name='Module',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, help_text='e.g. Snort, Switch-1, etc. Allowed characters: letters (AaBbCc...), numbers (012...) and hyphen (-)', max_length=50, unique=True, validators=[base.models.validate_has_allowed_chars])),
                ('description', models.TextField(blank=True, null=True)),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('short_uuid', models.CharField(db_index=True, default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'abstract': False,
            },
            bases=(models.Model, base.models.MethodsMixin),
        ),
        migrations.CreateModel(
            name='PublicIP',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True, null=True)),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('short_uuid', models.CharField(db_index=True, default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('address', models.GenericIPAddressField(help_text='e.g. *********', protocol='IPv4', unique=True)),
            ],
            options={
                'verbose_name_plural': 'Public IPs',
                'abstract': False,
            },
            bases=(models.Model, base.models.MethodsMixin),
        ),
        migrations.CreateModel(
            name='Router',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, help_text='e.g. Snort, Switch-1, etc. Allowed characters: letters (AaBbCc...), numbers (012...) and hyphen (-)', max_length=50, unique=True, validators=[base.models.validate_has_allowed_chars])),
                ('address', models.GenericIPAddressField(blank=True, db_index=True, help_text='e.g. ***********', null=True, protocol='IPv4', unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('short_uuid', models.CharField(db_index=True, default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'abstract': False,
            },
            bases=(models.Model, base.models.MethodsMixin),
        ),
        migrations.CreateModel(
            name='RouterBoard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, help_text='e.g. Snort, Switch-1, etc. Allowed characters: letters (AaBbCc...), numbers (012...) and hyphen (-)', max_length=50, unique=True, validators=[base.models.validate_has_allowed_chars])),
                ('address', models.GenericIPAddressField(blank=True, db_index=True, help_text='e.g. ***********', null=True, protocol='IPv4', unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('short_uuid', models.CharField(db_index=True, default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'RouterBoards',
                'abstract': False,
            },
            bases=(models.Model, base.models.MethodsMixin),
        ),
        migrations.CreateModel(
            name='Sensor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, help_text='e.g. Snort, Switch-1, etc. Allowed characters: letters (AaBbCc...), numbers (012...) and hyphen (-)', max_length=50, unique=True, validators=[base.models.validate_has_allowed_chars])),
                ('address', models.GenericIPAddressField(blank=True, db_index=True, help_text='e.g. ***********', null=True, protocol='IPv4', unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('short_uuid', models.CharField(db_index=True, default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'abstract': False,
            },
            bases=(models.Model, base.models.MethodsMixin),
        ),
        migrations.CreateModel(
            name='StaticIP',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('address', models.GenericIPAddressField(blank=True, db_index=True, help_text='e.g. ***********', null=True, protocol='IPv4', unique=True)),
                ('virtual_address', models.GenericIPAddressField(blank=True, db_index=True, help_text='e.g. ***********', null=True, protocol='IPv4', unique=True, verbose_name='Virtual Address')),
                ('description', models.TextField(blank=True, null=True)),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('short_uuid', models.CharField(db_index=True, default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('computer_name', models.CharField(db_index=True, help_text='e.g. some-custom-name', max_length=50, unique=True, verbose_name='Computer Name')),
                ('real_name', models.CharField(help_text='e.g. Jane Austen', max_length=50, verbose_name='Real Name')),
            ],
            options={
                'verbose_name_plural': 'Static IPs',
                'abstract': False,
            },
            bases=(models.Model, base.models.MethodsMixin),
        ),
        migrations.CreateModel(
            name='Switch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, help_text='e.g. Snort, Switch-1, etc. Allowed characters: letters (AaBbCc...), numbers (012...) and hyphen (-)', max_length=50, unique=True, validators=[base.models.validate_has_allowed_chars])),
                ('address', models.GenericIPAddressField(blank=True, db_index=True, help_text='e.g. ***********', null=True, protocol='IPv4', unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('short_uuid', models.CharField(db_index=True, default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Switches',
                'abstract': False,
            },
            bases=(models.Model, base.models.MethodsMixin),
        ),
        migrations.CreateModel(
            name='VMware',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, help_text='e.g. Snort, Switch-1, etc. Allowed characters: letters (AaBbCc...), numbers (012...) and hyphen (-)', max_length=50, unique=True, validators=[base.models.validate_has_allowed_chars])),
                ('address', models.GenericIPAddressField(blank=True, db_index=True, help_text='e.g. ***********', null=True, protocol='IPv4', unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('short_uuid', models.CharField(db_index=True, default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'VMwares',
                'abstract': False,
            },
            bases=(models.Model, base.models.MethodsMixin),
        ),
        migrations.CreateModel(
            name='WindowsServer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, help_text='e.g. Snort, Switch-1, etc. Allowed characters: letters (AaBbCc...), numbers (012...) and hyphen (-)', max_length=50, unique=True, validators=[base.models.validate_has_allowed_chars])),
                ('address', models.GenericIPAddressField(blank=True, db_index=True, help_text='e.g. ***********', null=True, protocol='IPv4', unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('short_uuid', models.CharField(db_index=True, default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Windows Servers',
                'abstract': False,
            },
            bases=(models.Model, base.models.MethodsMixin),
        ),
        migrations.CreateModel(
            name='FirewallRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(blank=True, null=True)),
                ('active', models.BooleanField(db_index=True, default=True)),
                ('short_uuid', models.CharField(db_index=True, default=rahavard.utils.create_short_uuid, editable=False, max_length=10, unique=True, verbose_name='Short UUID')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True)),
                ('tracking_id', models.IntegerField(db_index=True, unique=True, verbose_name='Tracking ID')),
                ('name', models.CharField(help_text='e.g. APAC1200', max_length=20)),
                ('interface', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='firewallrule_interface_rel', to='base.interface')),
            ],
            options={
                'verbose_name_plural': 'Firewall Rules',
                'abstract': False,
            },
            bases=(models.Model, base.models.MethodsMixin),
        ),
        migrations.AddField(
            model_name='interface',
            name='sensors',
            field=models.ManyToManyField(blank=True, to='base.sensor'),
        ),
    ]
