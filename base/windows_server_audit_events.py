## sources:
## https://www.ultimatewindowssecurity.com/securitylog/encyclopedia/default.aspx
## https://learn.microsoft.com/en-us/windows-server/identity/ad-ds/plan/appendix-l--events-to-monitor
## https://ss64.com/ps/syntax-eventids.html
## https://gist.github.com/githubfoam/69eee155e4edafb2e679fb6ac5ea47d0
## https://github.com/PerryvandenHondel/windows-event-id-list-csv/blob/master/windows-event-id.csv

WINDOWS_SERVER_AUDIT_EVENTS = {
    '00': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The log was started',
    },
    '01': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The log was stopped',
    },
    '02': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The log was temporarily paused due to low disk space',
    },
    '10': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A new IP address was leased to a client',
    },
    '11': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A lease was renewed by a client',
    },
    '12': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A lease was released by a client',
    },
    '13': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'An IP address was found to be in use on the network',
    },
    '14': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': "A lease request could not be satisfied because the scope's address pool was exhausted",
    },
    '15': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A lease was denied',
    },
    '16': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A lease was deleted',
    },
    '17': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A lease was expired and DNS records for an expired leases have not been deleted',
    },
    '18': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A lease was expired and DNS records were deleted',
    },
    '20': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A BOOTP address was leased to a client',
    },
    '21': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A dynamic BOOTP address was leased to a client',
    },
    '22': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': "A BOOTP request could not be satisfied because the scope's address pool for BOOTP was exhausted",
    },
    '23': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A BOOTP IP address was deleted after checking to see it was not in use',
    },
    '24': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'IP address cleanup operation has began',
    },
    '25': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'IP address cleanup statistics',
    },
    '29': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The time provider NtpClient is configured to acquire time from one or more time sources; however none of the sources are currently accessible',
    },
    '30': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DNS update request to the named DNS server',
    },
    '31': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DNS update failed',
    },
    '32': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DNS update successful',
    },
    '33': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Packet dropped due to NAP policy',
    },
    '34': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DNS update request failed.as the DNS update request queue limit exceeded',
    },
    '35': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DNS update request failed',
    },
    '36': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Packet dropped because the server is in failover standby role or the hash of the client ID does not match',
    },
    '38': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The time provider NtpClient cannot reach or is currently receiving invalid time data',
    },
    '41': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The system has rebooted without cleanly shutting down first',
    },
    '47': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Time Provider NtpClient: No valid response received',
    },
    '50': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Unreachable domain',
    },
    '51': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Authorization succeeded',
    },
    '52': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Upgraded to a Windows Server 2003 operating system',
    },
    '53': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Cached Authorization',
    },
    '54': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Authorization failed',
    },
    '55': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Authorization (servicing)',
    },
    '56': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Authorization failure, stopped servicing',
    },
    '57': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Server found in domain',
    },
    '58': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Server could not find domain',
    },
    '59': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Network failure',
    },
    '60': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'No DC is DS Enabled',
    },
    '61': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Server found that belongs to DS domain',
    },
    '62': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Another server found',
    },
    '63': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Restarting rogue detection',
    },
    '64': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'No DHCP enabled interfaces',
    },
    '512': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Windows NT is starting up',
    },
    '513': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Windows is shutting down',
    },
    '514': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'An authentication package has been loaded by the Local Security Authority',
    },
    '515': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A trusted logon process has registered with the Local Security Authority',
    },
    '516': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Internal resources allocated for the queuing of audit messages have been exhausted, leading to the loss of some audits',
    },
    '517': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The audit log was cleared',
    },
    '518': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A notification package has been loaded by the Security Account Manager',
    },
    '519': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A process is using an invalid local procedure call (LPC) port',
    },
    '520': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The system time was changed',
    },
    '521': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Unable to log events to security log',
    },
    '528': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Successful Logon',
    },
    '529': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Logon Failure - Unknown user name or bad password',
    },
    '530': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Logon Failure - Account logon time restriction violation',
    },
    '531': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Logon Failure - Account currently disabled',
    },
    '532': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Logon Failure - The specified user account has expired',
    },
    '533': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Logon Failure - User not allowed to logon at this computer',
    },
    '534': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Logon Failure - The user has not been granted the requested logon type at this machine',
    },
    '535': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': "Logon Failure - The specified account's password has expired",
    },
    '536': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Logon Failure - The NetLogon component is not active',
    },
    '537': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Logon failure - The logon attempt failed for other reasons',
    },
    '538': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'User Logoff',
    },
    '539': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Logon Failure - Account locked out',
    },
    '540': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Successful Network Logon',
    },
    '551': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'User initiated logoff',
    },
    '552': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Logon attempt using explicit credentials',
    },
    '560': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Object Open',
    },
    '561': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Handle Allocated',
    },
    '562': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Handle Closed',
    },
    '563': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Object Open for Delete',
    },
    '564': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Object Deleted',
    },
    '565': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Object Open (Active Directory)',
    },
    '566': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Object Operation (W3 Active Directory)',
    },
    '567': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Object Access Attempt',
    },
    '576': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Special privileges assigned to new logon',
    },
    '577': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Privileged Service Called',
    },
    '578': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Privileged object operation',
    },
    '592': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A new process has been created',
    },
    '593': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A process has exited',
    },
    '594': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A handle to an object has been duplicated',
    },
    '595': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Indirect access to an object has been obtained',
    },
    '596': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Backup of data protection master key',
    },
    '600': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A process was assigned a primary token',
    },
    '601': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Attempt to install service',
    },
    '602': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Scheduled Task created',
    },
    '608': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'User Right Assigned',
    },
    '609': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'User Right Removed',
    },
    '610': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'New Trusted Domain',
    },
    '611': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Removing Trusted Domain',
    },
    '612': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Audit Policy Change',
    },
    '613': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'IPSec policy agent started',
    },
    '614': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'IPSec policy agent disabled',
    },
    '615': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'IPSEC PolicyAgent Service',
    },
    '616': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'IPSec policy agent encountered a potentially serious failure',
    },
    '617': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Kerberos Policy Changed',
    },
    '618': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Encrypted Data Recovery Policy Changed',
    },
    '619': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Quality of Service Policy Changed',
    },
    '620': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Trusted Domain Information Modified',
    },
    '621': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'System Security Access Granted',
    },
    '622': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'System Security Access Removed',
    },
    '623': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Per User Audit Policy was refreshed',
    },
    '624': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'User Account Created',
    },
    '625': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'User Account Type Changed',
    },
    '626': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'User Account Enabled',
    },
    '627': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Change Password Attempt',
    },
    '628': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'User Account password set',
    },
    '629': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'User Account Disabled',
    },
    '630': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'User Account Deleted',
    },
    '631': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Enabled Global Group Created',
    },
    '632': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Enabled Global Group Member Added',
    },
    '633': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Enabled Global Group Member Removed',
    },
    '634': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Enabled Global Group Deleted',
    },
    '635': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Enabled Local Group Created',
    },
    '636': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Enabled Local Group Member Added',
    },
    '637': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Enabled Local Group Member Removed',
    },
    '638': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Enabled Local Group Deleted',
    },
    '639': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Enabled Local Group Changed',
    },
    '640': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'General Account Database Change',
    },
    '641': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Enabled Global Group Changed',
    },
    '642': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'User Account Changed',
    },
    '643': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Domain Policy Changed',
    },
    '644': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'User Account Locked Out',
    },
    '645': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Computer Account Created',
    },
    '646': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Computer Account Changed',
    },
    '647': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Computer Account Deleted',
    },
    '648': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Disabled Local Group Created',
    },
    '649': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Disabled Local Group Changed',
    },
    '650': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Disabled Local Group Member Added',
    },
    '651': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Disabled Local Group Member Removed',
    },
    '652': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Disabled Local Group Deleted',
    },
    '653': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Disabled Global Group Created',
    },
    '654': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Disabled Global Group Changed',
    },
    '655': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Disabled Global Group Member Added',
    },
    '656': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Disabled Global Group Member Removed',
    },
    '657': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Disabled Global Group Deleted',
    },
    '658': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Enabled Universal Group Created',
    },
    '659': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Enabled Universal Group Changed',
    },
    '660': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Enabled Universal Group Member Added',
    },
    '661': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Enabled Universal Group Member Removed',
    },
    '662': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Enabled Universal Group Deleted',
    },
    '663': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Disabled Universal Group Created',
    },
    '664': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Disabled Universal Group Changed',
    },
    '665': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Disabled Universal Group Member Added',
    },
    '666': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Disabled Universal Group Member Removed',
    },
    '667': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Security Disabled Universal Group Deleted',
    },
    '668': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Group Type Changed',
    },
    '669': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Add SID History',
    },
    '670': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Add SID History',
    },
    '671': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'User Account Unlocked',
    },
    '672': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Authentication Ticket Granted',
    },
    '673': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Service Ticket Granted',
    },
    '674': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Ticket Granted Renewed',
    },
    '675': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Pre-authentication failed',
    },
    '676': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Authentication Ticket Request Failed',
    },
    '677': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Service Ticket Request Failed',
    },
    '678': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Account Mapped for Logon by',
    },
    '679': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The name: %2 could not be mapped for logon by: %1',
    },
    '680': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Account Used for Logon by',
    },
    '681': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The logon to account: %2 by: %1 from workstation: %3 failed',
    },
    '682': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Session reconnected to winstation',
    },
    '683': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Session disconnected from winstation',
    },
    '684': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Set ACLs of members in administrators groups',
    },
    '685': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Account Name Changed',
    },
    '686': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Password of the following user accessed',
    },
    '687': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Basic Application Group Created',
    },
    '688': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Basic Application Group Changed',
    },
    '689': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Basic Application Group Member Added',
    },
    '690': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Basic Application Group Member Removed',
    },
    '691': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Basic Application Group Non-Member Added',
    },
    '692': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Basic Application Group Non-Member Removed',
    },
    '693': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Basic Application Group Deleted',
    },
    '694': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'LDAP Query Group Created',
    },
    '695': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'LDAP Query Group Changed',
    },
    '696': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'LDAP Query Group Deleted',
    },
    '697': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Password Policy Checking API is called',
    },
    '806': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Per User Audit Policy was refreshed',
    },
    '807': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Per user auditing policy set for user',
    },
    '808': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A security event source has attempted to register',
    },
    '809': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A security event source has attempted to unregister',
    },
    '848': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The following policy was active when the Windows Firewall started',
    },
    '849': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'An application was listed as an exception when the Windows Firewall started',
    },
    '850': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A port was listed as an exception when the Windows Firewall started',
    },
    '851': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A change has been made to the Windows Firewall application exception list',
    },
    '852': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A change has been made to the Windows Firewall port exception list',
    },
    '853': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The Windows Firewall operational mode has changed',
    },
    '854': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The Windows Firewall logging settings have changed',
    },
    '855': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A Windows Firewall ICMP setting has changed',
    },
    '856': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The Windows Firewall setting to allow unicast responses to multicast/broadcast traffic has changed',
    },
    '857': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The Windows Firewall setting to allow remote administration, allowing port TCP 135 and DCOM/RPC, has changed',
    },
    '858': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Windows Firewall group policy settings have been applied',
    },
    '859': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The Windows Firewall group policy settings have been removed',
    },
    '860': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The Windows Firewall has switched the active policy profile',
    },
    '861': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The Windows Firewall has detected an application listening for incoming traffic',
    },
    '1030': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'the Group Policy settings cannot be read, the Group Policy object (GPO) is corrupted, or the computer is unable to access the domain controller',
    },
    '1058': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'the computer is unable to access the Sysvol share, which stores the Group Policy templates and scripts',
    },
    '1074': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The system has been shutdown properly by a user or process. The process X has initiated the restart / shutdown of computer on behalf of user Y for the following reason: Z. Indicates that an application or a user initiated a restart or shutdown',
    },
    '1076': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The reason supplied by user X for the last unexpected shutdown of this computer is: Y. Records when the first user with shutdown privileges logs on to the computer after an unexpected restart or shutdown and supplies a reason for the occurrence.Follows after Event ID 6008 and means that the first user with shutdown privileges logged on to the server after an unexpected restart or shutdown and specified the cause',
    },
    '1100': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The event logging service has shut down',
    },
    '1101': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Audit events have been dropped by the transport',
    },
    '1102': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Medium to High',
        'Summary': 'The audit log was cleared',
    },
    '1104': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The security Log is now full',
    },
    '1105': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Event log automatic backup',
    },
    '1108': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The event logging service encountered an error',
    },
    '1704': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'the GPO was successfully applied to the client computer',
    },
    '4190': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCP server failed to assign an address because there are no more available in the scope',
    },
    '4191': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCP server encountered an error while processing a DHCP request',
    },
    '4198': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCP lease has expired',
    },
    '4608': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'Windows is starting up',
    },
    '4609': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'Windows is shutting down',
    },
    '4610': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'An authentication package has been loaded by the Local Security Authority',
    },
    '4611': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'A trusted logon process has been registered with the Local Security Authority',
    },
    '4612': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'Internal resources allocated for the queuing of audit messages have been exhausted, leading to the loss of some audits',
    },
    '4614': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'A notification package has been loaded by the Security Account Manager',
    },
    '4615': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'Invalid use of LPC port',
    },
    '4616': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'The system time was changed',
    },
    '4618': {
        'Category': 'System',
        'Potential Criticality': 'High',
        'Summary': 'A monitored security event pattern has occurred',
    },
    '4621': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'Administrator recovered system from CrashOnAuditFail. Users who are not administrators will now be allowed to log on. Some auditable activity might not have been recorded',
    },
    '4622': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'A security package has been loaded by the Local Security Authority',
    },
    '4624': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'An account was successfully logged on',
    },
    '4625': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'An account failed to log on',
    },
    '4626': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'User/Device claims information',
    },
    '4627': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'Group membership information',
    },
    '4634': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'An account was logged off',
    },
    '4646': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'IKE DoS-prevention mode started',
    },
    '4647': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'User initiated logoff',
    },
    '4648': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'A logon was attempted using explicit credentials',
    },
    '4649': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'High',
        'Summary': 'A replay attack was detected. May be a harmless false positive due to misconfiguration error',
    },
    '4650': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'An IPsec Main Mode security association was established. Extended Mode was not enabled. Certificate authentication was not used',
    },
    '4651': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'An IPsec Main Mode security association was established. Extended Mode was not enabled. A certificate was used for authentication',
    },
    '4652': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'An IPsec Main Mode negotiation failed',
    },
    '4653': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'An IPsec Main Mode negotiation failed',
    },
    '4654': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'An IPsec Quick Mode negotiation failed',
    },
    '4655': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'An IPsec Main Mode security association ended',
    },
    '4656': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A handle to an object was requested',
    },
    '4657': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A registry value was modified',
    },
    '4658': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'The handle to an object was closed',
    },
    '4659': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A handle to an object was requested with intent to delete',
    },
    '4660': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'An object was deleted',
    },
    '4661': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A handle to an object was requested',
    },
    '4662': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'An operation was performed on an object',
    },
    '4663': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'An attempt was made to access an object',
    },
    '4664': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'An attempt was made to create a hard link',
    },
    '4665': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'An attempt was made to create an application client context',
    },
    '4666': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'An application attempted an operation',
    },
    '4667': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'An application client context was deleted',
    },
    '4668': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'An application was initialized',
    },
    '4670': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'Permissions on an object were changed',
    },
    '4671': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'An application attempted to access a blocked ordinal through the TBS',
    },
    '4672': {
        'Category': 'Privilege Use',
        'Potential Criticality': 'Low',
        'Summary': 'Special privileges assigned to new logon',
    },
    '4673': {
        'Category': 'Privilege Use',
        'Potential Criticality': 'Low',
        'Summary': 'A privileged service was called',
    },
    '4674': {
        'Category': 'Privilege Use',
        'Potential Criticality': 'Low',
        'Summary': 'An operation was attempted on a privileged object',
    },
    '4675': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Medium',
        'Summary': 'SIDs were filtered',
    },
    '4688': {
        'Category': 'Detailed Tracking',
        'Potential Criticality': 'Low',
        'Summary': 'A new process has been created',
    },
    '4689': {
        'Category': 'Detailed Tracking',
        'Potential Criticality': 'Low',
        'Summary': 'A process has exited',
    },
    '4690': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'An attempt was made to duplicate a handle to an object',
    },
    '4691': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Indirect access to an object was requested',
    },
    '4692': {
        'Category': 'Detailed Tracking',
        'Potential Criticality': 'Medium',
        'Summary': 'Backup of data protection master key was attempted',
    },
    '4693': {
        'Category': 'Detailed Tracking',
        'Potential Criticality': 'Medium',
        'Summary': 'Recovery of data protection master key was attempted',
    },
    '4694': {
        'Category': 'Detailed Tracking',
        'Potential Criticality': 'Low',
        'Summary': 'Protection of auditable protected data was attempted',
    },
    '4695': {
        'Category': 'Detailed Tracking',
        'Potential Criticality': 'Low',
        'Summary': 'Unprotection of auditable protected data was attempted',
    },
    '4696': {
        'Category': 'Detailed Tracking',
        'Potential Criticality': 'Low',
        'Summary': 'A primary token was assigned to process',
    },
    '4697': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'A service was installed in the system',
    },
    '4698': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A scheduled task was created',
    },
    '4699': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A scheduled task was deleted',
    },
    '4700': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A scheduled task was enabled',
    },
    '4701': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A scheduled task was disabled',
    },
    '4702': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A scheduled task was updated',
    },
    '4703': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A user right was adjusted',
    },
    '4704': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A user right was assigned',
    },
    '4705': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A user right was removed',
    },
    '4706': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Medium',
        'Summary': 'A new trust was created to a domain',
    },
    '4707': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A trust to a domain was removed',
    },
    '4709': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'IPsec Services was started',
    },
    '4710': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'IPsec Services was disabled',
    },
    '4711': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'May contain any one of the following: PAStore Engine applied locally cached copy of Active Directory storage IPsec policy on the computer. PAStore Engine applied Active Directory storage IPsec policy on the computer. PAStore Engine applied local registry storage IPsec policy on the computer. PAStore Engine failed to apply locally cached copy of Active Directory storage IPsec policy on the computer. PAStore Engine failed to apply Active Directory storage IPsec policy on the computer. PAStore Engine failed to apply local registry storage IPsec policy on the computer. PAStore Engine failed to apply some rules of the active IPsec policy on the computer. PAStore Engine failed to load directory storage IPsec policy on the computer. PAStore Engine loaded directory storage IPsec policy on the computer. PAStore Engine failed to load local storage IPsec policy on the computer. PAStore Engine loaded local storage IPsec policy on the computer. PAStore Engine polled for changes to the active IPsec policy and detected no changes',
    },
    '4712': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'IPsec Services encountered a potentially serious failure',
    },
    '4713': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Medium',
        'Summary': 'Kerberos policy was changed',
    },
    '4714': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Medium',
        'Summary': 'Encrypted data recovery policy was changed',
    },
    '4715': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Medium',
        'Summary': 'The audit policy (SACL) on an object was changed',
    },
    '4716': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Medium',
        'Summary': 'Trusted domain information was modified',
    },
    '4717': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'System security access was granted to an account',
    },
    '4718': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'System security access was removed from an account',
    },
    '4719': {
        'Category': 'Policy Change',
        'Potential Criticality': 'High',
        'Summary': 'System audit policy was changed',
    },
    '4720': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A user account was created',
    },
    '4722': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A user account was enabled',
    },
    '4723': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': "An attempt was made to change an account's password",
    },
    '4724': {
        'Category': 'Account Management',
        'Potential Criticality': 'Medium',
        'Summary': "An attempt was made to reset an account's password",
    },
    '4725': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A user account was disabled',
    },
    '4726': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A user account was deleted',
    },
    '4727': {
        'Category': 'Account Management',
        'Potential Criticality': 'Medium',
        'Summary': 'A security-enabled global group was created',
    },
    '4728': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A member was added to a security-enabled global group',
    },
    '4729': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A member was removed from a security-enabled global group',
    },
    '4730': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A security-enabled global group was deleted',
    },
    '4731': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A security-enabled local group was created',
    },
    '4732': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A member was added to a security-enabled local group',
    },
    '4733': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A member was removed from a security-enabled local group',
    },
    '4734': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A security-enabled local group was deleted',
    },
    '4735': {
        'Category': 'Account Management',
        'Potential Criticality': 'Medium',
        'Summary': 'A security-enabled local group was changed',
    },
    '4737': {
        'Category': 'Account Management',
        'Potential Criticality': 'Medium',
        'Summary': 'A security-enabled global group was changed',
    },
    '4738': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A user account was changed',
    },
    '4739': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Medium',
        'Summary': 'Domain Policy was changed',
    },
    '4740': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A user account was locked out',
    },
    '4741': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A computer account was created',
    },
    '4742': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A computer account was changed',
    },
    '4743': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A computer account was deleted',
    },
    '4744': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A security-disabled local group was created',
    },
    '4745': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A security-disabled local group was changed',
    },
    '4746': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A member was added to a security-disabled local group',
    },
    '4747': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A member was removed from a security-disabled local group',
    },
    '4748': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A security-disabled local group was deleted',
    },
    '4749': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A security-disabled global group was created',
    },
    '4750': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A security-disabled global group was changed',
    },
    '4751': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A member was added to a security-disabled global group',
    },
    '4752': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A member was removed from a security-disabled global group',
    },
    '4753': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A security-disabled global group was deleted',
    },
    '4754': {
        'Category': 'Account Management',
        'Potential Criticality': 'Medium',
        'Summary': 'A security-enabled universal group was created',
    },
    '4755': {
        'Category': 'Account Management',
        'Potential Criticality': 'Medium',
        'Summary': 'A security-enabled universal group was changed',
    },
    '4756': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A member was added to a security-enabled universal group',
    },
    '4757': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A member was removed from a security-enabled universal group',
    },
    '4758': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A security-enabled universal group was deleted',
    },
    '4759': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A security-disabled universal group was created',
    },
    '4760': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A security-disabled universal group was changed',
    },
    '4761': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A member was added to a security-disabled universal group',
    },
    '4762': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A member was removed from a security-disabled universal group',
    },
    '4763': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A security-disabled universal group was deleted',
    },
    '4764': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A group’s type was changed',
    },
    '4765': {
        'Category': 'Account Management',
        'Potential Criticality': 'High',
        'Summary': 'SID History was added to an account',
    },
    '4766': {
        'Category': 'Account Management',
        'Potential Criticality': 'High',
        'Summary': 'An attempt to add SID History to an account failed',
    },
    '4767': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A user account was unlocked',
    },
    '4768': {
        'Category': 'Account Logon',
        'Potential Criticality': 'Low',
        'Summary': 'A Kerberos authentication ticket (TGT) was requested',
    },
    '4769': {
        'Category': 'Account Logon',
        'Potential Criticality': 'Low',
        'Summary': 'A Kerberos service ticket was requested',
    },
    '4770': {
        'Category': 'Account Logon',
        'Potential Criticality': 'Low',
        'Summary': 'A Kerberos service ticket was renewed',
    },
    '4771': {
        'Category': 'Account Logon',
        'Potential Criticality': 'Low',
        'Summary': 'Kerberos pre-authentication failed',
    },
    '4772': {
        'Category': 'Account Logon',
        'Potential Criticality': 'Low',
        'Summary': 'A Kerberos authentication ticket request failed',
    },
    '4773': {
        'Category': 'Account Logon',
        'Potential Criticality': 'Low',
        'Summary': 'A Kerberos service ticket request failed',
    },
    '4774': {
        'Category': 'Account Logon',
        'Potential Criticality': 'Low',
        'Summary': 'An account was mapped for logon',
    },
    '4775': {
        'Category': 'Account Logon',
        'Potential Criticality': 'Low',
        'Summary': 'An account could not be mapped for logon',
    },
    '4776': {
        'Category': 'Account Logon',
        'Potential Criticality': 'Low',
        'Summary': 'The domain controller attempted to validate the credentials for an account',
    },
    '4777': {
        'Category': 'Account Logon',
        'Potential Criticality': 'Low',
        'Summary': 'The domain controller failed to validate the credentials for an account',
    },
    '4778': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'A session was reconnected to a Window Station',
    },
    '4779': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'A session was disconnected from a Window Station',
    },
    '4780': {
        'Category': 'Account Management',
        'Potential Criticality': 'Medium',
        'Summary': 'The ACL was set on accounts which are members of administrators groups',
    },
    '4781': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'The name of an account was changed',
    },
    '4782': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'The password hash an account was accessed',
    },
    '4783': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A basic application group was created',
    },
    '4784': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A basic application group was changed',
    },
    '4785': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A member was added to a basic application group',
    },
    '4786': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A member was removed from a basic application group',
    },
    '4787': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A non-member was added to a basic application group',
    },
    '4788': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A non-member was removed from a basic application group',
    },
    '4789': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A basic application group was deleted',
    },
    '4790': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'An LDAP query group was created',
    },
    '4791': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A basic application group was changed',
    },
    '4792': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'An LDAP query group was deleted',
    },
    '4793': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'The Password Policy Checking API was called',
    },
    '4794': {
        'Category': 'Account Management',
        'Potential Criticality': 'High',
        'Summary': 'An attempt was made to set the Directory Services Restore Mode',
    },
    '4797': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'An attempt was made to query the existence of a blank password for an account',
    },
    '4798': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': "A user's local group membership was enumerated",
    },
    '4799': {
        'Category': 'Account Management',
        'Potential Criticality': 'Low',
        'Summary': 'A security-enabled local group membership was enumerated',
    },
    '4800': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'The workstation was locked',
    },
    '4801': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'The workstation was unlocked',
    },
    '4802': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'The screen saver was invoked',
    },
    '4803': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'The screen saver was dismissed',
    },
    '4816': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'RPC detected an integrity violation while decrypting an incoming message',
    },
    '4817': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'Auditing settings on an object were changed',
    },
    '4818': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Proposed Central Access Policy does not grant the same access permissions as the current Central Access Policy',
    },
    '4819': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'Central Access Policies on the machine have been changed',
    },
    '4820': {
        'Category': 'Account Logon',
        'Potential Criticality': 'Low',
        'Summary': 'A Kerberos Ticket-granting-ticket (TGT) was denied because the device does not meet the access control restrictions',
    },
    '4821': {
        'Category': 'Account Logon',
        'Potential Criticality': 'Low',
        'Summary': 'A Kerberos service ticket was denied because the user, device, or both does not meet the access control restrictions',
    },
    '4822': {
        'Category': 'Account Logon',
        'Potential Criticality': 'Low',
        'Summary': 'NTLM authentication failed because the account was a member of the Protected User group',
    },
    '4823': {
        'Category': 'Account Logon',
        'Potential Criticality': 'Low',
        'Summary': 'NTLM authentication failed because access control restrictions are required',
    },
    '4824': {
        'Category': 'Account Logon',
        'Potential Criticality': 'Low',
        'Summary': 'Kerberos preauthentication by using DES or RC4 failed because the account was a member of the Protected User group',
    },
    '4825': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'A user was denied the access to Remote Desktop',
    },
    '4826': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'Boot Configuration Data loaded',
    },
    '4830': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'SID History was removed from an account',
    },
    '4864': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A namespace collision was detected',
    },
    '4865': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Medium',
        'Summary': 'A trusted forest information entry was added',
    },
    '4866': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Medium',
        'Summary': 'A trusted forest information entry was removed',
    },
    '4867': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Medium',
        'Summary': 'A trusted forest information entry was modified',
    },
    '4868': {
        'Category': 'Object Access',
        'Potential Criticality': 'Medium',
        'Summary': 'The certificate manager denied a pending certificate request',
    },
    '4869': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services received a resubmitted certificate request',
    },
    '4870': {
        'Category': 'Object Access',
        'Potential Criticality': 'Medium',
        'Summary': 'Certificate Services revoked a certificate',
    },
    '4871': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services received a request to publish the certificate revocation list (CRL)',
    },
    '4872': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services published the certificate revocation list (CRL)',
    },
    '4873': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A certificate request extension changed',
    },
    '4874': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'One or more certificate request attributes changed',
    },
    '4875': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services received a request to shut down',
    },
    '4876': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services backup started',
    },
    '4877': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services backup completed',
    },
    '4878': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services restore started',
    },
    '4879': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services restore completed',
    },
    '4880': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services started',
    },
    '4881': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services stopped',
    },
    '4882': {
        'Category': 'Object Access',
        'Potential Criticality': 'Medium',
        'Summary': 'The security permissions for Certificate Services changed',
    },
    '4883': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services retrieved an archived key',
    },
    '4884': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services imported a certificate into its database',
    },
    '4885': {
        'Category': 'Object Access',
        'Potential Criticality': 'Medium',
        'Summary': 'The audit filter for Certificate Services changed',
    },
    '4886': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services received a certificate request',
    },
    '4887': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services approved a certificate request and issued a certificate',
    },
    '4888': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services denied a certificate request',
    },
    '4889': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services set the status of a certificate request to pending',
    },
    '4890': {
        'Category': 'Object Access',
        'Potential Criticality': 'Medium',
        'Summary': 'The certificate manager settings for Certificate Services changed',
    },
    '4891': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A configuration entry changed in Certificate Services',
    },
    '4892': {
        'Category': 'Object Access',
        'Potential Criticality': 'Medium',
        'Summary': 'A property of Certificate Services changed',
    },
    '4893': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services archived a key',
    },
    '4894': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services imported and archived a key',
    },
    '4895': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services published the CA certificate to Active Directory Domain Services',
    },
    '4896': {
        'Category': 'Object Access',
        'Potential Criticality': 'Medium',
        'Summary': 'One or more rows have been deleted from the certificate database',
    },
    '4897': {
        'Category': 'Object Access',
        'Potential Criticality': 'High',
        'Summary': 'Role separation enabled',
    },
    '4898': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services loaded a template',
    },
    '4899': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A Certificate Services template was updated',
    },
    '4900': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Certificate Services template security was updated',
    },
    '4902': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'The Per-user audit policy table was created',
    },
    '4904': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'An attempt was made to register a security event source',
    },
    '4905': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'An attempt was made to unregister a security event source',
    },
    '4906': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Medium',
        'Summary': 'The CrashOnAuditFail value has changed',
    },
    '4907': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Medium',
        'Summary': 'Auditing settings on object were changed',
    },
    '4908': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Medium',
        'Summary': 'Special Groups Logon table modified',
    },
    '4909': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'The local policy settings for the TBS were changed',
    },
    '4910': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'The group policy settings for the TBS were changed',
    },
    '4911': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'Resource attributes of the object were changed',
    },
    '4912': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Medium',
        'Summary': 'Per User Audit Policy was changed',
    },
    '4913': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'Central Access Policy on the object was changed',
    },
    '4928': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'An Active Directory replica source naming context was established',
    },
    '4929': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'An Active Directory replica source naming context was removed',
    },
    '4930': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'An Active Directory replica source naming context was modified',
    },
    '4931': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'An Active Directory replica destination naming context was modified',
    },
    '4932': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'Synchronization of a replica of an Active Directory naming context has begun',
    },
    '4933': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'Synchronization of a replica of an Active Directory naming context has ended',
    },
    '4934': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'Attributes of an Active Directory object were replicated',
    },
    '4935': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'Replication failure begins',
    },
    '4936': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'Replication failure ends',
    },
    '4937': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'A lingering object was removed from a replica',
    },
    '4944': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'The following policy was active when the Windows Firewall started',
    },
    '4945': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A rule was listed when the Windows Firewall started',
    },
    '4946': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A change has been made to Windows Firewall exception list. A rule was added',
    },
    '4947': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A change has been made to Windows Firewall exception list. A rule was modified',
    },
    '4948': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A change has been made to Windows Firewall exception list. A rule was deleted',
    },
    '4949': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'Windows Firewall settings were restored to the default values',
    },
    '4950': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A Windows Firewall setting has changed',
    },
    '4951': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A rule has been ignored because its major version number was not recognized by Windows Firewall',
    },
    '4952': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'Parts of a rule have been ignored because its minor version number was not recognized by Windows Firewall. The other parts of the rule will be enforced',
    },
    '4953': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A rule has been ignored by Windows Firewall because it could not parse the rule',
    },
    '4954': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'Windows Firewall Group Policy settings have changed. The new settings have been applied',
    },
    '4956': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'Windows Firewall has changed the active profile',
    },
    '4957': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'Windows Firewall did not apply the following rule',
    },
    '4958': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'Windows Firewall did not apply the following rule because the rule referred to items not configured on this computer',
    },
    '4960': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'IPsec dropped an inbound packet that failed an integrity check. If this problem persists, it could indicate a network issue or that packets are being modified in transit to this computer. Verify that the packets sent from the remote computer are the same as those received by this computer. This error might also indicate interoperability problems with other IPsec implementations',
    },
    '4961': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'IPsec dropped an inbound packet that failed a replay check. If this problem persists, it could indicate a replay attack against this computer',
    },
    '4962': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'IPsec dropped an inbound packet that failed a replay check. The inbound packet had too low a sequence number to ensure it was not a replay',
    },
    '4963': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'IPsec dropped an inbound clear text packet that should have been secured. This is usually due to the remote computer changing its IPsec policy without informing this computer. This could also be a spoofing attack attempt',
    },
    '4964': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'High',
        'Summary': 'Special groups have been assigned to a new logon',
    },
    '4965': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'IPsec received a packet from a remote computer with an incorrect Security Parameter Index (SPI). This is usually caused by malfunctioning hardware that is corrupting packets. If these errors persist, verify that the packets sent from the remote computer are the same as those received by this computer. This error may also indicate interoperability problems with other IPsec implementations. In that case, if connectivity is not impeded, then these events can be ignored',
    },
    '4976': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Medium',
        'Summary': 'During Main Mode negotiation, IPsec received an invalid negotiation packet. If this problem persists, it could indicate a network issue or an attempt to modify or replay this negotiation',
    },
    '4977': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Medium',
        'Summary': 'During Quick Mode negotiation, IPsec received an invalid negotiation packet. If this problem persists, it could indicate a network issue or an attempt to modify or replay this negotiation',
    },
    '4978': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Medium',
        'Summary': 'During Extended Mode negotiation, IPsec received an invalid negotiation packet. If this problem persists, it could indicate a network issue or an attempt to modify or replay this negotiation',
    },
    '4979': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'IPsec Main Mode and Extended Mode security associations were established',
    },
    '4980': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'IPsec Main Mode and Extended Mode security associations were established',
    },
    '4981': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'IPsec Main Mode and Extended Mode security associations were established',
    },
    '4982': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'IPsec Main Mode and Extended Mode security associations were established',
    },
    '4983': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Medium',
        'Summary': 'An IPsec Extended Mode negotiation failed. The corresponding Main Mode security association has been deleted',
    },
    '4984': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Medium',
        'Summary': 'An IPsec Extended Mode negotiation failed. The corresponding Main Mode security association has been deleted',
    },
    '4985': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'The state of a transaction has changed',
    },
    '5024': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'The Windows Firewall Service has started successfully',
    },
    '5025': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'The Windows Firewall Service has been stopped',
    },
    '5027': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'The Windows Firewall Service was unable to retrieve the security policy from the local storage. The service will continue enforcing the current policy',
    },
    '5028': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'The Windows Firewall Service was unable to parse the new security policy. The service will continue with currently enforced policy',
    },
    '5029': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'The Windows Firewall Service failed to initialize the driver. The service will continue to enforce the current policy',
    },
    '5030': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'The Windows Firewall Service failed to start',
    },
    '5031': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'The Windows Firewall Service blocked an application from accepting incoming connections on the network',
    },
    '5032': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'Windows Firewall was unable to notify the user that it blocked an application from accepting incoming connections on the network',
    },
    '5033': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'The Windows Firewall Driver has started successfully',
    },
    '5034': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'The Windows Firewall Driver has been stopped',
    },
    '5035': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'The Windows Firewall Driver failed to start',
    },
    '5037': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'The Windows Firewall Driver detected critical runtime error. Terminating',
    },
    '5038': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'Code integrity determined that the image hash of a file is not valid. The file could be corrupt due to unauthorized modification or the invalid hash could indicate a potential disk device error',
    },
    '5039': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A registry key was virtualized',
    },
    '5040': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A change has been made to IPsec settings. An Authentication Set was added',
    },
    '5041': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A change has been made to IPsec settings. An Authentication Set was modified',
    },
    '5042': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A change has been made to IPsec settings. An Authentication Set was deleted',
    },
    '5043': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A change has been made to IPsec settings. A Connection Security Rule was added',
    },
    '5044': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A change has been made to IPsec settings. A Connection Security Rule was modified',
    },
    '5045': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A change has been made to IPsec settings. A Connection Security Rule was deleted',
    },
    '5046': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A change has been made to IPsec settings. A Crypto Set was added',
    },
    '5047': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A change has been made to IPsec settings. A Crypto Set was modified',
    },
    '5048': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A change has been made to IPsec settings. A Crypto Set was deleted',
    },
    '5049': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'An IPsec Security Association was deleted',
    },
    '5050': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'An attempt to programmatically disable the Windows Firewall was rejected because this API is not supported on Windows Vista',
    },
    '5051': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A file was virtualized',
    },
    '5056': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'A cryptographic self test was performed',
    },
    '5057': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'A cryptographic primitive operation failed',
    },
    '5058': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'Key file operation',
    },
    '5059': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'Key migration operation',
    },
    '5060': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'Verification operation failed',
    },
    '5061': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'Cryptographic operation',
    },
    '5062': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'A kernel-mode cryptographic self test was performed',
    },
    '5063': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A cryptographic provider operation was attempted',
    },
    '5064': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A cryptographic context operation was attempted',
    },
    '5065': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A cryptographic context modification was attempted',
    },
    '5066': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A cryptographic function operation was attempted',
    },
    '5067': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A cryptographic function modification was attempted',
    },
    '5068': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A cryptographic function provider operation was attempted',
    },
    '5069': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A cryptographic function property operation was attempted',
    },
    '5070': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A cryptographic function property modification was attempted',
    },
    '5071': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'Key access denied by Microsoft key distribution service',
    },
    '5120': {
        'Category': 'Object Access',
        'Potential Criticality': 'Medium',
        'Summary': 'OCSP Responder Service Started',
    },
    '5121': {
        'Category': 'Object Access',
        'Potential Criticality': 'Medium',
        'Summary': 'OCSP Responder Service Stopped',
    },
    '5122': {
        'Category': 'Object Access',
        'Potential Criticality': 'Medium',
        'Summary': 'A Configuration entry changed in the OCSP Responder Service',
    },
    '5123': {
        'Category': 'Object Access',
        'Potential Criticality': 'Medium',
        'Summary': 'A configuration entry changed in the OCSP Responder Service',
    },
    '5124': {
        'Category': 'Object Access',
        'Potential Criticality': 'High',
        'Summary': 'A security setting was updated on the OCSP Responder Service',
    },
    '5125': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A request was submitted to the OCSP Responder Service',
    },
    '5126': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Signing Certificate was automatically updated by the OCSP Responder Service',
    },
    '5127': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'The OCSP Revocation Provider successfully updated the revocation information',
    },
    '5136': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'A directory service object was modified',
    },
    '5137': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'A directory service object was created',
    },
    '5138': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'A directory service object was undeleted',
    },
    '5139': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'A directory service object was moved',
    },
    '5140': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A network share object was accessed',
    },
    '5141': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'A directory service object was deleted',
    },
    '5142': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A network share object was added',
    },
    '5143': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A network share object was modified',
    },
    '5144': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A network share object was deleted',
    },
    '5145': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A network share object was checked to see whether the client can be granted desired access',
    },
    '5146': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'The Windows Filtering Platform has blocked a packet',
    },
    '5147': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A more restrictive Windows Filtering Platform filter has blocked a packet',
    },
    '5148': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'The Windows Filtering Platform has detected a DoS attack and entered a defensive mode; packets associated with this attack will be discarded',
    },
    '5149': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'The DoS attack has subsided and normal processing is being resumed',
    },
    '5150': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'The Windows Filtering Platform has blocked a packet',
    },
    '5151': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A more restrictive Windows Filtering Platform filter has blocked a packet',
    },
    '5152': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'The Windows Filtering Platform blocked a packet',
    },
    '5153': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'A more restrictive Windows Filtering Platform filter has blocked a packet',
    },
    '5154': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'The Windows Filtering Platform has permitted an application or service to listen on a port for incoming connections',
    },
    '5155': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'The Windows Filtering Platform has blocked an application or service from listening on a port for incoming connections',
    },
    '5156': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'The Windows Filtering Platform has allowed a connection',
    },
    '5157': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'The Windows Filtering Platform has blocked a connection',
    },
    '5158': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'The Windows Filtering Platform has permitted a bind to a local port',
    },
    '5159': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'The Windows Filtering Platform has blocked a bind to a local port',
    },
    '5168': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'Spn check for SMB/SMB2 failed',
    },
    '5169': {
        'Category': 'DS Access',
        'Potential Criticality': 'Low',
        'Summary': 'A directory service object was modified',
    },
    '5170': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'A directory service object was modified during a background cleanup task',
    },
    '5376': {
        'Category': 'Account Management',
        'Potential Criticality': 'Medium',
        'Summary': 'Credential Manager credentials were backed up',
    },
    '5377': {
        'Category': 'Account Management',
        'Potential Criticality': 'Medium',
        'Summary': 'Credential Manager credentials were restored from a backup',
    },
    '5378': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'The requested credentials delegation was disallowed by policy',
    },
    '5379': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Credential Manager credentials were read',
    },
    '5380': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Vault Find Credential',
    },
    '5381': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Vault credentials were read',
    },
    '5382': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Vault credentials were read',
    },
    '5440': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'The following callout was present when the Windows Filtering Platform Base Filtering Engine started',
    },
    '5441': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'The following filter was present when the Windows Filtering Platform Base Filtering Engine started',
    },
    '5442': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'The following provider was present when the Windows Filtering Platform Base Filtering Engine started',
    },
    '5443': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'The following provider context was present when the Windows Filtering Platform Base Filtering Engine started',
    },
    '5444': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'The following sub-layer was present when the Windows Filtering Platform Base Filtering Engine started',
    },
    '5446': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A Windows Filtering Platform callout has been changed',
    },
    '5447': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A Windows Filtering Platform filter has been changed',
    },
    '5448': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A Windows Filtering Platform provider has been changed',
    },
    '5449': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A Windows Filtering Platform provider context has been changed',
    },
    '5450': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'A Windows Filtering Platform sub-layer has been changed',
    },
    '5451': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'An IPsec Quick Mode security association was established',
    },
    '5452': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'An IPsec Quick Mode security association ended',
    },
    '5453': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Medium',
        'Summary': 'An IPsec negotiation with a remote computer failed because the IKE and AuthIP IPsec Keying Modules (IKEEXT) service is not started',
    },
    '5456': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine applied Active Directory storage IPsec policy on the computer',
    },
    '5457': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine failed to apply Active Directory storage IPsec policy on the computer',
    },
    '5458': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine applied locally cached copy of Active Directory storage IPsec policy on the computer',
    },
    '5459': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine failed to apply locally cached copy of Active Directory storage IPsec policy on the computer',
    },
    '5460': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine applied local registry storage IPsec policy on the computer',
    },
    '5461': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine failed to apply local registry storage IPsec policy on the computer',
    },
    '5462': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine failed to apply some rules of the active IPsec policy on the computer. Use the IP Security Monitor snap-in to diagnose the problem',
    },
    '5463': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine polled for changes to the active IPsec policy and detected no changes',
    },
    '5464': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine polled for changes to the active IPsec policy, detected changes, and applied them to IPsec Services',
    },
    '5465': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine received a control for forced reloading of IPsec policy and processed the control successfully',
    },
    '5466': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine polled for changes to the Active Directory IPsec policy, determined that Active Directory cannot be reached, and will use the cached copy of the Active Directory IPsec policy instead. Any changes made to the Active Directory IPsec policy since the last poll could not be applied',
    },
    '5467': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine polled for changes to the Active Directory IPsec policy, determined that Active Directory can be reached, and found no changes to the policy. The cached copy of the Active Directory IPsec policy is no longer being used',
    },
    '5468': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine polled for changes to the Active Directory IPsec policy, determined that Active Directory can be reached, found changes to the policy, and applied those changes. The cached copy of the Active Directory IPsec policy is no longer being used',
    },
    '5471': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine loaded local storage IPsec policy on the computer',
    },
    '5472': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine failed to load local storage IPsec policy on the computer',
    },
    '5473': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine loaded directory storage IPsec policy on the computer',
    },
    '5474': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine failed to load directory storage IPsec policy on the computer',
    },
    '5477': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'PAStore Engine failed to add quick mode filter',
    },
    '5478': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'IPsec Services has started successfully',
    },
    '5479': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'IPsec Services has been shut down successfully. The shutdown of IPsec Services can put the computer at greater risk of network attack or expose the computer to potential security risks',
    },
    '5480': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'IPsec Services failed to get the complete list of network interfaces on the computer. This poses a potential security risk because some of the network interfaces may not get the protection provided by the applied IPsec filters. Use the IP Security Monitor snap-in to diagnose the problem',
    },
    '5483': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'IPsec Services failed to initialize RPC server. IPsec Services could not be started',
    },
    '5484': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'IPsec Services has experienced a critical failure and has been shut down. The shutdown of IPsec Services can put the computer at greater risk of network attack or expose the computer to potential security risks',
    },
    '5485': {
        'Category': 'System',
        'Potential Criticality': 'Medium',
        'Summary': 'IPsec Services failed to process some IPsec filters on a plug-and-play event for network interfaces. This poses a potential security risk because some of the network interfaces may not get the protection provided by the applied IPsec filters. Use the IP Security Monitor snap-in to diagnose the problem',
    },
    '5632': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'A request was made to authenticate to a wireless network',
    },
    '5633': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'A request was made to authenticate to a wired network',
    },
    '5712': {
        'Category': 'Detailed Tracking',
        'Potential Criticality': 'Low',
        'Summary': 'A Remote Procedure Call (RPC) was attempted',
    },
    '5827': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Medium',
        'Summary': 'The Netlogon service denied a vulnerable Netlogon secure channel connection from a machine account',
    },
    '5828': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Medium',
        'Summary': 'The Netlogon service denied a vulnerable Netlogon secure channel connection using a trust account',
    },
    '5888': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'An object in the COM+ Catalog was modified',
    },
    '5889': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'An object was deleted from the COM+ Catalog',
    },
    '5890': {
        'Category': 'Object Access',
        'Potential Criticality': 'Low',
        'Summary': 'An object was added to the COM+ Catalog',
    },
    '6005': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The event log service was started. Indicates the system startup',
    },
    '6006': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The event log service was stopped. Indicates the proper system shutdown',
    },
    '6008': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Low',
        'Summary': 'The previous system shutdown was unexpected',
    },
    '6009': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Indicates the Windows product name, version, build number, service pack number, and operating system type detected at boot time',
    },
    '6013': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'The system uptime in seconds',
    },
    '6144': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Low',
        'Summary': 'Security policy in the group policy objects has been applied successfully',
    },
    '6145': {
        'Category': 'Policy Change',
        'Potential Criticality': 'Medium',
        'Summary': 'One or more errors occurred while processing security policy in the group policy objects',
    },
    '6272': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Low',
        'Summary': 'Network Policy Server granted access to a user',
    },
    '6273': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Medium',
        'Summary': 'Network Policy Server denied access to a user',
    },
    '6274': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Medium',
        'Summary': 'Network Policy Server discarded the request for a user',
    },
    '6275': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Medium',
        'Summary': 'Network Policy Server discarded the accounting request for a user',
    },
    '6276': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Medium',
        'Summary': 'Network Policy Server quarantined a user',
    },
    '6277': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Medium',
        'Summary': 'Network Policy Server granted access to a user but put it on probation because the host did not meet the defined health policy',
    },
    '6278': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Medium',
        'Summary': 'Network Policy Server granted full access to a user because the host met the defined health policy',
    },
    '6279': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Medium',
        'Summary': 'Network Policy Server locked the user account due to repeated failed authentication attempts',
    },
    '6280': {
        'Category': 'Logon/Logoff',
        'Potential Criticality': 'Medium',
        'Summary': 'Network Policy Server unlocked the user account',
    },
    '6281': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'Code Integrity determined that the page hashes of an image file are not valid. The file could be improperly signed without page hashes or corrupt due to unauthorized modification. The invalid hashes could indicate a potential disk device error',
    },
    '6400': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'BranchCache: Received an incorrectly formatted response while discovering availability of content',
    },
    '6401': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'BranchCache: Received invalid data from a peer. Data discarded',
    },
    '6402': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'BranchCache: The message to the hosted cache offering it data is incorrectly formatted',
    },
    '6403': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': "BranchCache: The hosted cache sent an incorrectly formatted response to the client's message to offer it data",
    },
    '6404': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'BranchCache: Hosted cache could not be authenticated using the provisioned SSL certificate',
    },
    '6405': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'BranchCache: %2 instance(s) of event id %1 occurred',
    },
    '6406': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': '%1 registered to Windows Firewall to control filtering for the following: %2',
    },
    '6407': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': '%1',
    },
    '6408': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'Registered product %1 failed and Windows Firewall is now controlling the filtering for %2',
    },
    '6409': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'BranchCache: A service connection point object could not be parsed',
    },
    '6410': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'Code integrity determined that a file does not meet the security requirements to load into a process. This could be due to the use of shared sections or other issues',
    },
    '6416': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'A new external device was recognized by the system',
    },
    '6417': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'The FIPS mode crypto selftests succeeded',
    },
    '6418': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'The FIPS mode crypto selftests failed',
    },
    '6419': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'A request was made to disable a device',
    },
    '6420': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'A device was disabled',
    },
    '6421': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'A request was made to enable a device',
    },
    '6422': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'A device was enabled',
    },
    '6423': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'The installation of this device is forbidden by system policy',
    },
    '6424': {
        'Category': 'System',
        'Potential Criticality': 'Low',
        'Summary': 'The installation of this device was allowed, after having previously been forbidden by policy',
    },
    '7009': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'a service timeout has occurred',
    },
    '7011': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'a service did not respond within the specified time',
    },
    '7023': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'the service terminated with the following error: The service terminated with the following service-specific error: Incorrect function',
    },
    '7024': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'the service terminated with service-specific error',
    },
    '7031': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'a service has stopped unexpectedly',
    },
    '7035': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'a service was successfully sent a start/Stop control',
    },
    '7036': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'a service has entered the running or stopped state',
    },
    '8191': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'Highest System-Defined Audit Message Value',
    },
    '11000': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Solicit',
    },
    '11001': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Advertise',
    },
    '11002': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Request',
    },
    '11003': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Confirm',
    },
    '11004': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Renew',
    },
    '11005': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Rebind',
    },
    '11006': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Decline',
    },
    '11007': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Release',
    },
    '11008': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Information Request',
    },
    '11009': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Scope Full',
    },
    '11010': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Started',
    },
    '11011': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Stopped',
    },
    '11012': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Audit log paused',
    },
    '11013': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Log File',
    },
    '11014': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Bad Address',
    },
    '11015': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Address is already in use',
    },
    '11016': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Client deleted',
    },
    '11017': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 DNS record not deleted',
    },
    '11018': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Expired',
    },
    '11019': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Leases Expired and Leases Deleted',
    },
    '11020': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Database cleanup begin',
    },
    '11021': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 Database cleanup end',
    },
    '11022': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DNS IPv6 Update Request',
    },
    '11023': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DNS IPv6 Update Failed',
    },
    '11024': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DNS IPv6 Update Successful',
    },
    '11028': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DNS IPv6 update request failed as the DNS update request queue limit exceeded',
    },
    '11029': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DNS IPv6 update request failed',
    },
    '11030': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 stateless client records purged',
    },
    '11031': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPv6 stateless client record is purged as the purge interval has expired for this client record',
    },
    '11032': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Miscellaneous',
        'Summary': 'DHCPV6 Information Request from IPV6 Stateless Client',
    },
    '24577': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Low',
        'Summary': 'Encryption of volume started',
    },
    '24578': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Low',
        'Summary': 'Encryption of volume stopped',
    },
    '24579': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Low',
        'Summary': 'Encryption of volume completed',
    },
    '24580': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Low',
        'Summary': 'Decryption of volume started',
    },
    '24581': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Low',
        'Summary': 'Decryption of volume stopped',
    },
    '24582': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Low',
        'Summary': 'Decryption of volume completed',
    },
    '24583': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Low',
        'Summary': 'Conversion worker thread for volume started',
    },
    '24584': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Low',
        'Summary': 'Conversion worker thread for volume temporarily stopped',
    },
    '24586': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Medium',
        'Summary': 'An error was encountered converting volume',
    },
    '24588': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Low',
        'Summary': 'The conversion operation on volume %2 encountered a bad sector error. Please validate the data on this volume',
    },
    '24592': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Medium',
        'Summary': 'An attempt to automatically restart conversion on volume %2 failed',
    },
    '24593': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Medium',
        'Summary': 'Metadata write: Volume %2 returning errors while trying to modify metadata. If failures continue, decrypt volume',
    },
    '24594': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Medium',
        'Summary': 'Metadata rebuild: An attempt to write a copy of metadata on volume %2 failed and may appear as disk corruption. If failures continue, decrypt volume',
    },
    '24595': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Low',
        'Summary': 'Volume %2 contains bad clusters. These clusters will be skipped during conversion',
    },
    '24621': {
        'Category': 'Miscellaneous',
        'Potential Criticality': 'Low',
        'Summary': 'Initial state check: Rolling volume conversion transaction on %2',
    },
}
